!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(require("JSZip")):"function"==typeof define&&define.amd?define(["JSZip"],e):"object"==typeof exports?exports.ePub=e(require("JSZip")):t.ePub=e(t.JSZip)}(window,(function(t){return function(t){var e={};function n(i){if(e[i])return e[i].exports;var r=e[i]={i:i,l:!1,exports:{}};return t[i].call(r.exports,r,r.exports,n),r.l=!0,r.exports}return n.m=t,n.c=e,n.d=function(t,e,i){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:i})},n.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var i=Object.create(null);if(n.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var r in t)n.d(i,r,function(e){return t[e]}.bind(null,r));return i},n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="/dist/",n(n.s=169)}([function(t,e,n){"use strict";n.r(e),n.d(e,"requestAnimationFrame",(function(){return o})),n.d(e,"uuid",(function(){return a})),n.d(e,"documentHeight",(function(){return u})),n.d(e,"isElement",(function(){return c})),n.d(e,"isNumber",(function(){return l})),n.d(e,"isFloat",(function(){return h})),n.d(e,"prefixed",(function(){return f})),n.d(e,"defaults",(function(){return d})),n.d(e,"extend",(function(){return p})),n.d(e,"insert",(function(){return v})),n.d(e,"locationOf",(function(){return g})),n.d(e,"indexOfSorted",(function(){return m})),n.d(e,"bounds",(function(){return y})),n.d(e,"borders",(function(){return b})),n.d(e,"nodeBounds",(function(){return w})),n.d(e,"windowBounds",(function(){return x})),n.d(e,"indexOfNode",(function(){return k})),n.d(e,"indexOfTextNode",(function(){return E})),n.d(e,"indexOfElementNode",(function(){return S})),n.d(e,"isXml",(function(){return O})),n.d(e,"createBlob",(function(){return T})),n.d(e,"createBlobUrl",(function(){return _})),n.d(e,"revokeBlobUrl",(function(){return N})),n.d(e,"createBase64Url",(function(){return C})),n.d(e,"type",(function(){return R})),n.d(e,"parse",(function(){return I})),n.d(e,"qs",(function(){return A})),n.d(e,"qsa",(function(){return j})),n.d(e,"qsp",(function(){return L})),n.d(e,"sprint",(function(){return P})),n.d(e,"treeWalker",(function(){return D})),n.d(e,"walk",(function(){return M})),n.d(e,"blob2base64",(function(){return z})),n.d(e,"defer",(function(){return B})),n.d(e,"querySelectorByType",(function(){return q})),n.d(e,"findChildren",(function(){return U})),n.d(e,"parents",(function(){return F})),n.d(e,"filterChildren",(function(){return W})),n.d(e,"getParentByTagName",(function(){return H})),n.d(e,"RangeObject",(function(){return V}));n(14),n(10),n(18),n(22),n(53),n(44),n(15),n(62),n(34),n(35),n(11),n(212),n(154),n(95),n(66),n(27),n(213),n(63);var i=n(98);function r(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}var o="undefined"!=typeof window&&(window.requestAnimationFrame||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame||window.msRequestAnimationFrame),s="undefined"!=typeof URL?URL:"undefined"!=typeof window?window.URL||window.webkitURL||window.mozURL:void 0;function a(){var t=(new Date).getTime();return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var n=(t+16*Math.random())%16|0;return t=Math.floor(t/16),("x"==e?n:7&n|8).toString(16)}))}function u(){return Math.max(document.documentElement.clientHeight,document.body.scrollHeight,document.documentElement.scrollHeight,document.body.offsetHeight,document.documentElement.offsetHeight)}function c(t){return!(!t||1!=t.nodeType)}function l(t){return!isNaN(parseFloat(t))&&isFinite(t)}function h(t){var e=parseFloat(t);return!1!==l(t)&&("string"==typeof t&&t.indexOf(".")>-1||Math.floor(e)!==e)}function f(t){var e=["-webkit-","-webkit-","-moz-","-o-","-ms-"],n=t.toLowerCase(),i=["Webkit","webkit","Moz","O","ms"].length;if("undefined"==typeof document||void 0!==document.body.style[n])return t;for(var r=0;r<i;r++)if(void 0!==document.body.style[e[r]+n])return e[r]+n;return t}function d(t){for(var e=1,n=arguments.length;e<n;e++){var i=arguments[e];for(var r in i)void 0===t[r]&&(t[r]=i[r])}return t}function p(t){var e=[].slice.call(arguments,1);return e.forEach((function(e){e&&Object.getOwnPropertyNames(e).forEach((function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(e,n))}))})),t}function v(t,e,n){var i=g(t,e,n);return e.splice(i,0,t),i}function g(t,e,n,i,r){var o,s=i||0,a=r||e.length,u=parseInt(s+(a-s)/2);return n||(n=function(t,e){return t>e?1:t<e?-1:t==e?0:void 0}),a-s<=0?u:(o=n(e[u],t),a-s==1?o>=0?u:u+1:0===o?u:-1===o?g(t,e,n,u,a):g(t,e,n,s,u))}function m(t,e,n,i,r){var o,s=i||0,a=r||e.length,u=parseInt(s+(a-s)/2);return n||(n=function(t,e){return t>e?1:t<e?-1:t==e?0:void 0}),a-s<=0?-1:(o=n(e[u],t),a-s==1?0===o?u:-1:0===o?u:-1===o?m(t,e,n,u,a):m(t,e,n,s,u))}function y(t){var e=window.getComputedStyle(t),n=0,i=0;return["width","paddingRight","paddingLeft","marginRight","marginLeft","borderRightWidth","borderLeftWidth"].forEach((function(t){n+=parseFloat(e[t])||0})),["height","paddingTop","paddingBottom","marginTop","marginBottom","borderTopWidth","borderBottomWidth"].forEach((function(t){i+=parseFloat(e[t])||0})),{height:i,width:n}}function b(t){var e=window.getComputedStyle(t),n=0,i=0;return["paddingRight","paddingLeft","marginRight","marginLeft","borderRightWidth","borderLeftWidth"].forEach((function(t){n+=parseFloat(e[t])||0})),["paddingTop","paddingBottom","marginTop","marginBottom","borderTopWidth","borderBottomWidth"].forEach((function(t){i+=parseFloat(e[t])||0})),{height:i,width:n}}function w(t){var e,n=t.ownerDocument;if(t.nodeType==Node.TEXT_NODE){var i=n.createRange();i.selectNodeContents(t),e=i.getBoundingClientRect()}else e=t.getBoundingClientRect();return e}function x(){var t=window.innerWidth,e=window.innerHeight;return{top:0,left:0,right:t,bottom:e,width:t,height:e}}function k(t,e){for(var n,i=t.parentNode.childNodes,r=-1,o=0;o<i.length&&((n=i[o]).nodeType===e&&r++,n!=t);o++);return r}function E(t){return k(t,3)}function S(t){return k(t,1)}function O(t){return["xml","opf","ncx"].indexOf(t)>-1}function T(t,e){return new Blob([t],{type:e})}function _(t,e){var n=T(t,e);return s.createObjectURL(n)}function N(t){return s.revokeObjectURL(t)}function C(t,e){if("string"==typeof t)return"data:"+e+";base64,"+btoa(t)}function R(t){return Object.prototype.toString.call(t).slice(8,-1)}function I(t,e,n){var r;return r="undefined"==typeof DOMParser||n?i.DOMParser:DOMParser,65279===t.charCodeAt(0)&&(t=t.slice(1)),(new r).parseFromString(t,e)}function A(t,e){var n;if(!t)throw new Error("No Element Provided");return void 0!==t.querySelector?t.querySelector(e):(n=t.getElementsByTagName(e)).length?n[0]:void 0}function j(t,e){return void 0!==t.querySelector?t.querySelectorAll(e):t.getElementsByTagName(e)}function L(t,e,n){var i,r;if(void 0!==t.querySelector){for(var o in e+="[",n)e+=o+"~='"+n[o]+"'";return e+="]",t.querySelector(e)}if(i=t.getElementsByTagName(e),r=Array.prototype.slice.call(i,0).filter((function(t){for(var e in n)if(t.getAttribute(e)===n[e])return!0;return!1})))return r[0]}function P(t,e){void 0!==(t.ownerDocument||t).createTreeWalker?D(t,e,NodeFilter.SHOW_TEXT):M(t,(function(t){t&&3===t.nodeType&&e(t)}))}function D(t,e,n){for(var i,r=document.createTreeWalker(t,n,null,!1);i=r.nextNode();)e(i)}function M(t,e){if(e(t))return!0;if(t=t.firstChild)do{if(M(t,e))return!0;t=t.nextSibling}while(t)}function z(t){return new Promise((function(e,n){var i=new FileReader;i.readAsDataURL(t),i.onloadend=function(){e(i.result)}}))}function B(){var t=this;this.resolve=null,this.reject=null,this.id=a(),this.promise=new Promise((function(e,n){t.resolve=e,t.reject=n})),Object.freeze(this)}function q(t,e,n){var i;if(void 0!==t.querySelector&&(i=t.querySelector("".concat(e,'[*|type="').concat(n,'"]'))),i&&0!==i.length)return i;i=j(t,e);for(var r=0;r<i.length;r++)if(i[r].getAttributeNS("http://www.idpf.org/2007/ops","type")===n||i[r].getAttribute("epub:type")===n)return i[r]}function U(t){for(var e=[],n=t.childNodes,i=0;i<n.length;i++){var r=n[i];1===r.nodeType&&e.push(r)}return e}function F(t){for(var e=[t];t;t=t.parentNode)e.unshift(t);return e}function W(t,e,n){for(var i=[],r=t.childNodes,o=0;o<r.length;o++){var s=r[o];if(1===s.nodeType&&s.nodeName.toLowerCase()===e){if(n)return s;i.push(s)}}if(!n)return i}function H(t,e){var n;if(null!==t&&""!==e)for(n=t.parentNode;1===n.nodeType;){if(n.tagName.toLowerCase()===e)return n;n=n.parentNode}}var V=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.collapsed=!1,this.commonAncestorContainer=void 0,this.endContainer=void 0,this.endOffset=void 0,this.startContainer=void 0,this.startOffset=void 0}var e,n,i;return e=t,(n=[{key:"setStart",value:function(t,e){this.startContainer=t,this.startOffset=e,this.endContainer?this.commonAncestorContainer=this._commonAncestorContainer():this.collapse(!0),this._checkCollapsed()}},{key:"setEnd",value:function(t,e){this.endContainer=t,this.endOffset=e,this.startContainer?(this.collapsed=!1,this.commonAncestorContainer=this._commonAncestorContainer()):this.collapse(!1),this._checkCollapsed()}},{key:"collapse",value:function(t){this.collapsed=!0,t?(this.endContainer=this.startContainer,this.endOffset=this.startOffset,this.commonAncestorContainer=this.startContainer.parentNode):(this.startContainer=this.endContainer,this.startOffset=this.endOffset,this.commonAncestorContainer=this.endOffset.parentNode)}},{key:"selectNode",value:function(t){var e=t.parentNode,n=Array.prototype.indexOf.call(e.childNodes,t);this.setStart(e,n),this.setEnd(e,n+1)}},{key:"selectNodeContents",value:function(t){t.childNodes[t.childNodes-1];var e=3===t.nodeType?t.textContent.length:parent.childNodes.length;this.setStart(t,0),this.setEnd(t,e)}},{key:"_commonAncestorContainer",value:function(t,e){var n=F(t||this.startContainer),i=F(e||this.endContainer);if(n[0]==i[0])for(var r=0;r<n.length;r++)if(n[r]!=i[r])return n[r-1]}},{key:"_checkCollapsed",value:function(){this.startContainer===this.endContainer&&this.startOffset===this.endOffset?this.collapsed=!0:this.collapsed=!1}},{key:"toString",value:function(){}}])&&r(e.prototype,n),i&&r(e,i),t}()},function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"a",(function(){return r})),n.d(e,"c",(function(){return o}));var i="0.3",r=["keydown","keyup","keypressed","mouseup","mousedown","mousemove","click","touchend","touchstart","touchmove"],o={BOOK:{OPEN_FAILED:"openFailed"},CONTENTS:{EXPAND:"expand",RESIZE:"resize",SELECTED:"selected",SELECTED_RANGE:"selectedRange",LINK_CLICKED:"linkClicked"},LOCATIONS:{CHANGED:"changed"},MANAGERS:{RESIZE:"resize",RESIZED:"resized",ORIENTATION_CHANGE:"orientationchange",ADDED:"added",SCROLL:"scroll",SCROLLED:"scrolled",REMOVED:"removed"},VIEWS:{AXIS:"axis",WRITING_MODE:"writingMode",LOAD_ERROR:"loaderror",RENDERED:"rendered",RESIZED:"resized",DISPLAYED:"displayed",SHOWN:"shown",HIDDEN:"hidden",MARK_CLICKED:"markClicked"},RENDITION:{STARTED:"started",ATTACHED:"attached",DISPLAYED:"displayed",DISPLAY_ERROR:"displayerror",RENDERED:"rendered",REMOVED:"removed",RESIZED:"resized",ORIENTATION_CHANGE:"orientationchange",LOCATION_CHANGED:"locationChanged",RELOCATED:"relocated",MARK_CLICKED:"markClicked",SELECTED:"selected",LAYOUT:"layout"},LAYOUT:{UPDATED:"updated"},ANNOTATION:{ATTACH:"attach",DETACH:"detach"}}},function(t,e,n){"use strict";n(35),n(15),n(37),n(38),n(157),n(158),n(63),n(66),n(11),n(10),n(34),n(67),n(68),n(69),n(14),n(18),n(22);var i=n(0);function r(t){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function o(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}var s=function(){function t(e,n,o){var s;if(function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.str="",this.base={},this.spinePos=0,this.range=!1,this.path={},this.start=null,this.end=null,!(this instanceof t))return new t(e,n,o);if("string"==typeof n?this.base=this.parseComponent(n):"object"===r(n)&&n.steps&&(this.base=n),"string"===(s=this.checkType(e)))return this.str=e,Object(i.extend)(this,this.parse(e));if("range"===s)return Object(i.extend)(this,this.fromRange(e,this.base,o));if("node"===s)return Object(i.extend)(this,this.fromNode(e,this.base,o));if("EpubCFI"===s&&e.path)return e;if(e)throw new TypeError("not a valid argument for EpubCFI");return this}var e,n,s;return e=t,(n=[{key:"checkType",value:function(e){return this.isCfiString(e)?"string":!e||"object"!==r(e)||"Range"!==Object(i.type)(e)&&void 0===e.startContainer?e&&"object"===r(e)&&void 0!==e.nodeType?"node":!!(e&&"object"===r(e)&&e instanceof t)&&"EpubCFI":"range"}},{key:"parse",value:function(t){var e,n,i,r={spinePos:-1,range:!1,base:{},path:{},start:null,end:null};return"string"!=typeof t?{spinePos:-1}:(0===t.indexOf("epubcfi(")&&")"===t[t.length-1]&&(t=t.slice(8,t.length-1)),(e=this.getChapterComponent(t))?(r.base=this.parseComponent(e),n=this.getPathComponent(t),r.path=this.parseComponent(n),(i=this.getRange(t))&&(r.range=!0,r.start=this.parseComponent(i[0]),r.end=this.parseComponent(i[1])),r.spinePos=r.base.steps[1].index,r):{spinePos:-1})}},{key:"parseComponent",value:function(t){var e,n={steps:[],terminal:{offset:null,assertion:null}},i=t.split(":"),r=i[0].split("/");return i.length>1&&(e=i[1],n.terminal=this.parseTerminal(e)),""===r[0]&&r.shift(),n.steps=r.map(function(t){return this.parseStep(t)}.bind(this)),n}},{key:"parseStep",value:function(t){var e,n,i,r,o;if((r=t.match(/\[(.*)\]/))&&r[1]&&(o=r[1]),n=parseInt(t),!isNaN(n))return n%2==0?(e="element",i=n/2-1):(e="text",i=(n-1)/2),{type:e,index:i,id:o||null}}},{key:"parseTerminal",value:function(t){var e,n,r=t.match(/\[(.*)\]/);return r&&r[1]?(e=parseInt(t.split("[")[0]),n=r[1]):e=parseInt(t),Object(i.isNumber)(e)||(e=null),{offset:e,assertion:n}}},{key:"getChapterComponent",value:function(t){return t.split("!")[0]}},{key:"getPathComponent",value:function(t){var e=t.split("!");if(e[1])return e[1].split(",")[0]}},{key:"getRange",value:function(t){var e=t.split(",");return 3===e.length&&[e[1],e[2]]}},{key:"getCharecterOffsetComponent",value:function(t){return t.split(":")[1]||""}},{key:"joinSteps",value:function(t){return t?t.map((function(t){var e="";return"element"===t.type&&(e+=2*(t.index+1)),"text"===t.type&&(e+=1+2*t.index),t.id&&(e+="["+t.id+"]"),e})).join("/"):""}},{key:"segmentString",value:function(t){var e="/";return e+=this.joinSteps(t.steps),t.terminal&&null!=t.terminal.offset&&(e+=":"+t.terminal.offset),t.terminal&&null!=t.terminal.assertion&&(e+="["+t.terminal.assertion+"]"),e}},{key:"toString",value:function(){var t="epubcfi(";return t+=this.segmentString(this.base),t+="!",t+=this.segmentString(this.path),this.range&&this.start&&(t+=",",t+=this.segmentString(this.start)),this.range&&this.end&&(t+=",",t+=this.segmentString(this.end)),t+=")"}},{key:"compare",value:function(e,n){var i,r,o,s;if("string"==typeof e&&(e=new t(e)),"string"==typeof n&&(n=new t(n)),e.spinePos>n.spinePos)return 1;if(e.spinePos<n.spinePos)return-1;e.range?(i=e.path.steps.concat(e.start.steps),o=e.start.terminal):(i=e.path.steps,o=e.path.terminal),n.range?(r=n.path.steps.concat(n.start.steps),s=n.start.terminal):(r=n.path.steps,s=n.path.terminal);for(var a=0;a<i.length;a++){if(!i[a])return-1;if(!r[a])return 1;if(i[a].index>r[a].index)return 1;if(i[a].index<r[a].index)return-1}return i.length<r.length?-1:o.offset>s.offset?1:o.offset<s.offset?-1:0}},{key:"step",value:function(t){var e=3===t.nodeType?"text":"element";return{id:t.id,tagName:t.tagName,type:e,index:this.position(t)}}},{key:"filteredStep",value:function(t,e){var n,i=this.filter(t,e);if(i)return n=3===i.nodeType?"text":"element",{id:i.id,tagName:i.tagName,type:n,index:this.filteredPosition(i,e)}}},{key:"pathTo",value:function(t,e,n){for(var i,r={steps:[],terminal:{offset:null,assertion:null}},o=t;o&&o.parentNode&&9!=o.parentNode.nodeType;)(i=n?this.filteredStep(o,n):this.step(o))&&r.steps.unshift(i),o=o.parentNode;return null!=e&&e>=0&&(r.terminal.offset=e,"text"!=r.steps[r.steps.length-1].type&&r.steps.push({type:"text",index:0})),r}},{key:"equalStep",value:function(t,e){return!(!t||!e)&&t.index===e.index&&t.id===e.id&&t.type===e.type}},{key:"fromRange",value:function(t,e,n){var i={range:!1,base:{},path:{},start:null,end:null},o=t.startContainer,s=t.endContainer,a=t.startOffset,u=t.endOffset,c=!1;if(n&&(c=null!=o.ownerDocument.querySelector("."+n)),"string"==typeof e?(i.base=this.parseComponent(e),i.spinePos=i.base.steps[1].index):"object"===r(e)&&(i.base=e),t.collapsed)c&&(a=this.patchOffset(o,a,n)),i.path=this.pathTo(o,a,n);else{i.range=!0,c&&(a=this.patchOffset(o,a,n)),i.start=this.pathTo(o,a,n),c&&(u=this.patchOffset(s,u,n)),i.end=this.pathTo(s,u,n),i.path={steps:[],terminal:null};var l,h=i.start.steps.length;for(l=0;l<h&&this.equalStep(i.start.steps[l],i.end.steps[l]);l++)l===h-1?i.start.terminal===i.end.terminal&&(i.path.steps.push(i.start.steps[l]),i.range=!1):i.path.steps.push(i.start.steps[l]);i.start.steps=i.start.steps.slice(i.path.steps.length),i.end.steps=i.end.steps.slice(i.path.steps.length)}return i}},{key:"fromNode",value:function(t,e,n){var i={range:!1,base:{},path:{},start:null,end:null};return"string"==typeof e?(i.base=this.parseComponent(e),i.spinePos=i.base.steps[1].index):"object"===r(e)&&(i.base=e),i.path=this.pathTo(t,null,n),i}},{key:"filter",value:function(t,e){var n,i,r,o,s,a=!1;return 3===t.nodeType?(a=!0,r=t.parentNode,n=t.parentNode.classList.contains(e)):(a=!1,n=t.classList.contains(e)),n&&a?(o=r.previousSibling,s=r.nextSibling,o&&3===o.nodeType?i=o:s&&3===s.nodeType&&(i=s),i||t):!(n&&!a)&&t}},{key:"patchOffset",value:function(t,e,n){if(3!=t.nodeType)throw new Error("Anchor must be a text node");var i=t,r=e;for(t.parentNode.classList.contains(n)&&(i=t.parentNode);i.previousSibling;){if(1===i.previousSibling.nodeType){if(!i.previousSibling.classList.contains(n))break;r+=i.previousSibling.textContent.length}else r+=i.previousSibling.textContent.length;i=i.previousSibling}return r}},{key:"normalizedMap",value:function(t,e,n){var i,r,o,s={},a=-1,u=t.length;for(i=0;i<u;i++)1===(r=t[i].nodeType)&&t[i].classList.contains(n)&&(r=3),i>0&&3===r&&3===o?s[i]=a:e===r&&(a+=1,s[i]=a),o=r;return s}},{key:"position",value:function(t){var e,n;return 1===t.nodeType?((e=t.parentNode.children)||(e=Object(i.findChildren)(t.parentNode)),n=Array.prototype.indexOf.call(e,t)):n=(e=this.textNodes(t.parentNode)).indexOf(t),n}},{key:"filteredPosition",value:function(t,e){var n,i;return 1===t.nodeType?(n=t.parentNode.children,i=this.normalizedMap(n,1,e)):(n=t.parentNode.childNodes,t.parentNode.classList.contains(e)&&(n=(t=t.parentNode).parentNode.childNodes),i=this.normalizedMap(n,3,e)),i[Array.prototype.indexOf.call(n,t)]}},{key:"stepsToXpath",value:function(t){var e=[".","*"];return t.forEach((function(t){var n=t.index+1;t.id?e.push("*[position()="+n+" and @id='"+t.id+"']"):"text"===t.type?e.push("text()["+n+"]"):e.push("*["+n+"]")})),e.join("/")}},{key:"stepsToQuerySelector",value:function(t){var e=["html"];return t.forEach((function(t){var n=t.index+1;t.id?e.push("#"+t.id):"text"===t.type||e.push("*:nth-child("+n+")")})),e.join(">")}},{key:"textNodes",value:function(t,e){return Array.prototype.slice.call(t.childNodes).filter((function(t){return 3===t.nodeType||!(!e||!t.classList.contains(e))}))}},{key:"walkToNode",value:function(t,e,n){var r,o,s=e||document,a=s.documentElement,u=t.length;for(o=0;o<u&&("element"===(r=t[o]).type?a=r.id?s.getElementById(r.id):(a.children||Object(i.findChildren)(a))[r.index]:"text"===r.type&&(a=this.textNodes(a,n)[r.index]),a);o++);return a}},{key:"findNode",value:function(t,e,n){var i,r,o=e||document;return n||void 0===o.evaluate?i=n?this.walkToNode(t,o,n):this.walkToNode(t,o):(r=this.stepsToXpath(t),i=o.evaluate(r,o,null,XPathResult.FIRST_ORDERED_NODE_TYPE,null).singleNodeValue),i}},{key:"fixMiss",value:function(t,e,n,i){var r,o,s=this.findNode(t.slice(0,-1),n,i),a=s.childNodes,u=this.normalizedMap(a,3,i),c=t[t.length-1].index;for(var l in u){if(!u.hasOwnProperty(l))return;if(u[l]===c){if(!(e>(o=(r=a[l]).textContent.length))){s=1===r.nodeType?r.childNodes[0]:r;break}e-=o}}return{container:s,offset:e}}},{key:"toRange",value:function(t,e){var n,r,o,s,a,u,c,l,h=t||document,f=!!e&&null!=h.querySelector("."+e);if(n=void 0!==h.createRange?h.createRange():new i.RangeObject,this.range?(r=this.start,u=this.path.steps.concat(r.steps),s=this.findNode(u,h,f?e:null),o=this.end,c=this.path.steps.concat(o.steps),a=this.findNode(c,h,f?e:null)):(r=this.path,u=this.path.steps,s=this.findNode(this.path.steps,h,f?e:null)),!s)return console.log("No startContainer found for",this.toString()),null;try{null!=r.terminal.offset?n.setStart(s,r.terminal.offset):n.setStart(s,0)}catch(t){l=this.fixMiss(u,r.terminal.offset,h,f?e:null),n.setStart(l.container,l.offset)}if(a)try{null!=o.terminal.offset?n.setEnd(a,o.terminal.offset):n.setEnd(a,0)}catch(t){l=this.fixMiss(c,this.end.terminal.offset,h,f?e:null),n.setEnd(l.container,l.offset)}return n}},{key:"isCfiString",value:function(t){return"string"==typeof t&&0===t.indexOf("epubcfi(")&&")"===t[t.length-1]}},{key:"generateChapterComponent",value:function(t,e,n){var i="/"+2*(t+1)+"/";return i+=2*(parseInt(e)+1),n&&(i+="["+n+"]"),i}},{key:"collapse",value:function(t){this.range&&(this.range=!1,t?(this.path.steps=this.path.steps.concat(this.start.steps),this.path.terminal=this.start.terminal):(this.path.steps=this.path.steps.concat(this.end.steps),this.path.terminal=this.end.terminal))}}])&&o(e.prototype,n),s&&o(e,s),t}();e.a=s},function(t,e){t.exports=function(t){try{return!!t()}catch(t){return!0}}},function(t,e,n){(function(e){var n=function(t){return t&&t.Math==Math&&t};t.exports=n("object"==typeof globalThis&&globalThis)||n("object"==typeof window&&window)||n("object"==typeof self&&self)||n("object"==typeof e&&e)||function(){return this}()||Function("return this")()}).call(this,n(73))},function(t,e,n){var i=n(4),r=n(45).f,o=n(33),s=n(25),a=n(104),u=n(130),c=n(107);t.exports=function(t,e){var n,l,h,f,d,p=t.target,v=t.global,g=t.stat;if(n=v?i:g?i[p]||a(p,{}):(i[p]||{}).prototype)for(l in e){if(f=e[l],h=t.noTargetGet?(d=r(n,l))&&d.value:n[l],!c(v?l:p+(g?".":"#")+l,t.forced)&&void 0!==h){if(typeof f==typeof h)continue;u(f,h)}(t.sham||h&&h.sham)&&o(f,"sham",!0),s(n,l,f,t)}}},function(t,e,n){var i=n(4),r=n(78),o=n(13),s=n(79),a=n(102),u=n(127),c=r("wks"),l=i.Symbol,h=u?l:l&&l.withoutSetter||s;t.exports=function(t){return o(c,t)&&(a||"string"==typeof c[t])||(a&&o(l,t)?c[t]=l[t]:c[t]=h("Symbol."+t)),c[t]}},function(t,e){t.exports=function(t){return"function"==typeof t}},function(t,e,n){var i=n(9);t.exports=function(t){if(i(t))return t;throw TypeError(String(t)+" is not an object")}},function(t,e,n){var i=n(7);t.exports=function(t){return"object"==typeof t?null!==t:i(t)}},function(t,e,n){var i=n(110),r=n(25),o=n(180);i||r(Object.prototype,"toString",o,{unsafe:!0})},function(t,e,n){var i=n(4),r=n(147),o=n(148),s=n(211),a=n(33),u=function(t){if(t&&t.forEach!==s)try{a(t,"forEach",s)}catch(e){t.forEach=s}};for(var c in r)r[c]&&u(i[c]&&i[c].prototype);u(o)},function(t,e,n){"use strict";var i,r,o,s,a,u,c,l=n(193),h=n(207),f=Function.prototype.apply,d=Function.prototype.call,p=Object.create,v=Object.defineProperty,g=Object.defineProperties,m=Object.prototype.hasOwnProperty,y={configurable:!0,enumerable:!1,writable:!0};r=function(t,e){var n,r;return h(e),r=this,i.call(this,t,n=function(){o.call(r,t,n),f.call(e,this,arguments)}),n.__eeOnceListener__=e,this},a={on:i=function(t,e){var n;return h(e),m.call(this,"__ee__")?n=this.__ee__:(n=y.value=p(null),v(this,"__ee__",y),y.value=null),n[t]?"object"==typeof n[t]?n[t].push(e):n[t]=[n[t],e]:n[t]=e,this},once:r,off:o=function(t,e){var n,i,r,o;if(h(e),!m.call(this,"__ee__"))return this;if(!(n=this.__ee__)[t])return this;if("object"==typeof(i=n[t]))for(o=0;r=i[o];++o)r!==e&&r.__eeOnceListener__!==e||(2===i.length?n[t]=i[o?0:1]:i.splice(o,1));else i!==e&&i.__eeOnceListener__!==e||delete n[t];return this},emit:s=function(t){var e,n,i,r,o;if(m.call(this,"__ee__")&&(r=this.__ee__[t]))if("object"==typeof r){for(n=arguments.length,o=new Array(n-1),e=1;e<n;++e)o[e-1]=arguments[e];for(r=r.slice(),e=0;i=r[e];++e)f.call(i,this,o)}else switch(arguments.length){case 1:d.call(r,this);break;case 2:d.call(r,this,arguments[1]);break;case 3:d.call(r,this,arguments[1],arguments[2]);break;default:for(n=arguments.length,o=new Array(n-1),e=1;e<n;++e)o[e-1]=arguments[e];f.call(r,this,o)}}},u={on:l(i),once:l(r),off:l(o),emit:l(s)},c=g({},u),t.exports=e=function(t){return null==t?p(c):g(Object(t),u)},e.methods=a},function(t,e,n){var i=n(24),r={}.hasOwnProperty;t.exports=Object.hasOwn||function(t,e){return r.call(i(t),e)}},function(t,e,n){"use strict";var i=n(31),r=n(86),o=n(60),s=n(30),a=n(137),u=s.set,c=s.getterFor("Array Iterator");t.exports=a(Array,"Array",(function(t,e){u(this,{type:"Array Iterator",target:i(t),index:0,kind:e})}),(function(){var t=c(this),e=t.target,n=t.kind,i=t.index++;return!e||i>=e.length?(t.target=void 0,{value:void 0,done:!0}):"keys"==n?{value:i,done:!1}:"values"==n?{value:e[i],done:!1}:{value:[i,e[i]],done:!1}}),"values"),o.Arguments=o.Array,r("keys"),r("values"),r("entries")},function(t,e,n){"use strict";var i=n(5),r=n(90);i({target:"RegExp",proto:!0,forced:/./.exec!==r},{exec:r})},function(t,e,n){"use strict";n(14),n(10),n(18),n(22),n(53),n(44),n(35),n(15);var i=n(26),r=n.n(i);function o(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}var s=function(){function t(e){var n;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),e.indexOf("://")>-1&&(e=new URL(e).pathname),n=this.parse(e),this.path=e,this.isDirectory(e)?this.directory=e:this.directory=n.dir+"/",this.filename=n.base,this.extension=n.ext.slice(1)}var e,n,i;return e=t,(n=[{key:"parse",value:function(t){return r.a.parse(t)}},{key:"isAbsolute",value:function(t){return r.a.isAbsolute(t||this.path)}},{key:"isDirectory",value:function(t){return"/"===t.charAt(t.length-1)}},{key:"resolve",value:function(t){return r.a.resolve(this.directory,t)}},{key:"relative",value:function(t){return t&&t.indexOf("://")>-1?t:r.a.relative(this.directory,t)}},{key:"splitPath",value:function(t){return this.splitPathRe.exec(t).slice(1)}},{key:"toString",value:function(){return this.path}}])&&o(e.prototype,n),i&&o(e,i),t}();e.a=s},function(t,e,n){var i=n(3);t.exports=!i((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]}))},function(t,e,n){"use strict";var i=n(113).charAt,r=n(21),o=n(30),s=n(137),a=o.set,u=o.getterFor("String Iterator");s(String,"String",(function(t){a(this,{type:"String Iterator",string:r(t),index:0})}),(function(){var t,e=u(this),n=e.string,r=e.index;return r>=n.length?{value:void 0,done:!0}:(t=i(n,r),e.index+=t.length,{value:t,done:!1})}))},function(t,e,n){"use strict";n(15),n(218),n(14),n(10),n(18),n(22),n(53),n(44);var i=n(16),r=n(26),o=n.n(r);function s(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}var a=function(){function t(e,n){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t);var r=e.indexOf("://")>-1,o=e;if(this.Url=void 0,this.href=e,this.protocol="",this.origin="",this.hash="",this.hash="",this.search="",this.base=n,!r&&!1!==n&&"string"!=typeof n&&window&&window.location&&(this.base=window.location.href),r||this.base)try{this.base?this.Url=new URL(e,this.base):this.Url=new URL(e),this.href=this.Url.href,this.protocol=this.Url.protocol,this.origin=this.Url.origin,this.hash=this.Url.hash,this.search=this.Url.search,o=this.Url.pathname+(this.Url.search?this.Url.search:"")}catch(t){this.Url=void 0,this.base&&(o=new i.a(this.base).resolve(o))}this.Path=new i.a(o),this.directory=this.Path.directory,this.filename=this.Path.filename,this.extension=this.Path.extension}var e,n,r;return e=t,(n=[{key:"path",value:function(){return this.Path}},{key:"resolve",value:function(t){var e;return t.indexOf("://")>-1?t:(e=o.a.resolve(this.directory,t),this.origin+e)}},{key:"relative",value:function(t){return o.a.relative(t,this.directory)}},{key:"toString",value:function(){return this.href}}])&&s(e.prototype,n),r&&s(e,r),t}();e.a=a},function(t,e,n){var i=n(17),r=n(129),o=n(8),s=n(75),a=Object.defineProperty;e.f=i?a:function(t,e,n){if(o(t),e=s(e),o(n),r)try{return a(t,e,n)}catch(t){}if("get"in n||"set"in n)throw TypeError("Accessors not supported");return"value"in n&&(t[e]=n.value),t}},function(t,e,n){var i=n(59);t.exports=function(t){if("Symbol"===i(t))throw TypeError("Cannot convert a Symbol value to a string");return String(t)}},function(t,e,n){var i=n(4),r=n(147),o=n(148),s=n(14),a=n(33),u=n(6),c=u("iterator"),l=u("toStringTag"),h=s.values,f=function(t,e){if(t){if(t[c]!==h)try{a(t,c,h)}catch(e){t[c]=h}if(t[l]||a(t,l,e),r[e])for(var n in s)if(t[n]!==s[n])try{a(t,n,s[n])}catch(e){t[n]=s[n]}}};for(var d in r)f(i[d]&&i[d].prototype,d);f(o,"DOMTokenList")},function(t,e,n){"use strict";n(95),n(11),n(14),n(10),n(27),n(18),n(22);function i(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}var r=function(){function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.context=e||this,this.hooks=[]}var e,n,r;return e=t,(n=[{key:"register",value:function(){for(var t=0;t<arguments.length;++t)if("function"==typeof arguments[t])this.hooks.push(arguments[t]);else for(var e=0;e<arguments[t].length;++e)this.hooks.push(arguments[t][e])}},{key:"deregister",value:function(t){for(var e=0;e<this.hooks.length;e++)if(this.hooks[e]===t){this.hooks.splice(e,1);break}}},{key:"trigger",value:function(){var t=arguments,e=this.context,n=[];return this.hooks.forEach((function(i){try{var r=i.apply(e,t)}catch(t){console.log(t)}r&&"function"==typeof r.then&&n.push(r)})),Promise.all(n)}},{key:"list",value:function(){return this.hooks}},{key:"clear",value:function(){return this.hooks=[]}}])&&i(e.prototype,n),r&&i(e,r),t}();e.a=r},function(t,e,n){var i=n(32);t.exports=function(t){return Object(i(t))}},function(t,e,n){var i=n(4),r=n(7),o=n(13),s=n(33),a=n(104),u=n(81),c=n(30),l=n(50).CONFIGURABLE,h=c.get,f=c.enforce,d=String(String).split("String");(t.exports=function(t,e,n,u){var c,h=!!u&&!!u.unsafe,p=!!u&&!!u.enumerable,v=!!u&&!!u.noTargetGet,g=u&&void 0!==u.name?u.name:e;r(n)&&("Symbol("===String(g).slice(0,7)&&(g="["+String(g).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),(!o(n,"name")||l&&n.name!==g)&&s(n,"name",g),(c=f(n)).source||(c.source=d.join("string"==typeof g?g:""))),t!==i?(h?!v&&t[e]&&(p=!0):delete t[e],p?t[e]=n:s(t,e,n)):p?t[e]=n:a(e,n)})(Function.prototype,"toString",(function(){return r(this)&&h(this).source||u(this)}))},function(t,e,n){"use strict";if(!i)var i={cwd:function(){return"/"}};function r(t){if("string"!=typeof t)throw new TypeError("Path must be a string. Received "+t)}function o(t,e){for(var n,i="",r=-1,o=0,s=0;s<=t.length;++s){if(s<t.length)n=t.charCodeAt(s);else{if(47===n)break;n=47}if(47===n){if(r===s-1||1===o);else if(r!==s-1&&2===o){if(i.length<2||46!==i.charCodeAt(i.length-1)||46!==i.charCodeAt(i.length-2))if(i.length>2){for(var a=i.length-1,u=a;u>=0&&47!==i.charCodeAt(u);--u);if(u!==a){i=-1===u?"":i.slice(0,u),r=s,o=0;continue}}else if(2===i.length||1===i.length){i="",r=s,o=0;continue}e&&(i.length>0?i+="/..":i="..")}else i.length>0?i+="/"+t.slice(r+1,s):i=t.slice(r+1,s);r=s,o=0}else 46===n&&-1!==o?++o:o=-1}return i}var s={resolve:function(){for(var t,e="",n=!1,s=arguments.length-1;s>=-1&&!n;s--){var a;s>=0?a=arguments[s]:(void 0===t&&(t=i.cwd()),a=t),r(a),0!==a.length&&(e=a+"/"+e,n=47===a.charCodeAt(0))}return e=o(e,!n),n?e.length>0?"/"+e:"/":e.length>0?e:"."},normalize:function(t){if(r(t),0===t.length)return".";var e=47===t.charCodeAt(0),n=47===t.charCodeAt(t.length-1);return 0!==(t=o(t,!e)).length||e||(t="."),t.length>0&&n&&(t+="/"),e?"/"+t:t},isAbsolute:function(t){return r(t),t.length>0&&47===t.charCodeAt(0)},join:function(){if(0===arguments.length)return".";for(var t,e=0;e<arguments.length;++e){var n=arguments[e];r(n),n.length>0&&(void 0===t?t=n:t+="/"+n)}return void 0===t?".":s.normalize(t)},relative:function(t,e){if(r(t),r(e),t===e)return"";if((t=s.resolve(t))===(e=s.resolve(e)))return"";for(var n=1;n<t.length&&47===t.charCodeAt(n);++n);for(var i=t.length,o=i-n,a=1;a<e.length&&47===e.charCodeAt(a);++a);for(var u=e.length-a,c=o<u?o:u,l=-1,h=0;h<=c;++h){if(h===c){if(u>c){if(47===e.charCodeAt(a+h))return e.slice(a+h+1);if(0===h)return e.slice(a+h)}else o>c&&(47===t.charCodeAt(n+h)?l=h:0===h&&(l=0));break}var f=t.charCodeAt(n+h);if(f!==e.charCodeAt(a+h))break;47===f&&(l=h)}var d="";for(h=n+l+1;h<=i;++h)h!==i&&47!==t.charCodeAt(h)||(0===d.length?d+="..":d+="/..");return d.length>0?d+e.slice(a+l):(a+=l,47===e.charCodeAt(a)&&++a,e.slice(a))},_makeLong:function(t){return t},dirname:function(t){if(r(t),0===t.length)return".";for(var e=t.charCodeAt(0),n=47===e,i=-1,o=!0,s=t.length-1;s>=1;--s)if(47===(e=t.charCodeAt(s))){if(!o){i=s;break}}else o=!1;return-1===i?n?"/":".":n&&1===i?"//":t.slice(0,i)},basename:function(t,e){if(void 0!==e&&"string"!=typeof e)throw new TypeError('"ext" argument must be a string');r(t);var n,i=0,o=-1,s=!0;if(void 0!==e&&e.length>0&&e.length<=t.length){if(e.length===t.length&&e===t)return"";var a=e.length-1,u=-1;for(n=t.length-1;n>=0;--n){var c=t.charCodeAt(n);if(47===c){if(!s){i=n+1;break}}else-1===u&&(s=!1,u=n+1),a>=0&&(c===e.charCodeAt(a)?-1==--a&&(o=n):(a=-1,o=u))}return i===o?o=u:-1===o&&(o=t.length),t.slice(i,o)}for(n=t.length-1;n>=0;--n)if(47===t.charCodeAt(n)){if(!s){i=n+1;break}}else-1===o&&(s=!1,o=n+1);return-1===o?"":t.slice(i,o)},extname:function(t){r(t);for(var e=-1,n=0,i=-1,o=!0,s=0,a=t.length-1;a>=0;--a){var u=t.charCodeAt(a);if(47!==u)-1===i&&(o=!1,i=a+1),46===u?-1===e?e=a:1!==s&&(s=1):-1!==e&&(s=-1);else if(!o){n=a+1;break}}return-1===e||-1===i||0===s||1===s&&e===i-1&&e===n+1?"":t.slice(e,i)},format:function(t){if(null===t||"object"!=typeof t)throw new TypeError('Parameter "pathObject" must be an object, not '+typeof t);return function(t,e){var n=e.dir||e.root,i=e.base||(e.name||"")+(e.ext||"");return n?n===e.root?n+i:n+t+i:i}("/",t)},parse:function(t){r(t);var e={root:"",dir:"",base:"",ext:"",name:""};if(0===t.length)return e;var n,i=t.charCodeAt(0),o=47===i;o?(e.root="/",n=1):n=0;for(var s=-1,a=0,u=-1,c=!0,l=t.length-1,h=0;l>=n;--l)if(47!==(i=t.charCodeAt(l)))-1===u&&(c=!1,u=l+1),46===i?-1===s?s=l:1!==h&&(h=1):-1!==s&&(h=-1);else if(!c){a=l+1;break}return-1===s||-1===u||0===h||1===h&&s===u-1&&s===a+1?-1!==u&&(e.base=e.name=0===a&&o?t.slice(1,u):t.slice(a,u)):(0===a&&o?(e.name=t.slice(1,s),e.base=t.slice(1,u)):(e.name=t.slice(a,s),e.base=t.slice(a,u)),e.ext=t.slice(s,u)),a>0?e.dir=t.slice(0,a-1):o&&(e.dir="/"),e},sep:"/",delimiter:":",posix:null};t.exports=s},function(t,e,n){"use strict";var i,r,o,s,a=n(5),u=n(49),c=n(4),l=n(29),h=n(181),f=n(25),d=n(108),p=n(85),v=n(42),g=n(109),m=n(48),y=n(7),b=n(9),w=n(84),x=n(81),k=n(182),E=n(183),S=n(142),O=n(144).set,T=n(184),_=n(187),N=n(188),C=n(146),R=n(189),I=n(30),A=n(107),j=n(6),L=n(190),P=n(112),D=n(76),M=j("species"),z="Promise",B=I.get,q=I.set,U=I.getterFor(z),F=h&&h.prototype,W=h,H=F,V=c.TypeError,X=c.document,G=c.process,Y=C.f,$=Y,K=!!(X&&X.createEvent&&c.dispatchEvent),Z=y(c.PromiseRejectionEvent),J=!1,Q=A(z,(function(){var t=x(W),e=t!==String(W);if(!e&&66===D)return!0;if(u&&!H.finally)return!0;if(D>=51&&/native code/.test(t))return!1;var n=new W((function(t){t(1)})),i=function(t){t((function(){}),(function(){}))};return(n.constructor={})[M]=i,!(J=n.then((function(){}))instanceof i)||!e&&L&&!Z})),tt=Q||!E((function(t){W.all(t).catch((function(){}))})),et=function(t){var e;return!(!b(t)||!y(e=t.then))&&e},nt=function(t,e){if(!t.notified){t.notified=!0;var n=t.reactions;T((function(){for(var i=t.value,r=1==t.state,o=0;n.length>o;){var s,a,u,c=n[o++],l=r?c.ok:c.fail,h=c.resolve,f=c.reject,d=c.domain;try{l?(r||(2===t.rejection&&st(t),t.rejection=1),!0===l?s=i:(d&&d.enter(),s=l(i),d&&(d.exit(),u=!0)),s===c.promise?f(V("Promise-chain cycle")):(a=et(s))?a.call(s,h,f):h(s)):f(i)}catch(t){d&&!u&&d.exit(),f(t)}}t.reactions=[],t.notified=!1,e&&!t.rejection&&rt(t)}))}},it=function(t,e,n){var i,r;K?((i=X.createEvent("Event")).promise=e,i.reason=n,i.initEvent(t,!1,!0),c.dispatchEvent(i)):i={promise:e,reason:n},!Z&&(r=c["on"+t])?r(i):"unhandledrejection"===t&&N("Unhandled promise rejection",n)},rt=function(t){O.call(c,(function(){var e,n=t.facade,i=t.value;if(ot(t)&&(e=R((function(){P?G.emit("unhandledRejection",i,n):it("unhandledrejection",n,i)})),t.rejection=P||ot(t)?2:1,e.error))throw e.value}))},ot=function(t){return 1!==t.rejection&&!t.parent},st=function(t){O.call(c,(function(){var e=t.facade;P?G.emit("rejectionHandled",e):it("rejectionhandled",e,t.value)}))},at=function(t,e,n){return function(i){t(e,i,n)}},ut=function(t,e,n){t.done||(t.done=!0,n&&(t=n),t.value=e,t.state=2,nt(t,!0))},ct=function(t,e,n){if(!t.done){t.done=!0,n&&(t=n);try{if(t.facade===e)throw V("Promise can't be resolved itself");var i=et(e);i?T((function(){var n={done:!1};try{i.call(e,at(ct,n,t),at(ut,n,t))}catch(e){ut(n,e,t)}})):(t.value=e,t.state=1,nt(t,!1))}catch(e){ut({done:!1},e,t)}}};if(Q&&(H=(W=function(t){w(this,W,z),m(t),i.call(this);var e=B(this);try{t(at(ct,e),at(ut,e))}catch(t){ut(e,t)}}).prototype,(i=function(t){q(this,{type:z,done:!1,notified:!1,parent:!1,reactions:[],rejection:!1,state:0,value:void 0})}).prototype=d(H,{then:function(t,e){var n=U(this),i=Y(S(this,W));return i.ok=!y(t)||t,i.fail=y(e)&&e,i.domain=P?G.domain:void 0,n.parent=!0,n.reactions.push(i),0!=n.state&&nt(n,!1),i.promise},catch:function(t){return this.then(void 0,t)}}),r=function(){var t=new i,e=B(t);this.promise=t,this.resolve=at(ct,e),this.reject=at(ut,e)},C.f=Y=function(t){return t===W||t===o?new r(t):$(t)},!u&&y(h)&&F!==Object.prototype)){s=F.then,J||(f(F,"then",(function(t,e){var n=this;return new W((function(t,e){s.call(n,t,e)})).then(t,e)}),{unsafe:!0}),f(F,"catch",H.catch,{unsafe:!0}));try{delete F.constructor}catch(t){}p&&p(F,H)}a({global:!0,wrap:!0,forced:Q},{Promise:W}),v(W,z,!1,!0),g(z),o=l(z),a({target:z,stat:!0,forced:Q},{reject:function(t){var e=Y(this);return e.reject.call(void 0,t),e.promise}}),a({target:z,stat:!0,forced:u||Q},{resolve:function(t){return _(u&&this===o?W:this,t)}}),a({target:z,stat:!0,forced:tt},{all:function(t){var e=this,n=Y(e),i=n.resolve,r=n.reject,o=R((function(){var n=m(e.resolve),o=[],s=0,a=1;k(t,(function(t){var u=s++,c=!1;o.push(void 0),a++,n.call(e,t).then((function(t){c||(c=!0,o[u]=t,--a||i(o))}),r)})),--a||i(o)}));return o.error&&r(o.value),n.promise},race:function(t){var e=this,n=Y(e),i=n.reject,r=R((function(){var r=m(e.resolve);k(t,(function(t){r.call(e,t).then(n.resolve,i)}))}));return r.error&&i(r.value),n.promise}})},function(t,e,n){"use strict";n.d(e,"a",(function(){return o})),n.d(e,"b",(function(){return s})),n.d(e,"d",(function(){return a})),n.d(e,"c",(function(){return u})),n.d(e,"e",(function(){return c}));n(11),n(15),n(62),n(221),n(34);var i=n(0),r=n(19);n(16);function o(t,e){var n,r,o=e.url,s=o.indexOf("://")>-1;t&&(r=Object(i.qs)(t,"head"),(n=Object(i.qs)(r,"base"))||(n=t.createElement("base"),r.insertBefore(n,r.firstChild)),!s&&window&&window.location&&(o=window.location.origin+o),n.setAttribute("href",o))}function s(t,e){var n,r,o=e.canonical;t&&(n=Object(i.qs)(t,"head"),(r=Object(i.qs)(n,"link[rel='canonical']"))?r.setAttribute("href",o):((r=t.createElement("link")).setAttribute("rel","canonical"),r.setAttribute("href",o),n.appendChild(r)))}function a(t,e){var n,r,o=e.idref;t&&(n=Object(i.qs)(t,"head"),(r=Object(i.qs)(n,"link[property='dc.identifier']"))?r.setAttribute("content",o):((r=t.createElement("meta")).setAttribute("name","dc.identifier"),r.setAttribute("content",o),n.appendChild(r)))}function u(t,e){var n=t.querySelectorAll("a[href]");if(n.length)for(var o=Object(i.qs)(t.ownerDocument,"base"),s=o?o.getAttribute("href"):void 0,a=function(t){var n=t.getAttribute("href");if(0!==n.indexOf("mailto:"))if(n.indexOf("://")>-1)t.setAttribute("target","_blank");else{var i;try{i=new r.a(n,s)}catch(t){}t.onclick=function(){return i&&i.hash?e(i.Path.path+i.hash):e(i?i.Path.path:n),!1}}}.bind(this),u=0;u<n.length;u++)a(n[u])}function c(t,e,n){return e.forEach((function(e,i){e&&n[i]&&(e=e.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&"),t=t.replace(new RegExp(e,"g"),n[i]))})),t}},function(t,e,n){var i=n(4),r=n(7),o=function(t){return r(t)?t:void 0};t.exports=function(t,e){return arguments.length<2?o(i[t]):i[t]&&i[t][e]}},function(t,e,n){var i,r,o,s=n(173),a=n(4),u=n(9),c=n(33),l=n(13),h=n(103),f=n(82),d=n(57),p=a.WeakMap;if(s||h.state){var v=h.state||(h.state=new p),g=v.get,m=v.has,y=v.set;i=function(t,e){if(m.call(v,t))throw new TypeError("Object already initialized");return e.facade=t,y.call(v,t,e),e},r=function(t){return g.call(v,t)||{}},o=function(t){return m.call(v,t)}}else{var b=f("state");d[b]=!0,i=function(t,e){if(l(t,b))throw new TypeError("Object already initialized");return e.facade=t,c(t,b,e),e},r=function(t){return l(t,b)?t[b]:{}},o=function(t){return l(t,b)}}t.exports={set:i,get:r,has:o,enforce:function(t){return o(t)?r(t):i(t,{})},getterFor:function(t){return function(e){var n;if(!u(e)||(n=r(e)).type!==t)throw TypeError("Incompatible receiver, "+t+" required");return n}}}},function(t,e,n){var i=n(74),r=n(32);t.exports=function(t){return i(r(t))}},function(t,e){t.exports=function(t){if(null==t)throw TypeError("Can't call method on "+t);return t}},function(t,e,n){var i=n(17),r=n(20),o=n(46);t.exports=i?function(t,e,n){return r.f(t,e,o(1,n))}:function(t,e,n){return t[e]=n,t}},function(t,e,n){"use strict";var i=n(50).PROPER,r=n(25),o=n(8),s=n(21),a=n(3),u=n(114),c=RegExp.prototype,l=c.toString,h=a((function(){return"/a/b"!=l.call({source:"a",flags:"b"})})),f=i&&"toString"!=l.name;(h||f)&&r(RegExp.prototype,"toString",(function(){var t=o(this),e=s(t.source),n=t.flags;return"/"+e+"/"+s(void 0===n&&t instanceof RegExp&&!("flags"in c)?u.call(t):n)}),{unsafe:!0})},function(t,e,n){"use strict";var i=n(5),r=n(93),o=n(89),s=n(9),a=n(83),u=n(36),c=n(31),l=n(94),h=n(6),f=n(64)("slice"),d=h("species"),p=[].slice,v=Math.max;i({target:"Array",proto:!0,forced:!f},{slice:function(t,e){var n,i,h,f=c(this),g=u(f),m=a(t,g),y=a(void 0===e?g:e,g);if(r(f)&&(n=f.constructor,(o(n)&&(n===Array||r(n.prototype))||s(n)&&null===(n=n[d]))&&(n=void 0),n===Array||void 0===n))return p.call(f,m,y);for(i=new(void 0===n?Array:n)(v(y-m,0)),h=0;m<y;m++,h++)m in f&&l(i,h,f[m]);return i.length=h,i}})},function(t,e,n){var i=n(52);t.exports=function(t){return i(t.length)}},function(t,e,n){"use strict";var i=n(91),r=n(120),o=n(8),s=n(32),a=n(142),u=n(116),c=n(52),l=n(21),h=n(40),f=n(92),d=n(90),p=n(115),v=n(3),g=p.UNSUPPORTED_Y,m=[].push,y=Math.min;i("split",(function(t,e,n){var i;return i="c"=="abbc".split(/(b)*/)[1]||4!="test".split(/(?:)/,-1).length||2!="ab".split(/(?:ab)*/).length||4!=".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length?function(t,n){var i=l(s(this)),o=void 0===n?4294967295:n>>>0;if(0===o)return[];if(void 0===t)return[i];if(!r(t))return e.call(i,t,o);for(var a,u,c,h=[],f=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")+(t.sticky?"y":""),p=0,v=new RegExp(t.source,f+"g");(a=d.call(v,i))&&!((u=v.lastIndex)>p&&(h.push(i.slice(p,a.index)),a.length>1&&a.index<i.length&&m.apply(h,a.slice(1)),c=a[0].length,p=u,h.length>=o));)v.lastIndex===a.index&&v.lastIndex++;return p===i.length?!c&&v.test("")||h.push(""):h.push(i.slice(p)),h.length>o?h.slice(0,o):h}:"0".split(void 0,0).length?function(t,n){return void 0===t&&0===n?[]:e.call(this,t,n)}:e,[function(e,n){var r=s(this),o=null==e?void 0:h(e,t);return o?o.call(e,r,n):i.call(l(r),e,n)},function(t,r){var s=o(this),h=l(t),d=n(i,s,h,r,i!==e);if(d.done)return d.value;var p=a(s,RegExp),v=s.unicode,m=(s.ignoreCase?"i":"")+(s.multiline?"m":"")+(s.unicode?"u":"")+(g?"g":"y"),b=new p(g?"^(?:"+s.source+")":s,m),w=void 0===r?4294967295:r>>>0;if(0===w)return[];if(0===h.length)return null===f(b,h)?[h]:[];for(var x=0,k=0,E=[];k<h.length;){b.lastIndex=g?0:k;var S,O=f(b,g?h.slice(k):h);if(null===O||(S=y(c(b.lastIndex+(g?k:0)),h.length))===x)k=u(h,k,v);else{if(E.push(h.slice(x,k)),E.length===w)return E;for(var T=1;T<=O.length-1;T++)if(E.push(O[T]),E.length===w)return E;k=x=S}}return E.push(h.slice(x)),E}]}),!!v((function(){var t=/(?:)/,e=t.exec;t.exec=function(){return e.apply(this,arguments)};var n="ab".split(t);return 2!==n.length||"a"!==n[0]||"b"!==n[1]})),g)},function(t,e,n){"use strict";var i=n(5),r=n(65).map;i({target:"Array",proto:!0,forced:!n(64)("map")},{map:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}})},function(t,e,n){var i=n(17),r=n(50).EXISTS,o=n(20).f,s=Function.prototype,a=s.toString,u=/^\s*function ([^ (]*)/;i&&!r&&o(s,"name",{configurable:!0,get:function(){try{return a.call(this).match(u)[1]}catch(t){return""}}})},function(t,e,n){var i=n(48);t.exports=function(t,e){var n=t[e];return null==n?void 0:i(n)}},function(t,e){var n=Math.ceil,i=Math.floor;t.exports=function(t){var e=+t;return e!=e||0===e?0:(e>0?i:n)(e)}},function(t,e,n){var i=n(20).f,r=n(13),o=n(6)("toStringTag");t.exports=function(t,e,n){t&&!r(t=n?t:t.prototype,o)&&i(t,o,{configurable:!0,value:e})}},function(t,e,n){var i,r=n(8),o=n(135),s=n(105),a=n(57),u=n(136),c=n(80),l=n(82),h=l("IE_PROTO"),f=function(){},d=function(t){return"<script>"+t+"<\/script>"},p=function(t){t.write(d("")),t.close();var e=t.parentWindow.Object;return t=null,e},v=function(){try{i=new ActiveXObject("htmlfile")}catch(t){}var t,e;v="undefined"!=typeof document?document.domain&&i?p(i):((e=c("iframe")).style.display="none",u.appendChild(e),e.src=String("javascript:"),(t=e.contentWindow.document).open(),t.write(d("document.F=Object")),t.close(),t.F):p(i);for(var n=s.length;n--;)delete v.prototype[s[n]];return v()};a[h]=!0,t.exports=Object.create||function(t,e){var n;return null!==t?(f.prototype=r(t),n=new f,f.prototype=null,n[h]=t):n=v(),void 0===e?n:o(n,e)}},function(t,e,n){"use strict";n(14);var i=n(5),r=n(29),o=n(151),s=n(25),a=n(108),u=n(42),c=n(138),l=n(30),h=n(84),f=n(7),d=n(13),p=n(61),v=n(59),g=n(8),m=n(9),y=n(21),b=n(43),w=n(46),x=n(111),k=n(88),E=n(6),S=r("fetch"),O=r("Request"),T=O&&O.prototype,_=r("Headers"),N=E("iterator"),C=l.set,R=l.getterFor("URLSearchParams"),I=l.getterFor("URLSearchParamsIterator"),A=/\+/g,j=Array(4),L=function(t){return j[t-1]||(j[t-1]=RegExp("((?:%[\\da-f]{2}){"+t+"})","gi"))},P=function(t){try{return decodeURIComponent(t)}catch(e){return t}},D=function(t){var e=t.replace(A," "),n=4;try{return decodeURIComponent(e)}catch(t){for(;n;)e=e.replace(L(n--),P);return e}},M=/[!'()~]|%20/g,z={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+"},B=function(t){return z[t]},q=function(t){return encodeURIComponent(t).replace(M,B)},U=function(t,e){if(e)for(var n,i,r=e.split("&"),o=0;o<r.length;)(n=r[o++]).length&&(i=n.split("="),t.push({key:D(i.shift()),value:D(i.join("="))}))},F=function(t){this.entries.length=0,U(this.entries,t)},W=function(t,e){if(t<e)throw TypeError("Not enough arguments")},H=c((function(t,e){C(this,{type:"URLSearchParamsIterator",iterator:x(R(t).entries),kind:e})}),"Iterator",(function(){var t=I(this),e=t.kind,n=t.iterator.next(),i=n.value;return n.done||(n.value="keys"===e?i.key:"values"===e?i.value:[i.key,i.value]),n})),V=function(){h(this,V,"URLSearchParams");var t,e,n,i,r,o,s,a,u,c=arguments.length>0?arguments[0]:void 0,l=this,f=[];if(C(l,{type:"URLSearchParams",entries:f,updateURL:function(){},updateSearchParams:F}),void 0!==c)if(m(c))if(t=k(c))for(n=(e=x(c,t)).next;!(i=n.call(e)).done;){if((s=(o=(r=x(g(i.value))).next).call(r)).done||(a=o.call(r)).done||!o.call(r).done)throw TypeError("Expected sequence with length 2");f.push({key:y(s.value),value:y(a.value)})}else for(u in c)d(c,u)&&f.push({key:u,value:y(c[u])});else U(f,"string"==typeof c?"?"===c.charAt(0)?c.slice(1):c:y(c))},X=V.prototype;if(a(X,{append:function(t,e){W(arguments.length,2);var n=R(this);n.entries.push({key:y(t),value:y(e)}),n.updateURL()},delete:function(t){W(arguments.length,1);for(var e=R(this),n=e.entries,i=y(t),r=0;r<n.length;)n[r].key===i?n.splice(r,1):r++;e.updateURL()},get:function(t){W(arguments.length,1);for(var e=R(this).entries,n=y(t),i=0;i<e.length;i++)if(e[i].key===n)return e[i].value;return null},getAll:function(t){W(arguments.length,1);for(var e=R(this).entries,n=y(t),i=[],r=0;r<e.length;r++)e[r].key===n&&i.push(e[r].value);return i},has:function(t){W(arguments.length,1);for(var e=R(this).entries,n=y(t),i=0;i<e.length;)if(e[i++].key===n)return!0;return!1},set:function(t,e){W(arguments.length,1);for(var n,i=R(this),r=i.entries,o=!1,s=y(t),a=y(e),u=0;u<r.length;u++)(n=r[u]).key===s&&(o?r.splice(u--,1):(o=!0,n.value=a));o||r.push({key:s,value:a}),i.updateURL()},sort:function(){var t,e,n,i=R(this),r=i.entries,o=r.slice();for(r.length=0,n=0;n<o.length;n++){for(t=o[n],e=0;e<n;e++)if(r[e].key>t.key){r.splice(e,0,t);break}e===n&&r.push(t)}i.updateURL()},forEach:function(t){for(var e,n=R(this).entries,i=p(t,arguments.length>1?arguments[1]:void 0,3),r=0;r<n.length;)i((e=n[r++]).value,e.key,this)},keys:function(){return new H(this,"keys")},values:function(){return new H(this,"values")},entries:function(){return new H(this,"entries")}},{enumerable:!0}),s(X,N,X.entries,{name:"entries"}),s(X,"toString",(function(){for(var t,e=R(this).entries,n=[],i=0;i<e.length;)t=e[i++],n.push(q(t.key)+"="+q(t.value));return n.join("&")}),{enumerable:!0}),u(V,"URLSearchParams"),i({global:!0,forced:!o},{URLSearchParams:V}),!o&&f(_)){var G=function(t){if(m(t)){var e,n=t.body;if("URLSearchParams"===v(n))return(e=t.headers?new _(t.headers):new _).has("content-type")||e.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"),b(t,{body:w(0,String(n)),headers:w(0,e)})}return t};if(f(S)&&i({global:!0,enumerable:!0,forced:!0},{fetch:function(t){return S(t,arguments.length>1?G(arguments[1]):{})}}),f(O)){var Y=function(t){return h(this,Y,"Request"),new O(t,arguments.length>1?G(arguments[1]):{})};T.constructor=Y,Y.prototype=T,i({global:!0,forced:!0},{Request:Y})}}t.exports={URLSearchParams:V,getState:R}},function(t,e,n){var i=n(17),r=n(100),o=n(46),s=n(31),a=n(75),u=n(13),c=n(129),l=Object.getOwnPropertyDescriptor;e.f=i?l:function(t,e){if(t=s(t),e=a(e),c)try{return l(t,e)}catch(t){}if(u(t,e))return o(!r.f.call(t,e),t[e])}},function(t,e){t.exports=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}},function(t,e){var n={}.toString;t.exports=function(t){return n.call(t).slice(8,-1)}},function(t,e,n){var i=n(7),r=n(128);t.exports=function(t){if(i(t))return t;throw TypeError(r(t)+" is not a function")}},function(t,e){t.exports=!1},function(t,e,n){var i=n(17),r=n(13),o=Function.prototype,s=i&&Object.getOwnPropertyDescriptor,a=r(o,"name"),u=a&&"something"===function(){}.name,c=a&&(!i||i&&s(o,"name").configurable);t.exports={EXISTS:a,PROPER:u,CONFIGURABLE:c}},function(t,e,n){var i=n(131),r=n(105).concat("length","prototype");e.f=Object.getOwnPropertyNames||function(t){return i(t,r)}},function(t,e,n){var i=n(41),r=Math.min;t.exports=function(t){return t>0?r(i(t),9007199254740991):0}},function(t,e,n){"use strict";n(18);var i,r=n(5),o=n(17),s=n(151),a=n(4),u=n(135),c=n(25),l=n(84),h=n(13),f=n(152),d=n(208),p=n(113).codeAt,v=n(210),g=n(21),m=n(42),y=n(44),b=n(30),w=a.URL,x=y.URLSearchParams,k=y.getState,E=b.set,S=b.getterFor("URL"),O=Math.floor,T=Math.pow,_=/[a-z]/i,N=/[\d+-.a-z]/i,C=/\d/,R=/^0x/i,I=/^[0-7]+$/,A=/^\d+$/,j=/^[\da-f]+$/i,L=/[\0\t\n\r #%/:<>?@[\\\]^|]/,P=/[\0\t\n\r #/:<>?@[\\\]^|]/,D=/^[\u0000-\u0020]+|[\u0000-\u0020]+$/g,M=/[\t\n\r]/g,z=function(t,e){var n,i,r;if("["==e.charAt(0)){if("]"!=e.charAt(e.length-1))return"Invalid host";if(!(n=q(e.slice(1,-1))))return"Invalid host";t.host=n}else if(Y(t)){if(e=v(e),L.test(e))return"Invalid host";if(null===(n=B(e)))return"Invalid host";t.host=n}else{if(P.test(e))return"Invalid host";for(n="",i=d(e),r=0;r<i.length;r++)n+=X(i[r],F);t.host=n}},B=function(t){var e,n,i,r,o,s,a,u=t.split(".");if(u.length&&""==u[u.length-1]&&u.pop(),(e=u.length)>4)return t;for(n=[],i=0;i<e;i++){if(""==(r=u[i]))return t;if(o=10,r.length>1&&"0"==r.charAt(0)&&(o=R.test(r)?16:8,r=r.slice(8==o?1:2)),""===r)s=0;else{if(!(10==o?A:8==o?I:j).test(r))return t;s=parseInt(r,o)}n.push(s)}for(i=0;i<e;i++)if(s=n[i],i==e-1){if(s>=T(256,5-e))return null}else if(s>255)return null;for(a=n.pop(),i=0;i<n.length;i++)a+=n[i]*T(256,3-i);return a},q=function(t){var e,n,i,r,o,s,a,u=[0,0,0,0,0,0,0,0],c=0,l=null,h=0,f=function(){return t.charAt(h)};if(":"==f()){if(":"!=t.charAt(1))return;h+=2,l=++c}for(;f();){if(8==c)return;if(":"!=f()){for(e=n=0;n<4&&j.test(f());)e=16*e+parseInt(f(),16),h++,n++;if("."==f()){if(0==n)return;if(h-=n,c>6)return;for(i=0;f();){if(r=null,i>0){if(!("."==f()&&i<4))return;h++}if(!C.test(f()))return;for(;C.test(f());){if(o=parseInt(f(),10),null===r)r=o;else{if(0==r)return;r=10*r+o}if(r>255)return;h++}u[c]=256*u[c]+r,2!=++i&&4!=i||c++}if(4!=i)return;break}if(":"==f()){if(h++,!f())return}else if(f())return;u[c++]=e}else{if(null!==l)return;h++,l=++c}}if(null!==l)for(s=c-l,c=7;0!=c&&s>0;)a=u[c],u[c--]=u[l+s-1],u[l+--s]=a;else if(8!=c)return;return u},U=function(t){var e,n,i,r;if("number"==typeof t){for(e=[],n=0;n<4;n++)e.unshift(t%256),t=O(t/256);return e.join(".")}if("object"==typeof t){for(e="",i=function(t){for(var e=null,n=1,i=null,r=0,o=0;o<8;o++)0!==t[o]?(r>n&&(e=i,n=r),i=null,r=0):(null===i&&(i=o),++r);return r>n&&(e=i,n=r),e}(t),n=0;n<8;n++)r&&0===t[n]||(r&&(r=!1),i===n?(e+=n?":":"::",r=!0):(e+=t[n].toString(16),n<7&&(e+=":")));return"["+e+"]"}return t},F={},W=f({},F,{" ":1,'"':1,"<":1,">":1,"`":1}),H=f({},W,{"#":1,"?":1,"{":1,"}":1}),V=f({},H,{"/":1,":":1,";":1,"=":1,"@":1,"[":1,"\\":1,"]":1,"^":1,"|":1}),X=function(t,e){var n=p(t,0);return n>32&&n<127&&!h(e,t)?t:encodeURIComponent(t)},G={ftp:21,file:null,http:80,https:443,ws:80,wss:443},Y=function(t){return h(G,t.scheme)},$=function(t){return""!=t.username||""!=t.password},K=function(t){return!t.host||t.cannotBeABaseURL||"file"==t.scheme},Z=function(t,e){var n;return 2==t.length&&_.test(t.charAt(0))&&(":"==(n=t.charAt(1))||!e&&"|"==n)},J=function(t){var e;return t.length>1&&Z(t.slice(0,2))&&(2==t.length||"/"===(e=t.charAt(2))||"\\"===e||"?"===e||"#"===e)},Q=function(t){var e=t.path,n=e.length;!n||"file"==t.scheme&&1==n&&Z(e[0],!0)||e.pop()},tt=function(t){return"."===t||"%2e"===t.toLowerCase()},et={},nt={},it={},rt={},ot={},st={},at={},ut={},ct={},lt={},ht={},ft={},dt={},pt={},vt={},gt={},mt={},yt={},bt={},wt={},xt={},kt=function(t,e,n,r){var o,s,a,u,c,l=n||et,f=0,p="",v=!1,g=!1,m=!1;for(n||(t.scheme="",t.username="",t.password="",t.host=null,t.port=null,t.path=[],t.query=null,t.fragment=null,t.cannotBeABaseURL=!1,e=e.replace(D,"")),e=e.replace(M,""),o=d(e);f<=o.length;){switch(s=o[f],l){case et:if(!s||!_.test(s)){if(n)return"Invalid scheme";l=it;continue}p+=s.toLowerCase(),l=nt;break;case nt:if(s&&(N.test(s)||"+"==s||"-"==s||"."==s))p+=s.toLowerCase();else{if(":"!=s){if(n)return"Invalid scheme";p="",l=it,f=0;continue}if(n&&(Y(t)!=h(G,p)||"file"==p&&($(t)||null!==t.port)||"file"==t.scheme&&!t.host))return;if(t.scheme=p,n)return void(Y(t)&&G[t.scheme]==t.port&&(t.port=null));p="","file"==t.scheme?l=pt:Y(t)&&r&&r.scheme==t.scheme?l=rt:Y(t)?l=ut:"/"==o[f+1]?(l=ot,f++):(t.cannotBeABaseURL=!0,t.path.push(""),l=bt)}break;case it:if(!r||r.cannotBeABaseURL&&"#"!=s)return"Invalid scheme";if(r.cannotBeABaseURL&&"#"==s){t.scheme=r.scheme,t.path=r.path.slice(),t.query=r.query,t.fragment="",t.cannotBeABaseURL=!0,l=xt;break}l="file"==r.scheme?pt:st;continue;case rt:if("/"!=s||"/"!=o[f+1]){l=st;continue}l=ct,f++;break;case ot:if("/"==s){l=lt;break}l=yt;continue;case st:if(t.scheme=r.scheme,s==i)t.username=r.username,t.password=r.password,t.host=r.host,t.port=r.port,t.path=r.path.slice(),t.query=r.query;else if("/"==s||"\\"==s&&Y(t))l=at;else if("?"==s)t.username=r.username,t.password=r.password,t.host=r.host,t.port=r.port,t.path=r.path.slice(),t.query="",l=wt;else{if("#"!=s){t.username=r.username,t.password=r.password,t.host=r.host,t.port=r.port,t.path=r.path.slice(),t.path.pop(),l=yt;continue}t.username=r.username,t.password=r.password,t.host=r.host,t.port=r.port,t.path=r.path.slice(),t.query=r.query,t.fragment="",l=xt}break;case at:if(!Y(t)||"/"!=s&&"\\"!=s){if("/"!=s){t.username=r.username,t.password=r.password,t.host=r.host,t.port=r.port,l=yt;continue}l=lt}else l=ct;break;case ut:if(l=ct,"/"!=s||"/"!=p.charAt(f+1))continue;f++;break;case ct:if("/"!=s&&"\\"!=s){l=lt;continue}break;case lt:if("@"==s){v&&(p="%40"+p),v=!0,a=d(p);for(var y=0;y<a.length;y++){var b=a[y];if(":"!=b||m){var w=X(b,V);m?t.password+=w:t.username+=w}else m=!0}p=""}else if(s==i||"/"==s||"?"==s||"#"==s||"\\"==s&&Y(t)){if(v&&""==p)return"Invalid authority";f-=d(p).length+1,p="",l=ht}else p+=s;break;case ht:case ft:if(n&&"file"==t.scheme){l=gt;continue}if(":"!=s||g){if(s==i||"/"==s||"?"==s||"#"==s||"\\"==s&&Y(t)){if(Y(t)&&""==p)return"Invalid host";if(n&&""==p&&($(t)||null!==t.port))return;if(u=z(t,p))return u;if(p="",l=mt,n)return;continue}"["==s?g=!0:"]"==s&&(g=!1),p+=s}else{if(""==p)return"Invalid host";if(u=z(t,p))return u;if(p="",l=dt,n==ft)return}break;case dt:if(!C.test(s)){if(s==i||"/"==s||"?"==s||"#"==s||"\\"==s&&Y(t)||n){if(""!=p){var x=parseInt(p,10);if(x>65535)return"Invalid port";t.port=Y(t)&&x===G[t.scheme]?null:x,p=""}if(n)return;l=mt;continue}return"Invalid port"}p+=s;break;case pt:if(t.scheme="file","/"==s||"\\"==s)l=vt;else{if(!r||"file"!=r.scheme){l=yt;continue}if(s==i)t.host=r.host,t.path=r.path.slice(),t.query=r.query;else if("?"==s)t.host=r.host,t.path=r.path.slice(),t.query="",l=wt;else{if("#"!=s){J(o.slice(f).join(""))||(t.host=r.host,t.path=r.path.slice(),Q(t)),l=yt;continue}t.host=r.host,t.path=r.path.slice(),t.query=r.query,t.fragment="",l=xt}}break;case vt:if("/"==s||"\\"==s){l=gt;break}r&&"file"==r.scheme&&!J(o.slice(f).join(""))&&(Z(r.path[0],!0)?t.path.push(r.path[0]):t.host=r.host),l=yt;continue;case gt:if(s==i||"/"==s||"\\"==s||"?"==s||"#"==s){if(!n&&Z(p))l=yt;else if(""==p){if(t.host="",n)return;l=mt}else{if(u=z(t,p))return u;if("localhost"==t.host&&(t.host=""),n)return;p="",l=mt}continue}p+=s;break;case mt:if(Y(t)){if(l=yt,"/"!=s&&"\\"!=s)continue}else if(n||"?"!=s)if(n||"#"!=s){if(s!=i&&(l=yt,"/"!=s))continue}else t.fragment="",l=xt;else t.query="",l=wt;break;case yt:if(s==i||"/"==s||"\\"==s&&Y(t)||!n&&("?"==s||"#"==s)){if(".."===(c=(c=p).toLowerCase())||"%2e."===c||".%2e"===c||"%2e%2e"===c?(Q(t),"/"==s||"\\"==s&&Y(t)||t.path.push("")):tt(p)?"/"==s||"\\"==s&&Y(t)||t.path.push(""):("file"==t.scheme&&!t.path.length&&Z(p)&&(t.host&&(t.host=""),p=p.charAt(0)+":"),t.path.push(p)),p="","file"==t.scheme&&(s==i||"?"==s||"#"==s))for(;t.path.length>1&&""===t.path[0];)t.path.shift();"?"==s?(t.query="",l=wt):"#"==s&&(t.fragment="",l=xt)}else p+=X(s,H);break;case bt:"?"==s?(t.query="",l=wt):"#"==s?(t.fragment="",l=xt):s!=i&&(t.path[0]+=X(s,F));break;case wt:n||"#"!=s?s!=i&&("'"==s&&Y(t)?t.query+="%27":t.query+="#"==s?"%23":X(s,F)):(t.fragment="",l=xt);break;case xt:s!=i&&(t.fragment+=X(s,W))}f++}},Et=function(t){var e,n,i=l(this,Et,"URL"),r=arguments.length>1?arguments[1]:void 0,s=g(t),a=E(i,{type:"URL"});if(void 0!==r)if(r instanceof Et)e=S(r);else if(n=kt(e={},g(r)))throw TypeError(n);if(n=kt(a,s,null,e))throw TypeError(n);var u=a.searchParams=new x,c=k(u);c.updateSearchParams(a.query),c.updateURL=function(){a.query=String(u)||null},o||(i.href=Ot.call(i),i.origin=Tt.call(i),i.protocol=_t.call(i),i.username=Nt.call(i),i.password=Ct.call(i),i.host=Rt.call(i),i.hostname=It.call(i),i.port=At.call(i),i.pathname=jt.call(i),i.search=Lt.call(i),i.searchParams=Pt.call(i),i.hash=Dt.call(i))},St=Et.prototype,Ot=function(){var t=S(this),e=t.scheme,n=t.username,i=t.password,r=t.host,o=t.port,s=t.path,a=t.query,u=t.fragment,c=e+":";return null!==r?(c+="//",$(t)&&(c+=n+(i?":"+i:"")+"@"),c+=U(r),null!==o&&(c+=":"+o)):"file"==e&&(c+="//"),c+=t.cannotBeABaseURL?s[0]:s.length?"/"+s.join("/"):"",null!==a&&(c+="?"+a),null!==u&&(c+="#"+u),c},Tt=function(){var t=S(this),e=t.scheme,n=t.port;if("blob"==e)try{return new Et(e.path[0]).origin}catch(t){return"null"}return"file"!=e&&Y(t)?e+"://"+U(t.host)+(null!==n?":"+n:""):"null"},_t=function(){return S(this).scheme+":"},Nt=function(){return S(this).username},Ct=function(){return S(this).password},Rt=function(){var t=S(this),e=t.host,n=t.port;return null===e?"":null===n?U(e):U(e)+":"+n},It=function(){var t=S(this).host;return null===t?"":U(t)},At=function(){var t=S(this).port;return null===t?"":String(t)},jt=function(){var t=S(this),e=t.path;return t.cannotBeABaseURL?e[0]:e.length?"/"+e.join("/"):""},Lt=function(){var t=S(this).query;return t?"?"+t:""},Pt=function(){return S(this).searchParams},Dt=function(){var t=S(this).fragment;return t?"#"+t:""},Mt=function(t,e){return{get:t,set:e,configurable:!0,enumerable:!0}};if(o&&u(St,{href:Mt(Ot,(function(t){var e=S(this),n=g(t),i=kt(e,n);if(i)throw TypeError(i);k(e.searchParams).updateSearchParams(e.query)})),origin:Mt(Tt),protocol:Mt(_t,(function(t){var e=S(this);kt(e,g(t)+":",et)})),username:Mt(Nt,(function(t){var e=S(this),n=d(g(t));if(!K(e)){e.username="";for(var i=0;i<n.length;i++)e.username+=X(n[i],V)}})),password:Mt(Ct,(function(t){var e=S(this),n=d(g(t));if(!K(e)){e.password="";for(var i=0;i<n.length;i++)e.password+=X(n[i],V)}})),host:Mt(Rt,(function(t){var e=S(this);e.cannotBeABaseURL||kt(e,g(t),ht)})),hostname:Mt(It,(function(t){var e=S(this);e.cannotBeABaseURL||kt(e,g(t),ft)})),port:Mt(At,(function(t){var e=S(this);K(e)||(""==(t=g(t))?e.port=null:kt(e,t,dt))})),pathname:Mt(jt,(function(t){var e=S(this);e.cannotBeABaseURL||(e.path=[],kt(e,g(t),mt))})),search:Mt(Lt,(function(t){var e=S(this);""==(t=g(t))?e.query=null:("?"==t.charAt(0)&&(t=t.slice(1)),e.query="",kt(e,t,wt)),k(e.searchParams).updateSearchParams(e.query)})),searchParams:Mt(Pt),hash:Mt(Dt,(function(t){var e=S(this);""!=(t=g(t))?("#"==t.charAt(0)&&(t=t.slice(1)),e.fragment="",kt(e,t,xt)):e.fragment=null}))}),c(St,"toJSON",(function(){return Ot.call(this)}),{enumerable:!0}),c(St,"toString",(function(){return Ot.call(this)}),{enumerable:!0}),w){var zt=w.createObjectURL,Bt=w.revokeObjectURL;zt&&c(Et,"createObjectURL",(function(t){return zt.apply(w,arguments)})),Bt&&c(Et,"revokeObjectURL",(function(t){return Bt.apply(w,arguments)}))}m(Et,"URL"),r({global:!0,forced:!s,sham:!o},{URL:Et})},function(t,e,n){"use strict";n(10),n(27);var i=n(0);function r(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function o(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}var s=function(){function t(e){r(this,t),this._q=[],this.context=e,this.tick=i.requestAnimationFrame,this.running=!1,this.paused=!1}var e,n,s;return e=t,(n=[{key:"enqueue",value:function(){var t,e,n=[].shift.call(arguments),r=arguments;if(!n)throw new Error("No Task Provided");return e="function"==typeof n?{task:n,args:r,deferred:t=new i.defer,promise:t.promise}:{promise:n},this._q.push(e),0!=this.paused||this.running||this.run(),e.promise}},{key:"dequeue",value:function(){var t,e,n;return!this._q.length||this.paused?((t=new i.defer).deferred.resolve(),t.promise):(e=(t=this._q.shift()).task)?(n=e.apply(this.context,t.args))&&"function"==typeof n.then?n.then(function(){t.deferred.resolve.apply(this.context,arguments)}.bind(this),function(){t.deferred.reject.apply(this.context,arguments)}.bind(this)):(t.deferred.resolve.apply(this.context,n),t.promise):t.promise?t.promise:void 0}},{key:"dump",value:function(){for(;this._q.length;)this.dequeue()}},{key:"run",value:function(){var t=this;return this.running||(this.running=!0,this.defered=new i.defer),this.tick.call(window,(function(){t._q.length?t.dequeue().then(function(){this.run()}.bind(t)):(t.defered.resolve(),t.running=void 0)})),1==this.paused&&(this.paused=!1),this.defered.promise}},{key:"flush",value:function(){return this.running?this.running:this._q.length?(this.running=this.dequeue().then(function(){return this.running=void 0,this.flush()}.bind(this)),this.running):void 0}},{key:"clear",value:function(){this._q=[]}},{key:"length",value:function(){return this._q.length}},{key:"pause",value:function(){this.paused=!0}},{key:"stop",value:function(){this._q=[],this.running=!1,this.paused=!0}}])&&o(e.prototype,n),s&&o(e,s),t}();e.a=s},function(t,e,n){"use strict";n(39),n(163),n(164),n(161),n(38),n(11),n(67),n(68),n(10),n(69),n(14),n(18),n(22);var i=n(12),r=n.n(i),o=n(0);function s(){var t="reverse",e=function(){var t=document.createElement("div");t.dir="rtl",t.style.position="fixed",t.style.width="1px",t.style.height="1px",t.style.top="0px",t.style.left="0px",t.style.overflow="hidden";var e=document.createElement("div");e.style.width="2px";var n=document.createElement("span");n.style.width="1px",n.style.display="inline-block";var i=document.createElement("span");return i.style.width="1px",i.style.display="inline-block",e.appendChild(n),e.appendChild(i),t.appendChild(e),t}();return document.body.appendChild(e),e.scrollLeft>0?t="default":"undefined"!=typeof Element&&Element.prototype.scrollIntoView?(e.children[0].children[1].scrollIntoView(),e.scrollLeft<0&&(t="negative")):(e.scrollLeft=1,0===e.scrollLeft&&(t="negative")),document.body.removeChild(e),t}var a=n(56),u=n(54),c=n(167),l=n.n(c);function h(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}var f=function(){function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.settings=e||{},this.id="epubjs-container-"+Object(o.uuid)(),this.container=this.create(this.settings),this.settings.hidden&&(this.wrapper=this.wrap(this.container))}var e,n,i;return e=t,(n=[{key:"create",value:function(t){var e=t.height,n=t.width,i=t.overflow||!1,r=t.axis||"vertical",s=t.direction;Object(o.extend)(this.settings,t),t.height&&Object(o.isNumber)(t.height)&&(e=t.height+"px"),t.width&&Object(o.isNumber)(t.width)&&(n=t.width+"px");var a=document.createElement("div");return a.id=this.id,a.classList.add("epub-container"),a.style.wordSpacing="0",a.style.lineHeight="0",a.style.verticalAlign="top",a.style.position="relative","horizontal"===r&&(a.style.display="flex",a.style.flexDirection="row",a.style.flexWrap="nowrap"),n&&(a.style.width=n),e&&(a.style.height=e),i&&("scroll"===i&&"vertical"===r?(a.style["overflow-y"]=i,a.style["overflow-x"]="hidden"):"scroll"===i&&"horizontal"===r?(a.style["overflow-y"]="hidden",a.style["overflow-x"]=i):a.style.overflow=i),s&&(a.dir=s,a.style.direction=s),s&&this.settings.fullsize&&(document.body.style.direction=s),a}},{key:"wrap",value:function(t){var e=document.createElement("div");return e.style.visibility="hidden",e.style.overflow="hidden",e.style.width="0",e.style.height="0",e.appendChild(t),e}},{key:"getElement",value:function(t){var e;if(Object(o.isElement)(t)?e=t:"string"==typeof t&&(e=document.getElementById(t)),!e)throw new Error("Not an Element");return e}},{key:"attachTo",value:function(t){var e,n=this.getElement(t);if(n)return e=this.settings.hidden?this.wrapper:this.container,n.appendChild(e),this.element=n,n}},{key:"getContainer",value:function(){return this.container}},{key:"onResize",value:function(t){Object(o.isNumber)(this.settings.width)&&Object(o.isNumber)(this.settings.height)||(this.resizeFunc=l()(t,50),window.addEventListener("resize",this.resizeFunc,!1))}},{key:"onOrientationChange",value:function(t){this.orientationChangeFunc=t,window.addEventListener("orientationchange",this.orientationChangeFunc,!1)}},{key:"size",value:function(t,e){var n,i=t||this.settings.width,r=e||this.settings.height;null===t?(n=this.element.getBoundingClientRect()).width&&(t=Math.floor(n.width),this.container.style.width=t+"px"):Object(o.isNumber)(t)?this.container.style.width=t+"px":this.container.style.width=t,null===e?(n=n||this.element.getBoundingClientRect()).height&&(e=n.height,this.container.style.height=e+"px"):Object(o.isNumber)(e)?this.container.style.height=e+"px":this.container.style.height=e,Object(o.isNumber)(t)||(t=this.container.clientWidth),Object(o.isNumber)(e)||(e=this.container.clientHeight),this.containerStyles=window.getComputedStyle(this.container),this.containerPadding={left:parseFloat(this.containerStyles["padding-left"])||0,right:parseFloat(this.containerStyles["padding-right"])||0,top:parseFloat(this.containerStyles["padding-top"])||0,bottom:parseFloat(this.containerStyles["padding-bottom"])||0};var s=Object(o.windowBounds)(),a=window.getComputedStyle(document.body),u=parseFloat(a["padding-left"])||0,c=parseFloat(a["padding-right"])||0,l=parseFloat(a["padding-top"])||0,h=parseFloat(a["padding-bottom"])||0;return i||(t=s.width-u-c),(this.settings.fullsize&&!r||!r)&&(e=s.height-l-h),{width:t-this.containerPadding.left-this.containerPadding.right,height:e-this.containerPadding.top-this.containerPadding.bottom}}},{key:"bounds",value:function(){var t;return"visible"!==this.container.style.overflow&&(t=this.container&&this.container.getBoundingClientRect()),t&&t.width&&t.height?t:Object(o.windowBounds)()}},{key:"getSheet",value:function(){var t=document.createElement("style");return t.appendChild(document.createTextNode("")),document.head.appendChild(t),t.sheet}},{key:"addStyleRules",value:function(t,e){var n="#"+this.id+" ",i="";this.sheet||(this.sheet=this.getSheet()),e.forEach((function(t){for(var e in t)t.hasOwnProperty(e)&&(i+=e+":"+t[e]+";")})),this.sheet.insertRule(n+t+" {"+i+"}",0)}},{key:"axis",value:function(t){"horizontal"===t?(this.container.style.display="flex",this.container.style.flexDirection="row",this.container.style.flexWrap="nowrap"):this.container.style.display="block",this.settings.axis=t}},{key:"direction",value:function(t){this.container&&(this.container.dir=t,this.container.style.direction=t),this.settings.fullsize&&(document.body.style.direction=t),this.settings.dir=t}},{key:"overflow",value:function(t){this.container&&("scroll"===t&&"vertical"===this.settings.axis?(this.container.style["overflow-y"]=t,this.container.style["overflow-x"]="hidden"):"scroll"===t&&"horizontal"===this.settings.axis?(this.container.style["overflow-y"]="hidden",this.container.style["overflow-x"]=t):this.container.style.overflow=t),this.settings.overflow=t}},{key:"destroy",value:function(){this.element&&(this.settings.hidden?this.wrapper:this.container,this.element.contains(this.container)&&this.element.removeChild(this.container),window.removeEventListener("resize",this.resizeFunc),window.removeEventListener("orientationChange",this.orientationChangeFunc))}}])&&h(e.prototype,n),i&&h(e,i),t}();n(35),n(95);function d(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}var p=function(){function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.container=e,this._views=[],this.length=0,this.hidden=!1}var e,n,i;return e=t,(n=[{key:"all",value:function(){return this._views}},{key:"first",value:function(){return this._views[0]}},{key:"last",value:function(){return this._views[this._views.length-1]}},{key:"indexOf",value:function(t){return this._views.indexOf(t)}},{key:"slice",value:function(){return this._views.slice.apply(this._views,arguments)}},{key:"get",value:function(t){return this._views[t]}},{key:"append",value:function(t){return this._views.push(t),this.container&&this.container.appendChild(t.element),this.length++,t}},{key:"prepend",value:function(t){return this._views.unshift(t),this.container&&this.container.insertBefore(t.element,this.container.firstChild),this.length++,t}},{key:"insert",value:function(t,e){return this._views.splice(e,0,t),this.container&&(e<this.container.children.length?this.container.insertBefore(t.element,this.container.children[e]):this.container.appendChild(t.element)),this.length++,t}},{key:"remove",value:function(t){var e=this._views.indexOf(t);e>-1&&this._views.splice(e,1),this.destroy(t),this.length--}},{key:"destroy",value:function(t){t.displayed&&t.destroy(),this.container&&this.container.removeChild(t.element),t=null}},{key:"forEach",value:function(){return this._views.forEach.apply(this._views,arguments)}},{key:"clear",value:function(){var t,e=this.length;if(this.length){for(var n=0;n<e;n++)t=this._views[n],this.destroy(t);this._views=[],this.length=0}}},{key:"find",value:function(t){for(var e,n=this.length,i=0;i<n;i++)if((e=this._views[i]).displayed&&e.section.index==t.index)return e}},{key:"displayed",value:function(){for(var t,e=[],n=this.length,i=0;i<n;i++)(t=this._views[i]).displayed&&e.push(t);return e}},{key:"show",value:function(){for(var t,e=this.length,n=0;n<e;n++)(t=this._views[n]).displayed&&t.show();this.hidden=!1}},{key:"hide",value:function(){for(var t,e=this.length,n=0;n<e;n++)(t=this._views[n]).displayed&&t.hide();this.hidden=!0}}])&&d(e.prototype,n),i&&d(e,i),t}(),v=n(1);function g(t){return(g="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function m(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}var y=function(){function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.name="default",this.optsSettings=e.settings,this.View=e.view,this.request=e.request,this.renditionQueue=e.queue,this.q=new u.a(this),this.settings=Object(o.extend)(this.settings||{},{infinite:!0,hidden:!1,width:void 0,height:void 0,axis:void 0,writingMode:void 0,flow:"scrolled",ignoreClass:"",fullsize:void 0,allowScriptedContent:!1,allowPopups:!1}),Object(o.extend)(this.settings,e.settings||{}),this.viewSettings={ignoreClass:this.settings.ignoreClass,axis:this.settings.axis,flow:this.settings.flow,layout:this.layout,method:this.settings.method,width:0,height:0,forceEvenPages:!0,allowScriptedContent:this.settings.allowScriptedContent,allowPopups:this.settings.allowPopups},this.rendered=!1}var e,n,i;return e=t,(n=[{key:"render",value:function(t,e){var n=t.tagName;void 0!==this.settings.fullsize||!n||"body"!=n.toLowerCase()&&"html"!=n.toLowerCase()||(this.settings.fullsize=!0),this.settings.fullsize&&(this.settings.overflow="visible",this.overflow=this.settings.overflow),this.settings.size=e,this.settings.rtlScrollType=s(),this.stage=new f({width:e.width,height:e.height,overflow:this.overflow,hidden:this.settings.hidden,axis:this.settings.axis,fullsize:this.settings.fullsize,direction:this.settings.direction}),this.stage.attachTo(t),this.container=this.stage.getContainer(),this.views=new p(this.container),this._bounds=this.bounds(),this._stageSize=this.stage.size(),this.viewSettings.width=this._stageSize.width,this.viewSettings.height=this._stageSize.height,this.stage.onResize(this.onResized.bind(this)),this.stage.onOrientationChange(this.onOrientationChange.bind(this)),this.addEventListeners(),this.layout&&this.updateLayout(),this.rendered=!0}},{key:"addEventListeners",value:function(){var t;window.addEventListener("unload",function(t){this.destroy()}.bind(this)),t=this.settings.fullsize?window:this.container,this._onScroll=this.onScroll.bind(this),t.addEventListener("scroll",this._onScroll)}},{key:"removeEventListeners",value:function(){(this.settings.fullsize?window:this.container).removeEventListener("scroll",this._onScroll),this._onScroll=void 0}},{key:"destroy",value:function(){clearTimeout(this.orientationTimeout),clearTimeout(this.resizeTimeout),clearTimeout(this.afterScrolled),this.clear(),this.removeEventListeners(),this.stage.destroy(),this.rendered=!1}},{key:"onOrientationChange",value:function(t){var e=window.orientation;this.optsSettings.resizeOnOrientationChange&&this.resize(),clearTimeout(this.orientationTimeout),this.orientationTimeout=setTimeout(function(){this.orientationTimeout=void 0,this.optsSettings.resizeOnOrientationChange&&this.resize(),this.emit(v.c.MANAGERS.ORIENTATION_CHANGE,e)}.bind(this),500)}},{key:"onResized",value:function(t){this.resize()}},{key:"resize",value:function(t,e,n){var i=this.stage.size(t,e);this.winBounds=Object(o.windowBounds)(),this.orientationTimeout&&this.winBounds.width===this.winBounds.height?this._stageSize=void 0:this._stageSize&&this._stageSize.width===i.width&&this._stageSize.height===i.height||(this._stageSize=i,this._bounds=this.bounds(),this.clear(),this.viewSettings.width=this._stageSize.width,this.viewSettings.height=this._stageSize.height,this.updateLayout(),this.emit(v.c.MANAGERS.RESIZED,{width:this._stageSize.width,height:this._stageSize.height},n))}},{key:"createView",value:function(t,e){return new this.View(t,Object(o.extend)(this.viewSettings,{forceRight:e}))}},{key:"handleNextPrePaginated",value:function(t,e,n){var i;if("pre-paginated"===this.layout.name&&this.layout.divisor>1){if(t||0===e.index)return;if((i=e.next())&&!i.properties.includes("page-spread-left"))return n.call(this,i)}}},{key:"display",value:function(t,e){var n=new o.defer,i=n.promise;(e===t.href||Object(o.isNumber)(e))&&(e=void 0);var r=this.views.find(t);if(r&&t&&"pre-paginated"!==this.layout.name){var s=r.offset();if("ltr"===this.settings.direction)this.scrollTo(s.left,s.top,!0);else{var a=r.width();this.scrollTo(s.left+a,s.top,!0)}if(e){var u=r.locationOf(e),c=r.width();this.moveTo(u,c)}return n.resolve(),i}this.clear();var l=!1;return"pre-paginated"===this.layout.name&&2===this.layout.divisor&&t.properties.includes("page-spread-right")&&(l=!0),this.add(t,l).then(function(t){if(e){var n=t.locationOf(e),i=t.width();this.moveTo(n,i)}}.bind(this),(function(t){n.reject(t)})).then(function(){return this.handleNextPrePaginated(l,t,this.add)}.bind(this)).then(function(){this.views.show(),n.resolve()}.bind(this)),i}},{key:"afterDisplayed",value:function(t){this.emit(v.c.MANAGERS.ADDED,t)}},{key:"afterResized",value:function(t){this.emit(v.c.MANAGERS.RESIZE,t.section)}},{key:"moveTo",value:function(t,e){var n=0,i=0;this.isPaginated?((n=Math.floor(t.left/this.layout.delta)*this.layout.delta)+this.layout.delta>this.container.scrollWidth&&(n=this.container.scrollWidth-this.layout.delta),(i=Math.floor(t.top/this.layout.delta)*this.layout.delta)+this.layout.delta>this.container.scrollHeight&&(i=this.container.scrollHeight-this.layout.delta)):i=t.top,"rtl"===this.settings.direction&&(n+=this.layout.delta,n-=e),this.scrollTo(n,i,!0)}},{key:"add",value:function(t,e){var n=this,i=this.createView(t,e);return this.views.append(i),i.onDisplayed=this.afterDisplayed.bind(this),i.onResize=this.afterResized.bind(this),i.on(v.c.VIEWS.AXIS,(function(t){n.updateAxis(t)})),i.on(v.c.VIEWS.WRITING_MODE,(function(t){n.updateWritingMode(t)})),i.display(this.request)}},{key:"append",value:function(t,e){var n=this,i=this.createView(t,e);return this.views.append(i),i.onDisplayed=this.afterDisplayed.bind(this),i.onResize=this.afterResized.bind(this),i.on(v.c.VIEWS.AXIS,(function(t){n.updateAxis(t)})),i.on(v.c.VIEWS.WRITING_MODE,(function(t){n.updateWritingMode(t)})),i.display(this.request)}},{key:"prepend",value:function(t,e){var n=this,i=this.createView(t,e);return i.on(v.c.VIEWS.RESIZED,(function(t){n.counter(t)})),this.views.prepend(i),i.onDisplayed=this.afterDisplayed.bind(this),i.onResize=this.afterResized.bind(this),i.on(v.c.VIEWS.AXIS,(function(t){n.updateAxis(t)})),i.on(v.c.VIEWS.WRITING_MODE,(function(t){n.updateWritingMode(t)})),i.display(this.request)}},{key:"counter",value:function(t){"vertical"===this.settings.axis?this.scrollBy(0,t.heightDelta,!0):this.scrollBy(t.widthDelta,0,!0)}},{key:"next",value:function(){var t,e=this.settings.direction;if(this.views.length&&(!this.isPaginated||"horizontal"!==this.settings.axis||e&&"ltr"!==e?this.isPaginated&&"horizontal"===this.settings.axis&&"rtl"===e?(this.scrollLeft=this.container.scrollLeft,"default"===this.settings.rtlScrollType?this.container.scrollLeft>0?this.scrollBy(this.layout.delta,0,!0):t=this.views.last().section.next():this.container.scrollLeft+-1*this.layout.delta>-1*this.container.scrollWidth?this.scrollBy(this.layout.delta,0,!0):t=this.views.last().section.next()):this.isPaginated&&"vertical"===this.settings.axis?(this.scrollTop=this.container.scrollTop,this.container.scrollTop+this.container.offsetHeight<this.container.scrollHeight?this.scrollBy(0,this.layout.height,!0):t=this.views.last().section.next()):t=this.views.last().section.next():(this.scrollLeft=this.container.scrollLeft,this.container.scrollLeft+this.container.offsetWidth+this.layout.delta<=this.container.scrollWidth?this.scrollBy(this.layout.delta,0,!0):t=this.views.last().section.next()),t)){this.clear(),this.updateLayout();var n=!1;return"pre-paginated"===this.layout.name&&2===this.layout.divisor&&t.properties.includes("page-spread-right")&&(n=!0),this.append(t,n).then(function(){return this.handleNextPrePaginated(n,t,this.append)}.bind(this),(function(t){return t})).then(function(){this.isPaginated||"horizontal"!==this.settings.axis||"rtl"!==this.settings.direction||"default"!==this.settings.rtlScrollType||this.scrollTo(this.container.scrollWidth,0,!0),this.views.show()}.bind(this))}}},{key:"prev",value:function(){var t,e=this.settings.direction;if(this.views.length&&(!this.isPaginated||"horizontal"!==this.settings.axis||e&&"ltr"!==e?this.isPaginated&&"horizontal"===this.settings.axis&&"rtl"===e?(this.scrollLeft=this.container.scrollLeft,"default"===this.settings.rtlScrollType?this.container.scrollLeft+this.container.offsetWidth<this.container.scrollWidth?this.scrollBy(-this.layout.delta,0,!0):t=this.views.first().section.prev():this.container.scrollLeft<0?this.scrollBy(-this.layout.delta,0,!0):t=this.views.first().section.prev()):this.isPaginated&&"vertical"===this.settings.axis?(this.scrollTop=this.container.scrollTop,this.container.scrollTop>0?this.scrollBy(0,-this.layout.height,!0):t=this.views.first().section.prev()):t=this.views.first().section.prev():(this.scrollLeft=this.container.scrollLeft,this.container.scrollLeft>0?this.scrollBy(-this.layout.delta,0,!0):t=this.views.first().section.prev()),t)){this.clear(),this.updateLayout();var n=!1;return"pre-paginated"===this.layout.name&&2===this.layout.divisor&&"object"!==g(t.prev())&&(n=!0),this.prepend(t,n).then(function(){var e;if("pre-paginated"===this.layout.name&&this.layout.divisor>1&&(e=t.prev()))return this.prepend(e)}.bind(this),(function(t){return t})).then(function(){this.isPaginated&&"horizontal"===this.settings.axis&&("rtl"===this.settings.direction?"default"===this.settings.rtlScrollType?this.scrollTo(0,0,!0):this.scrollTo(-1*this.container.scrollWidth+this.layout.delta,0,!0):this.scrollTo(this.container.scrollWidth-this.layout.delta,0,!0)),this.views.show()}.bind(this))}}},{key:"current",value:function(){var t=this.visible();return t.length?t[t.length-1]:null}},{key:"clear",value:function(){this.views&&(this.views.hide(),this.scrollTo(0,0,!0),this.views.clear())}},{key:"currentLocation",value:function(){return this.updateLayout(),this.isPaginated&&"horizontal"===this.settings.axis?this.location=this.paginatedLocation():this.location=this.scrolledLocation(),this.location}},{key:"scrolledLocation",value:function(){var t=this,e=this.visible(),n=this.container.getBoundingClientRect(),i=n.height<window.innerHeight?n.height:window.innerHeight,r=n.width<window.innerWidth?n.width:window.innerWidth,o="vertical"===this.settings.axis,s=(this.settings.direction,0);return this.settings.fullsize&&(s=o?window.scrollY:window.scrollX),e.map((function(e){var a,u,c,l,h=e.section,f=h.index,d=h.href,p=e.position(),v=e.width(),g=e.height();o?(u=(a=s+n.top-p.top+0)+i-0,l=t.layout.count(g,i).pages,c=i):(u=(a=s+n.left-p.left+0)+r-0,l=t.layout.count(v,r).pages,c=r);var m=Math.ceil(a/c),y=[],b=Math.ceil(u/c);if("rtl"===t.settings.direction&&!o){var w=m;m=l-b,b=l-w}y=[];for(var x=m;x<=b;x++){var k=x+1;y.push(k)}return{index:f,href:d,pages:y,totalPages:l,mapping:t.mapping.page(e.contents,e.section.cfiBase,a,u)}}))}},{key:"paginatedLocation",value:function(){var t=this,e=this.visible(),n=this.container.getBoundingClientRect(),i=0,r=0;return this.settings.fullsize&&(i=window.scrollX),e.map((function(e){var o,s,a,u,c=e.section,l=c.index,h=c.href,f=e.position(),d=e.width();"rtl"===t.settings.direction?(o=n.right-i,u=Math.min(Math.abs(o-f.left),t.layout.width)-r,s=(a=f.width-(f.right-o)-r)-u):(o=n.left+i,u=Math.min(f.right-o,t.layout.width)-r,a=(s=o-f.left+r)+u),r+=u;var p=t.mapping.page(e.contents,e.section.cfiBase,s,a),v=t.layout.count(d).pages,g=Math.floor(s/t.layout.pageWidth),m=[],y=Math.floor(a/t.layout.pageWidth);if(g<0&&(g=0,y+=1),"rtl"===t.settings.direction){var b=g;g=v-y,y=v-b}for(var w=g+1;w<=y;w++){var x=w;m.push(x)}return{index:l,href:h,pages:m,totalPages:v,mapping:p}}))}},{key:"isVisible",value:function(t,e,n,i){var r=t.position(),o=i||this.bounds();return"horizontal"===this.settings.axis&&r.right>o.left-e&&r.left<o.right+n||"vertical"===this.settings.axis&&r.bottom>o.top-e&&r.top<o.bottom+n}},{key:"visible",value:function(){for(var t,e=this.bounds(),n=this.views.displayed(),i=n.length,r=[],o=0;o<i;o++)t=n[o],!0===this.isVisible(t,0,0,e)&&r.push(t);return r}},{key:"scrollBy",value:function(t,e,n){var i="rtl"===this.settings.direction?-1:1;n&&(this.ignore=!0),this.settings.fullsize?window.scrollBy(t*i,e*i):(t&&(this.container.scrollLeft+=t*i),e&&(this.container.scrollTop+=e)),this.scrolled=!0}},{key:"scrollTo",value:function(t,e,n){n&&(this.ignore=!0),this.settings.fullsize?window.scrollTo(t,e):(this.container.scrollLeft=t,this.container.scrollTop=e),this.scrolled=!0}},{key:"onScroll",value:function(){var t,e;this.settings.fullsize?(t=window.scrollY,e=window.scrollX):(t=this.container.scrollTop,e=this.container.scrollLeft),this.scrollTop=t,this.scrollLeft=e,this.ignore?this.ignore=!1:(this.emit(v.c.MANAGERS.SCROLL,{top:t,left:e}),clearTimeout(this.afterScrolled),this.afterScrolled=setTimeout(function(){this.emit(v.c.MANAGERS.SCROLLED,{top:this.scrollTop,left:this.scrollLeft})}.bind(this),20))}},{key:"bounds",value:function(){var t;return t=this.stage.bounds(),t}},{key:"applyLayout",value:function(t){this.layout=t,this.updateLayout(),this.views&&this.views.length>0&&"pre-paginated"===this.layout.name&&this.display(this.views.first().section)}},{key:"updateLayout",value:function(){this.stage&&(this._stageSize=this.stage.size(),this.isPaginated?(this.layout.calculate(this._stageSize.width,this._stageSize.height,this.settings.gap),this.settings.offset=this.layout.delta/this.layout.divisor):this.layout.calculate(this._stageSize.width,this._stageSize.height),this.viewSettings.width=this.layout.width,this.viewSettings.height=this.layout.height,this.setLayout(this.layout))}},{key:"setLayout",value:function(t){this.viewSettings.layout=t,this.mapping=new a.a(t.props,this.settings.direction,this.settings.axis),this.views&&this.views.forEach((function(e){e&&e.setLayout(t)}))}},{key:"updateWritingMode",value:function(t){this.writingMode=t}},{key:"updateAxis",value:function(t,e){(e||t!==this.settings.axis)&&(this.settings.axis=t,this.stage&&this.stage.axis(t),this.viewSettings.axis=t,this.mapping&&(this.mapping=new a.a(this.layout.props,this.settings.direction,this.settings.axis)),this.layout&&("vertical"===t?this.layout.spread("none"):this.layout.spread(this.layout.settings.spread)))}},{key:"updateFlow",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"auto",n="paginated"===t||"auto"===t;this.isPaginated=n,"scrolled-doc"===t||"scrolled-continuous"===t||"scrolled"===t?this.updateAxis("vertical"):this.updateAxis("horizontal"),this.viewSettings.flow=t,this.settings.overflow?this.overflow=this.settings.overflow:this.overflow=n?"hidden":e,this.stage&&this.stage.overflow(this.overflow),this.updateLayout()}},{key:"getContents",value:function(){var t=[];return this.views?(this.views.forEach((function(e){var n=e&&e.contents;n&&t.push(n)})),t):t}},{key:"direction",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"ltr";this.settings.direction=t,this.stage&&this.stage.direction(t),this.viewSettings.direction=t,this.updateLayout()}},{key:"isRendered",value:function(){return this.rendered}}])&&m(e.prototype,n),i&&m(e,i),t}();r()(y.prototype);e.a=y},function(t,e,n){"use strict";n(97),n(10),n(34);var i=n(2),r=n(0);function o(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function s(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}var a=function(){function t(e,n,i){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];o(this,t),this.layout=e,this.horizontal="horizontal"===i,this.direction=n||"ltr",this._dev=r}var e,n,a;return e=t,(n=[{key:"section",value:function(t){var e=this.findRanges(t);return this.rangeListToCfiList(t.section.cfiBase,e)}},{key:"page",value:function(t,e,n,r){var o,s=!(!t||!t.document)&&t.document.body;if(s){if(o=this.rangePairToCfiPair(e,{start:this.findStart(s,n,r),end:this.findEnd(s,n,r)}),!0===this._dev){var a=t.document,u=new i.a(o.start).toRange(a),c=new i.a(o.end).toRange(a),l=a.defaultView.getSelection(),h=a.createRange();l.removeAllRanges(),h.setStart(u.startContainer,u.startOffset),h.setEnd(c.endContainer,c.endOffset),l.addRange(h)}return o}}},{key:"walk",value:function(t,e){if(!t||t.nodeType!==Node.TEXT_NODE){var n=function(t){return t.data.trim().length>0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_REJECT},i=n;i.acceptNode=n;for(var r,o,s=document.createTreeWalker(t,NodeFilter.SHOW_TEXT,i,!1);(r=s.nextNode())&&!(o=e(r)););return o}}},{key:"findRanges",value:function(t){for(var e,n,i=[],r=t.contents.scrollWidth(),o=Math.ceil(r/this.layout.spreadWidth)*this.layout.divisor,s=this.layout.columnWidth,a=this.layout.gap,u=0;u<o.pages;u++)e=(s+a)*u,n=s*(u+1)+a*u,i.push({start:this.findStart(t.document.body,e,n),end:this.findEnd(t.document.body,e,n)});return i}},{key:"findStart",value:function(t,e,n){for(var i,o,s=this,a=[t],u=t;a.length;)if(i=a.shift(),o=this.walk(i,(function(t){var i,o,c,l,h;if(h=Object(r.nodeBounds)(t),s.horizontal&&"ltr"===s.direction){if(i=s.horizontal?h.left:h.top,o=s.horizontal?h.right:h.bottom,i>=e&&i<=n)return t;if(o>e)return t;u=t,a.push(t)}else if(s.horizontal&&"rtl"===s.direction){if(i=h.left,(o=h.right)<=n&&o>=e)return t;if(i<n)return t;u=t,a.push(t)}else{if(c=h.top,l=h.bottom,c>=e&&c<=n)return t;if(l>e)return t;u=t,a.push(t)}})))return this.findTextStartRange(o,e,n);return this.findTextStartRange(u,e,n)}},{key:"findEnd",value:function(t,e,n){for(var i,o,s=this,a=[t],u=t;a.length;)if(i=a.shift(),o=this.walk(i,(function(t){var i,o,c,l,h;if(h=Object(r.nodeBounds)(t),s.horizontal&&"ltr"===s.direction){if(i=Math.round(h.left),o=Math.round(h.right),i>n&&u)return u;if(o>n)return t;u=t,a.push(t)}else if(s.horizontal&&"rtl"===s.direction){if(i=Math.round(s.horizontal?h.left:h.top),(o=Math.round(s.horizontal?h.right:h.bottom))<e&&u)return u;if(i<e)return t;u=t,a.push(t)}else{if(c=Math.round(h.top),l=Math.round(h.bottom),c>n&&u)return u;if(l>n)return t;u=t,a.push(t)}})))return this.findTextEndRange(o,e,n);return this.findTextEndRange(u,e,n)}},{key:"findTextStartRange",value:function(t,e,n){for(var i,r,o=this.splitTextNodeIntoRanges(t),s=0;s<o.length;s++)if(r=(i=o[s]).getBoundingClientRect(),this.horizontal&&"ltr"===this.direction){if(r.left>=e)return i}else if(this.horizontal&&"rtl"===this.direction){if(r.right<=n)return i}else if(r.top>=e)return i;return o[0]}},{key:"findTextEndRange",value:function(t,e,n){for(var i,r,o,s,a,u,c,l=this.splitTextNodeIntoRanges(t),h=0;h<l.length;h++){if(o=(r=l[h]).getBoundingClientRect(),this.horizontal&&"ltr"===this.direction){if(s=o.left,a=o.right,s>n&&i)return i;if(a>n)return r}else if(this.horizontal&&"rtl"===this.direction){if(s=o.left,(a=o.right)<e&&i)return i;if(s<e)return r}else{if(u=o.top,c=o.bottom,u>n&&i)return i;if(c>n)return r}i=r}return l[l.length-1]}},{key:"splitTextNodeIntoRanges",value:function(t,e){var n,i=[],r=(t.textContent||"").trim(),o=t.ownerDocument,s=e||" ",a=r.indexOf(s);if(-1===a||t.nodeType!=Node.TEXT_NODE)return(n=o.createRange()).selectNodeContents(t),[n];for((n=o.createRange()).setStart(t,0),n.setEnd(t,a),i.push(n),n=!1;-1!=a;)(a=r.indexOf(s,a+1))>0&&(n&&(n.setEnd(t,a),i.push(n)),(n=o.createRange()).setStart(t,a+1));return n&&(n.setEnd(t,r.length),i.push(n)),i}},{key:"rangePairToCfiPair",value:function(t,e){var n=e.start,r=e.end;return n.collapse(!0),r.collapse(!1),{start:new i.a(n,t).toString(),end:new i.a(r,t).toString()}}},{key:"rangeListToCfiList",value:function(t,e){for(var n,i=[],r=0;r<e.length;r++)n=this.rangePairToCfiPair(t,e[r]),i.push(n);return i}},{key:"axis",value:function(t){return t&&(this.horizontal="horizontal"===t),this.horizontal}}])&&s(e.prototype,n),a&&s(e,a),t}();e.a=a},function(t,e){t.exports={}},function(t,e,n){var i=n(13),r=n(7),o=n(24),s=n(82),a=n(133),u=s("IE_PROTO"),c=Object.prototype;t.exports=a?Object.getPrototypeOf:function(t){var e=o(t);if(i(e,u))return e[u];var n=e.constructor;return r(n)&&e instanceof n?n.prototype:e instanceof Object?c:null}},function(t,e,n){var i=n(110),r=n(7),o=n(47),s=n(6)("toStringTag"),a="Arguments"==o(function(){return arguments}());t.exports=i?o:function(t){var e,n,i;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(n=function(t,e){try{return t[e]}catch(t){}}(e=Object(t),s))?n:a?o(e):"Object"==(i=o(e))&&r(e.callee)?"Arguments":i}},function(t,e){t.exports={}},function(t,e,n){var i=n(48);t.exports=function(t,e,n){if(i(t),void 0===e)return t;switch(n){case 0:return function(){return t.call(e)};case 1:return function(n){return t.call(e,n)};case 2:return function(n,i){return t.call(e,n,i)};case 3:return function(n,i,r){return t.call(e,n,i,r)}}return function(){return t.apply(e,arguments)}}},function(t,e,n){"use strict";var i=n(91),r=n(3),o=n(8),s=n(7),a=n(41),u=n(52),c=n(21),l=n(32),h=n(116),f=n(40),d=n(191),p=n(92),v=n(6)("replace"),g=Math.max,m=Math.min,y="$0"==="a".replace(/./,"$0"),b=!!/./[v]&&""===/./[v]("a","$0");i("replace",(function(t,e,n){var i=b?"$":"$0";return[function(t,n){var i=l(this),r=null==t?void 0:f(t,v);return r?r.call(t,i,n):e.call(c(i),t,n)},function(t,r){var l=o(this),f=c(t);if("string"==typeof r&&-1===r.indexOf(i)&&-1===r.indexOf("$<")){var v=n(e,l,f,r);if(v.done)return v.value}var y=s(r);y||(r=c(r));var b=l.global;if(b){var w=l.unicode;l.lastIndex=0}for(var x=[];;){var k=p(l,f);if(null===k)break;if(x.push(k),!b)break;""===c(k[0])&&(l.lastIndex=h(f,u(l.lastIndex),w))}for(var E,S="",O=0,T=0;T<x.length;T++){k=x[T];for(var _=c(k[0]),N=g(m(a(k.index),f.length),0),C=[],R=1;R<k.length;R++)C.push(void 0===(E=k[R])?E:String(E));var I=k.groups;if(y){var A=[_].concat(C,N,f);void 0!==I&&A.push(I);var j=c(r.apply(void 0,A))}else j=d(_,f,N,C,I,r);N>=O&&(S+=f.slice(O,N)+j,O=N+_.length)}return S+f.slice(O)}]}),!!r((function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")}))||!y||b)},function(t,e,n){"use strict";var i=n(5),r=n(3),o=n(93),s=n(9),a=n(24),u=n(36),c=n(94),l=n(117),h=n(64),f=n(6),d=n(76),p=f("isConcatSpreadable"),v=d>=51||!r((function(){var t=[];return t[p]=!1,t.concat()[0]!==t})),g=h("concat"),m=function(t){if(!s(t))return!1;var e=t[p];return void 0!==e?!!e:o(t)};i({target:"Array",proto:!0,forced:!v||!g},{concat:function(t){var e,n,i,r,o,s=a(this),h=l(s,0),f=0;for(e=-1,i=arguments.length;e<i;e++)if(m(o=-1===e?s:arguments[e])){if(f+(r=u(o))>9007199254740991)throw TypeError("Maximum allowed index exceeded");for(n=0;n<r;n++,f++)n in o&&c(h,f,o[n])}else{if(f>=9007199254740991)throw TypeError("Maximum allowed index exceeded");c(h,f++,o)}return h.length=f,h}})},function(t,e,n){var i=n(3),r=n(6),o=n(76),s=r("species");t.exports=function(t){return o>=51||!i((function(){var e=[];return(e.constructor={})[s]=function(){return{foo:1}},1!==e[t](Boolean).foo}))}},function(t,e,n){var i=n(61),r=n(74),o=n(24),s=n(36),a=n(117),u=[].push,c=function(t){var e=1==t,n=2==t,c=3==t,l=4==t,h=6==t,f=7==t,d=5==t||h;return function(p,v,g,m){for(var y,b,w=o(p),x=r(w),k=i(v,g,3),E=s(x),S=0,O=m||a,T=e?O(p,E):n||f?O(p,0):void 0;E>S;S++)if((d||S in x)&&(b=k(y=x[S],S,w),t))if(e)T[S]=b;else if(b)switch(t){case 3:return!0;case 5:return y;case 6:return S;case 2:u.call(T,y)}else switch(t){case 4:return!1;case 7:u.call(T,y)}return h?-1:c||l?l:T}};t.exports={forEach:c(0),map:c(1),filter:c(2),some:c(3),every:c(4),find:c(5),findIndex:c(6),filterReject:c(7)}},function(t,e,n){"use strict";var i=n(5),r=n(65).filter;i({target:"Array",proto:!0,forced:!n(64)("filter")},{filter:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}})},function(t,e,n){"use strict";var i=n(5),r=n(4),o=n(29),s=n(49),a=n(17),u=n(102),c=n(3),l=n(13),h=n(93),f=n(7),d=n(9),p=n(101),v=n(8),g=n(24),m=n(31),y=n(75),b=n(21),w=n(46),x=n(43),k=n(87),E=n(51),S=n(119),O=n(106),T=n(45),_=n(20),N=n(100),C=n(25),R=n(78),I=n(82),A=n(57),j=n(79),L=n(6),P=n(159),D=n(160),M=n(42),z=n(30),B=n(65).forEach,q=I("hidden"),U=L("toPrimitive"),F=z.set,W=z.getterFor("Symbol"),H=Object.prototype,V=r.Symbol,X=o("JSON","stringify"),G=T.f,Y=_.f,$=S.f,K=N.f,Z=R("symbols"),J=R("op-symbols"),Q=R("string-to-symbol-registry"),tt=R("symbol-to-string-registry"),et=R("wks"),nt=r.QObject,it=!nt||!nt.prototype||!nt.prototype.findChild,rt=a&&c((function(){return 7!=x(Y({},"a",{get:function(){return Y(this,"a",{value:7}).a}})).a}))?function(t,e,n){var i=G(H,e);i&&delete H[e],Y(t,e,n),i&&t!==H&&Y(H,e,i)}:Y,ot=function(t,e){var n=Z[t]=x(V.prototype);return F(n,{type:"Symbol",tag:t,description:e}),a||(n.description=e),n},st=function(t,e,n){t===H&&st(J,e,n),v(t);var i=y(e);return v(n),l(Z,i)?(n.enumerable?(l(t,q)&&t[q][i]&&(t[q][i]=!1),n=x(n,{enumerable:w(0,!1)})):(l(t,q)||Y(t,q,w(1,{})),t[q][i]=!0),rt(t,i,n)):Y(t,i,n)},at=function(t,e){v(t);var n=m(e),i=k(n).concat(ht(n));return B(i,(function(e){a&&!ut.call(n,e)||st(t,e,n[e])})),t},ut=function(t){var e=y(t),n=K.call(this,e);return!(this===H&&l(Z,e)&&!l(J,e))&&(!(n||!l(this,e)||!l(Z,e)||l(this,q)&&this[q][e])||n)},ct=function(t,e){var n=m(t),i=y(e);if(n!==H||!l(Z,i)||l(J,i)){var r=G(n,i);return!r||!l(Z,i)||l(n,q)&&n[q][i]||(r.enumerable=!0),r}},lt=function(t){var e=$(m(t)),n=[];return B(e,(function(t){l(Z,t)||l(A,t)||n.push(t)})),n},ht=function(t){var e=t===H,n=$(e?J:m(t)),i=[];return B(n,(function(t){!l(Z,t)||e&&!l(H,t)||i.push(Z[t])})),i};(u||(C((V=function(){if(this instanceof V)throw TypeError("Symbol is not a constructor");var t=arguments.length&&void 0!==arguments[0]?b(arguments[0]):void 0,e=j(t),n=function(t){this===H&&n.call(J,t),l(this,q)&&l(this[q],e)&&(this[q][e]=!1),rt(this,e,w(1,t))};return a&&it&&rt(H,e,{configurable:!0,set:n}),ot(e,t)}).prototype,"toString",(function(){return W(this).tag})),C(V,"withoutSetter",(function(t){return ot(j(t),t)})),N.f=ut,_.f=st,T.f=ct,E.f=S.f=lt,O.f=ht,P.f=function(t){return ot(L(t),t)},a&&(Y(V.prototype,"description",{configurable:!0,get:function(){return W(this).description}}),s||C(H,"propertyIsEnumerable",ut,{unsafe:!0}))),i({global:!0,wrap:!0,forced:!u,sham:!u},{Symbol:V}),B(k(et),(function(t){D(t)})),i({target:"Symbol",stat:!0,forced:!u},{for:function(t){var e=b(t);if(l(Q,e))return Q[e];var n=V(e);return Q[e]=n,tt[n]=e,n},keyFor:function(t){if(!p(t))throw TypeError(t+" is not a symbol");if(l(tt,t))return tt[t]},useSetter:function(){it=!0},useSimple:function(){it=!1}}),i({target:"Object",stat:!0,forced:!u,sham:!a},{create:function(t,e){return void 0===e?x(t):at(x(t),e)},defineProperty:st,defineProperties:at,getOwnPropertyDescriptor:ct}),i({target:"Object",stat:!0,forced:!u},{getOwnPropertyNames:lt,getOwnPropertySymbols:ht}),i({target:"Object",stat:!0,forced:c((function(){O.f(1)}))},{getOwnPropertySymbols:function(t){return O.f(g(t))}}),X)&&i({target:"JSON",stat:!0,forced:!u||c((function(){var t=V();return"[null]"!=X([t])||"{}"!=X({a:t})||"{}"!=X(Object(t))}))},{stringify:function(t,e,n){for(var i,r=[t],o=1;arguments.length>o;)r.push(arguments[o++]);if(i=e,(d(e)||void 0!==t)&&!p(t))return h(e)||(e=function(t,e){if(f(i)&&(e=i.call(this,t,e)),!p(e))return e}),r[1]=e,X.apply(null,r)}});if(!V.prototype[U]){var ft=V.prototype.valueOf;C(V.prototype,U,(function(){return ft.apply(this,arguments)}))}M(V,"Symbol"),A[q]=!0},function(t,e,n){"use strict";var i=n(5),r=n(17),o=n(4),s=n(13),a=n(7),u=n(9),c=n(20).f,l=n(130),h=o.Symbol;if(r&&a(h)&&(!("description"in h.prototype)||void 0!==h().description)){var f={},d=function(){var t=arguments.length<1||void 0===arguments[0]?void 0:String(arguments[0]),e=this instanceof d?new h(t):void 0===t?h():h(t);return""===t&&(f[e]=!0),e};l(d,h);var p=d.prototype=h.prototype;p.constructor=d;var v=p.toString,g="Symbol(test)"==String(h("test")),m=/^Symbol\((.*)\)[^)]+$/;c(p,"description",{configurable:!0,get:function(){var t=u(this)?this.valueOf():this,e=v.call(t);if(s(f,t))return"";var n=g?e.slice(7,-1):e.replace(m,"$1");return""===n?void 0:n}}),i({global:!0,forced:!0},{Symbol:d})}},function(t,e,n){n(160)("iterator")},function(t,e,n){var i=n(5),r=n(24),o=n(87);i({target:"Object",stat:!0,forced:n(3)((function(){o(1)}))},{keys:function(t){return o(r(t))}})},function(t,e,n){"use strict";n(15),n(157),n(158),n(10),n(27),n(70),n(11),n(38),n(63),n(34),n(163),n(164);var i=n(12),r=n.n(i),o=n(0),s=n(2),a=n(56),u=n(28),c=n(1);function l(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}var h="undefined"!=typeof navigator,f=h&&/Chrome/.test(navigator.userAgent),d=h&&!f&&/AppleWebKit/.test(navigator.userAgent),p=function(){function t(e,n,i,r){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.epubcfi=new s.a,this.document=e,this.documentElement=this.document.documentElement,this.content=n||this.document.body,this.window=this.document.defaultView,this._size={width:0,height:0},this.sectionIndex=r||0,this.cfiBase=i||"",this.epubReadingSystem("epub.js",c.b),this.called=0,this.active=!0,this.listeners()}var e,n,i;return e=t,i=[{key:"listenedEvents",get:function(){return c.a}}],(n=[{key:"width",value:function(t){var e=this.content;return t&&Object(o.isNumber)(t)&&(t+="px"),t&&(e.style.width=t),parseInt(this.window.getComputedStyle(e).width)}},{key:"height",value:function(t){var e=this.content;return t&&Object(o.isNumber)(t)&&(t+="px"),t&&(e.style.height=t),parseInt(this.window.getComputedStyle(e).height)}},{key:"contentWidth",value:function(t){var e=this.content||this.document.body;return t&&Object(o.isNumber)(t)&&(t+="px"),t&&(e.style.width=t),parseInt(this.window.getComputedStyle(e).width)}},{key:"contentHeight",value:function(t){var e=this.content||this.document.body;return t&&Object(o.isNumber)(t)&&(t+="px"),t&&(e.style.height=t),parseInt(this.window.getComputedStyle(e).height)}},{key:"textWidth",value:function(){var t,e=this.document.createRange(),n=this.content||this.document.body,i=Object(o.borders)(n);return e.selectNodeContents(n),t=e.getBoundingClientRect().width,i&&i.width&&(t+=i.width),Math.round(t)}},{key:"textHeight",value:function(){var t,e=this.document.createRange(),n=this.content||this.document.body;return e.selectNodeContents(n),t=e.getBoundingClientRect().bottom,Math.round(t)}},{key:"scrollWidth",value:function(){return this.documentElement.scrollWidth}},{key:"scrollHeight",value:function(){return this.documentElement.scrollHeight}},{key:"overflow",value:function(t){return t&&(this.documentElement.style.overflow=t),this.window.getComputedStyle(this.documentElement).overflow}},{key:"overflowX",value:function(t){return t&&(this.documentElement.style.overflowX=t),this.window.getComputedStyle(this.documentElement).overflowX}},{key:"overflowY",value:function(t){return t&&(this.documentElement.style.overflowY=t),this.window.getComputedStyle(this.documentElement).overflowY}},{key:"css",value:function(t,e,n){var i=this.content||this.document.body;return e?i.style.setProperty(t,e,n?"important":""):i.style.removeProperty(t),this.window.getComputedStyle(i)[t]}},{key:"viewport",value:function(t){var e,n=this.document.querySelector("meta[name='viewport']"),i={width:void 0,height:void 0,scale:void 0,minimum:void 0,maximum:void 0,scalable:void 0},r=[];if(n&&n.hasAttribute("content")){var s=n.getAttribute("content"),a=s.match(/width\s*=\s*([^,]*)/),u=s.match(/height\s*=\s*([^,]*)/),c=s.match(/initial-scale\s*=\s*([^,]*)/),l=s.match(/minimum-scale\s*=\s*([^,]*)/),h=s.match(/maximum-scale\s*=\s*([^,]*)/),f=s.match(/user-scalable\s*=\s*([^,]*)/);a&&a.length&&void 0!==a[1]&&(i.width=a[1]),u&&u.length&&void 0!==u[1]&&(i.height=u[1]),c&&c.length&&void 0!==c[1]&&(i.scale=c[1]),l&&l.length&&void 0!==l[1]&&(i.minimum=l[1]),h&&h.length&&void 0!==h[1]&&(i.maximum=h[1]),f&&f.length&&void 0!==f[1]&&(i.scalable=f[1])}return e=Object(o.defaults)(t||{},i),t&&(e.width&&r.push("width="+e.width),e.height&&r.push("height="+e.height),e.scale&&r.push("initial-scale="+e.scale),"no"===e.scalable?(r.push("minimum-scale="+e.scale),r.push("maximum-scale="+e.scale),r.push("user-scalable="+e.scalable)):(e.scalable&&r.push("user-scalable="+e.scalable),e.minimum&&r.push("minimum-scale="+e.minimum),e.maximum&&r.push("minimum-scale="+e.maximum)),n||((n=this.document.createElement("meta")).setAttribute("name","viewport"),this.document.querySelector("head").appendChild(n)),n.setAttribute("content",r.join(", ")),this.window.scrollTo(0,0)),e}},{key:"expand",value:function(){this.emit(c.c.CONTENTS.EXPAND)}},{key:"listeners",value:function(){this.imageLoadListeners(),this.mediaQueryListeners(),this.addEventListeners(),this.addSelectionListeners(),"undefined"==typeof ResizeObserver?(this.resizeListeners(),this.visibilityListeners()):this.resizeObservers(),this.linksHandler()}},{key:"removeListeners",value:function(){this.removeEventListeners(),this.removeSelectionListeners(),this.observer&&this.observer.disconnect(),clearTimeout(this.expanding)}},{key:"resizeCheck",value:function(){var t=this.textWidth(),e=this.textHeight();t==this._size.width&&e==this._size.height||(this._size={width:t,height:e},this.onResize&&this.onResize(this._size),this.emit(c.c.CONTENTS.RESIZE,this._size))}},{key:"resizeListeners",value:function(){clearTimeout(this.expanding),requestAnimationFrame(this.resizeCheck.bind(this)),this.expanding=setTimeout(this.resizeListeners.bind(this),350)}},{key:"visibilityListeners",value:function(){var t=this;document.addEventListener("visibilitychange",(function(){"visible"===document.visibilityState&&!1===t.active?(t.active=!0,t.resizeListeners()):(t.active=!1,clearTimeout(t.expanding))}))}},{key:"transitionListeners",value:function(){var t=this.content;t.style.transitionProperty="font, font-size, font-size-adjust, font-stretch, font-variation-settings, font-weight, width, height",t.style.transitionDuration="0.001ms",t.style.transitionTimingFunction="linear",t.style.transitionDelay="0",this._resizeCheck=this.resizeCheck.bind(this),this.document.addEventListener("transitionend",this._resizeCheck)}},{key:"mediaQueryListeners",value:function(){for(var t=this.document.styleSheets,e=function(t){t.matches&&!this._expanding&&setTimeout(this.expand.bind(this),1)}.bind(this),n=0;n<t.length;n+=1){var i;try{i=t[n].cssRules}catch(t){return}if(!i)return;for(var r=0;r<i.length;r+=1)i[r].media&&this.window.matchMedia(i[r].media.mediaText).addListener(e)}}},{key:"resizeObservers",value:function(){var t=this;this.observer=new ResizeObserver((function(e){requestAnimationFrame(t.resizeCheck.bind(t))})),this.observer.observe(this.document.documentElement)}},{key:"mutationObservers",value:function(){var t=this;this.observer=new MutationObserver((function(e){t.resizeCheck()})),this.observer.observe(this.document,{attributes:!0,childList:!0,characterData:!0,subtree:!0})}},{key:"imageLoadListeners",value:function(){for(var t,e=this.document.querySelectorAll("img"),n=0;n<e.length;n++)void 0!==(t=e[n]).naturalWidth&&0===t.naturalWidth&&(t.onload=this.expand.bind(this))}},{key:"fontLoadListeners",value:function(){this.document&&this.document.fonts&&this.document.fonts.ready.then(function(){this.resizeCheck()}.bind(this))}},{key:"root",value:function(){return this.document?this.document.documentElement:null}},{key:"locationOf",value:function(t,e){var n,i={left:0,top:0};if(!this.document)return i;if(this.epubcfi.isCfiString(t)){var r=new s.a(t).toRange(this.document,e);if(r){try{if(!r.endContainer||r.startContainer==r.endContainer&&r.startOffset==r.endOffset){var o=r.startContainer.textContent.indexOf(" ",r.startOffset);-1==o&&(o=r.startContainer.textContent.length),r.setEnd(r.startContainer,o)}}catch(t){console.error("setting end offset to start container length failed",t)}if(r.startContainer.nodeType===Node.ELEMENT_NODE)n=r.startContainer.getBoundingClientRect(),i.left=n.left,i.top=n.top;else if(d){var a=r.startContainer,u=new Range;try{1===a.nodeType?n=a.getBoundingClientRect():r.startOffset+2<a.length?(u.setStart(a,r.startOffset),u.setEnd(a,r.startOffset+2),n=u.getBoundingClientRect()):r.startOffset-2>0?(u.setStart(a,r.startOffset-2),u.setEnd(a,r.startOffset),n=u.getBoundingClientRect()):n=a.parentNode.getBoundingClientRect()}catch(t){console.error(t,t.stack)}}else n=r.getBoundingClientRect()}}else if("string"==typeof t&&t.indexOf("#")>-1){var c=t.substring(t.indexOf("#")+1),l=this.document.getElementById(c);if(l)if(d){var h=new Range;h.selectNode(l),n=h.getBoundingClientRect()}else n=l.getBoundingClientRect()}return n&&(i.left=n.left,i.top=n.top),i}},{key:"addStylesheet",value:function(t){return new Promise(function(e,n){var i,r=!1;this.document?(i=this.document.querySelector("link[href='"+t+"']"))?e(!0):((i=this.document.createElement("link")).type="text/css",i.rel="stylesheet",i.href=t,i.onload=i.onreadystatechange=function(){r||this.readyState&&"complete"!=this.readyState||(r=!0,setTimeout((function(){e(!0)}),1))},this.document.head.appendChild(i)):e(!1)}.bind(this))}},{key:"_getStylesheetNode",value:function(t){var e;return t="epubjs-inserted-css-"+(t||""),!!this.document&&((e=this.document.getElementById(t))||((e=this.document.createElement("style")).id=t,this.document.head.appendChild(e)),e)}},{key:"addStylesheetCss",value:function(t,e){return!(!this.document||!t||(this._getStylesheetNode(e).innerHTML=t,0))}},{key:"addStylesheetRules",value:function(t,e){var n;if(this.document&&t&&0!==t.length)if(n=this._getStylesheetNode(e).sheet,"[object Array]"===Object.prototype.toString.call(t))for(var i=0,r=t.length;i<r;i++){var o=1,s=t[i],a=t[i][0],u="";"[object Array]"===Object.prototype.toString.call(s[1][0])&&(s=s[1],o=0);for(var c=s.length;o<c;o++){var l=s[o];u+=l[0]+":"+l[1]+(l[2]?" !important":"")+";\n"}n.insertRule(a+"{"+u+"}",n.cssRules.length)}else Object.keys(t).forEach((function(e){var i=t[e];if(Array.isArray(i))i.forEach((function(t){var i=Object.keys(t).map((function(e){return"".concat(e,":").concat(t[e])})).join(";");n.insertRule("".concat(e,"{").concat(i,"}"),n.cssRules.length)}));else{var r=Object.keys(i).map((function(t){return"".concat(t,":").concat(i[t])})).join(";");n.insertRule("".concat(e,"{").concat(r,"}"),n.cssRules.length)}}))}},{key:"addScript",value:function(t){return new Promise(function(e,n){var i,r=!1;this.document?((i=this.document.createElement("script")).type="text/javascript",i.async=!0,i.src=t,i.onload=i.onreadystatechange=function(){r||this.readyState&&"complete"!=this.readyState||(r=!0,setTimeout((function(){e(!0)}),1))},this.document.head.appendChild(i)):e(!1)}.bind(this))}},{key:"addClass",value:function(t){var e;this.document&&(e=this.content||this.document.body)&&e.classList.add(t)}},{key:"removeClass",value:function(t){var e;this.document&&(e=this.content||this.document.body)&&e.classList.remove(t)}},{key:"addEventListeners",value:function(){this.document&&(this._triggerEvent=this.triggerEvent.bind(this),c.a.forEach((function(t){this.document.addEventListener(t,this._triggerEvent,{passive:!0})}),this))}},{key:"removeEventListeners",value:function(){this.document&&(c.a.forEach((function(t){this.document.removeEventListener(t,this._triggerEvent,{passive:!0})}),this),this._triggerEvent=void 0)}},{key:"triggerEvent",value:function(t){this.emit(t.type,t)}},{key:"addSelectionListeners",value:function(){this.document&&(this._onSelectionChange=this.onSelectionChange.bind(this),this.document.addEventListener("selectionchange",this._onSelectionChange,{passive:!0}))}},{key:"removeSelectionListeners",value:function(){this.document&&(this.document.removeEventListener("selectionchange",this._onSelectionChange,{passive:!0}),this._onSelectionChange=void 0)}},{key:"onSelectionChange",value:function(t){this.selectionEndTimeout&&clearTimeout(this.selectionEndTimeout),this.selectionEndTimeout=setTimeout(function(){var t=this.window.getSelection();this.triggerSelectedEvent(t)}.bind(this),250)}},{key:"triggerSelectedEvent",value:function(t){var e,n;t&&t.rangeCount>0&&((e=t.getRangeAt(0)).collapsed||(n=new s.a(e,this.cfiBase).toString(),this.emit(c.c.CONTENTS.SELECTED,n),this.emit(c.c.CONTENTS.SELECTED_RANGE,e)))}},{key:"range",value:function(t,e){return new s.a(t).toRange(this.document,e)}},{key:"cfiFromRange",value:function(t,e){return new s.a(t,this.cfiBase,e).toString()}},{key:"cfiFromNode",value:function(t,e){return new s.a(t,this.cfiBase,e).toString()}},{key:"map",value:function(t){var e=new a.a(t);return e.section()}},{key:"size",value:function(t,e){var n={scale:1,scalable:"no"};this.layoutStyle("scrolling"),t>=0&&(this.width(t),n.width=t,this.css("padding","0 "+t/12+"px")),e>=0&&(this.height(e),n.height=e),this.css("margin","0"),this.css("box-sizing","border-box"),this.viewport(n)}},{key:"columns",value:function(t,e,n,i,r){var s=Object(o.prefixed)("column-axis"),a=Object(o.prefixed)("column-gap"),u=Object(o.prefixed)("column-width"),c=Object(o.prefixed)("column-fill"),l=0===this.writingMode().indexOf("vertical")?"vertical":"horizontal";this.layoutStyle("paginated"),"rtl"===r&&"horizontal"===l&&this.direction(r),this.width(t),this.height(e),this.viewport({width:t,height:e,scale:1,scalable:"no"}),this.css("overflow-y","hidden"),this.css("margin","0",!0),"vertical"===l?(this.css("padding-top",i/2+"px",!0),this.css("padding-bottom",i/2+"px",!0),this.css("padding-left","20px"),this.css("padding-right","20px"),this.css(s,"vertical")):(this.css("padding-top","20px"),this.css("padding-bottom","20px"),this.css("padding-left",i/2+"px",!0),this.css("padding-right",i/2+"px",!0),this.css(s,"horizontal")),this.css("box-sizing","border-box"),this.css("max-width","inherit"),this.css(c,"auto"),this.css(a,i+"px"),this.css(u,n+"px"),this.css("-webkit-line-box-contain","block glyphs replaced")}},{key:"scaler",value:function(t,e,n){var i="scale("+t+")",r="";this.css("transform-origin","top left"),(e>=0||n>=0)&&(r=" translate("+(e||0)+"px, "+(n||0)+"px )"),this.css("transform",i+r)}},{key:"fit",value:function(t,e,n){var i=this.viewport(),r=parseInt(i.width),o=parseInt(i.height),s=t/r,a=e/o,u=s<a?s:a;if(this.layoutStyle("paginated"),this.width(r),this.height(o),this.overflow("hidden"),this.scaler(u,0,0),this.css("background-size",r*u+"px "+o*u+"px"),this.css("background-color","transparent"),n&&n.properties.includes("page-spread-left")){var c=t-r*u;this.css("margin-left",c+"px")}}},{key:"direction",value:function(t){this.documentElement&&(this.documentElement.style.direction=t)}},{key:"mapPage",value:function(t,e,n,i,r){return new a.a(e,r).page(this,t,n,i)}},{key:"linksHandler",value:function(){var t=this;Object(u.c)(this.content,(function(e){t.emit(c.c.CONTENTS.LINK_CLICKED,e)}))}},{key:"writingMode",value:function(t){var e=Object(o.prefixed)("writing-mode");return t&&this.documentElement&&(this.documentElement.style[e]=t),this.window.getComputedStyle(this.documentElement)[e]||""}},{key:"layoutStyle",value:function(t){return t&&(this._layoutStyle=t,navigator.epubReadingSystem.layoutStyle=this._layoutStyle),this._layoutStyle||"paginated"}},{key:"epubReadingSystem",value:function(t,e){return navigator.epubReadingSystem={name:t,version:e,layoutStyle:this.layoutStyle(),hasFeature:function(t){switch(t){case"dom-manipulation":case"layout-changes":case"touch-events":case"mouse-events":case"keyboard-events":return!0;case"spine-scripting":default:return!1}}},navigator.epubReadingSystem}},{key:"destroy",value:function(){this.removeListeners()}}])&&l(e.prototype,n),i&&l(e,i),t}();r()(p.prototype),e.a=p},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.Underline=e.Highlight=e.Mark=e.Pane=void 0;var i=function(){function t(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}return function(e,n,i){return n&&t(e.prototype,n),i&&t(e,i),e}}(),r=s(n(228)),o=s(n(229));function s(t){return t&&t.__esModule?t:{default:t}}function a(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}function u(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}function c(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}e.Pane=function(){function t(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:document.body;c(this,t),this.target=e,this.element=r.default.createElement("svg"),this.marks=[],this.element.style.position="absolute",this.element.setAttribute("pointer-events","none"),o.default.proxyMouse(this.target,this.marks),this.container=n,this.container.appendChild(this.element),this.render()}return i(t,[{key:"addMark",value:function(t){var e=r.default.createElement("g");return this.element.appendChild(e),t.bind(e,this.container),this.marks.push(t),t.render(),t}},{key:"removeMark",value:function(t){var e=this.marks.indexOf(t);if(-1!==e){var n=t.unbind();this.element.removeChild(n),this.marks.splice(e,1)}}},{key:"render",value:function(){var t,e,n,i;!function(t,e){t.style.setProperty("top",e.top+"px","important"),t.style.setProperty("left",e.left+"px","important"),t.style.setProperty("height",e.height+"px","important"),t.style.setProperty("width",e.width+"px","important")}(this.element,(t=this.target,e=this.container,n=e.getBoundingClientRect(),i=t.getBoundingClientRect(),{top:i.top-n.top,left:i.left-n.left,height:t.scrollHeight,width:t.scrollWidth}));var r=!0,o=!1,s=void 0;try{for(var a,u=this.marks[Symbol.iterator]();!(r=(a=u.next()).done);r=!0){a.value.render()}}catch(t){o=!0,s=t}finally{try{!r&&u.return&&u.return()}finally{if(o)throw s}}}}]),t}();var l=e.Mark=function(){function t(){c(this,t),this.element=null}return i(t,[{key:"bind",value:function(t,e){this.element=t,this.container=e}},{key:"unbind",value:function(){var t=this.element;return this.element=null,t}},{key:"render",value:function(){}},{key:"dispatchEvent",value:function(t){this.element&&this.element.dispatchEvent(t)}},{key:"getBoundingClientRect",value:function(){return this.element.getBoundingClientRect()}},{key:"getClientRects",value:function(){for(var t=[],e=this.element.firstChild;e;)t.push(e.getBoundingClientRect()),e=e.nextSibling;return t}},{key:"filteredRanges",value:function(){var t=Array.from(this.range.getClientRects());return t.filter((function(e){for(var n=0;n<t.length;n++){if(t[n]===e)return!0;if(i=t[n],(r=e).right<=i.right&&r.left>=i.left&&r.top>=i.top&&r.bottom<=i.bottom)return!1}var i,r;return!0}))}}]),t}(),h=e.Highlight=function(t){function e(t,n,i,r){c(this,e);var o=a(this,(e.__proto__||Object.getPrototypeOf(e)).call(this));return o.range=t,o.className=n,o.data=i||{},o.attributes=r||{},o}return u(e,t),i(e,[{key:"bind",value:function(t,n){for(var i in function t(e,n,i){null===e&&(e=Function.prototype);var r=Object.getOwnPropertyDescriptor(e,n);if(void 0===r){var o=Object.getPrototypeOf(e);return null===o?void 0:t(o,n,i)}if("value"in r)return r.value;var s=r.get;return void 0!==s?s.call(i):void 0}(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"bind",this).call(this,t,n),this.data)this.data.hasOwnProperty(i)&&(this.element.dataset[i]=this.data[i]);for(var i in this.attributes)this.attributes.hasOwnProperty(i)&&this.element.setAttribute(i,this.attributes[i]);this.className&&this.element.classList.add(this.className)}},{key:"render",value:function(){for(;this.element.firstChild;)this.element.removeChild(this.element.firstChild);for(var t=this.element.ownerDocument.createDocumentFragment(),e=this.filteredRanges(),n=this.element.getBoundingClientRect(),i=this.container.getBoundingClientRect(),o=0,s=e.length;o<s;o++){var a=e[o],u=r.default.createElement("rect");u.setAttribute("x",a.left-n.left+i.left),u.setAttribute("y",a.top-n.top+i.top),u.setAttribute("height",a.height),u.setAttribute("width",a.width),t.appendChild(u)}this.element.appendChild(t)}}]),e}(l);e.Underline=function(t){function e(t,n,i,r){return c(this,e),a(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t,n,i,r))}return u(e,t),i(e,[{key:"render",value:function(){for(;this.element.firstChild;)this.element.removeChild(this.element.firstChild);for(var t=this.element.ownerDocument.createDocumentFragment(),e=this.filteredRanges(),n=this.element.getBoundingClientRect(),i=this.container.getBoundingClientRect(),o=0,s=e.length;o<s;o++){var a=e[o],u=r.default.createElement("rect");u.setAttribute("x",a.left-n.left+i.left),u.setAttribute("y",a.top-n.top+i.top),u.setAttribute("height",a.height),u.setAttribute("width",a.width),u.setAttribute("fill","none");var c=r.default.createElement("line");c.setAttribute("x1",a.left-n.left+i.left),c.setAttribute("x2",a.left-n.left+i.left+a.width),c.setAttribute("y1",a.top-n.top+i.top+a.height-1),c.setAttribute("y2",a.top-n.top+i.top+a.height-1),c.setAttribute("stroke-width",1),c.setAttribute("stroke","black"),c.setAttribute("stroke-linecap","square"),t.appendChild(u),t.appendChild(c)}this.element.appendChild(t)}}]),e}(h)},function(t,e){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(t){"object"==typeof window&&(n=window)}t.exports=n},function(t,e,n){var i=n(3),r=n(47),o="".split;t.exports=i((function(){return!Object("z").propertyIsEnumerable(0)}))?function(t){return"String"==r(t)?o.call(t,""):Object(t)}:Object},function(t,e,n){var i=n(171),r=n(101);t.exports=function(t){var e=i(t,"string");return r(e)?e:String(e)}},function(t,e,n){var i,r,o=n(4),s=n(77),a=o.process,u=o.Deno,c=a&&a.versions||u&&u.version,l=c&&c.v8;l?r=(i=l.split("."))[0]<4?1:i[0]+i[1]:s&&(!(i=s.match(/Edge\/(\d+)/))||i[1]>=74)&&(i=s.match(/Chrome\/(\d+)/))&&(r=i[1]),t.exports=r&&+r},function(t,e,n){var i=n(29);t.exports=i("navigator","userAgent")||""},function(t,e,n){var i=n(49),r=n(103);(t.exports=function(t,e){return r[t]||(r[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.18.3",mode:i?"pure":"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})},function(t,e){var n=0,i=Math.random();t.exports=function(t){return"Symbol("+String(void 0===t?"":t)+")_"+(++n+i).toString(36)}},function(t,e,n){var i=n(4),r=n(9),o=i.document,s=r(o)&&r(o.createElement);t.exports=function(t){return s?o.createElement(t):{}}},function(t,e,n){var i=n(7),r=n(103),o=Function.toString;i(r.inspectSource)||(r.inspectSource=function(t){return o.call(t)}),t.exports=r.inspectSource},function(t,e,n){var i=n(78),r=n(79),o=i("keys");t.exports=function(t){return o[t]||(o[t]=r(t))}},function(t,e,n){var i=n(41),r=Math.max,o=Math.min;t.exports=function(t,e){var n=i(t);return n<0?r(n+e,0):o(n,e)}},function(t,e){t.exports=function(t,e,n){if(t instanceof e)return t;throw TypeError("Incorrect "+(n?n+" ":"")+"invocation")}},function(t,e,n){var i=n(8),r=n(179);t.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var t,e=!1,n={};try{(t=Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set).call(n,[]),e=n instanceof Array}catch(t){}return function(n,o){return i(n),r(o),e?t.call(n,o):n.__proto__=o,n}}():void 0)},function(t,e,n){var i=n(6),r=n(43),o=n(20),s=i("unscopables"),a=Array.prototype;null==a[s]&&o.f(a,s,{configurable:!0,value:r(null)}),t.exports=function(t){a[s][t]=!0}},function(t,e,n){var i=n(131),r=n(105);t.exports=Object.keys||function(t){return i(t,r)}},function(t,e,n){var i=n(59),r=n(40),o=n(60),s=n(6)("iterator");t.exports=function(t){if(null!=t)return r(t,s)||r(t,"@@iterator")||o[i(t)]}},function(t,e,n){var i=n(3),r=n(7),o=n(59),s=n(29),a=n(81),u=[],c=s("Reflect","construct"),l=/^\s*(?:class|function)\b/,h=l.exec,f=!l.exec((function(){})),d=function(t){if(!r(t))return!1;try{return c(Object,u,t),!0}catch(t){return!1}};t.exports=!c||i((function(){var t;return d(d.call)||!d(Object)||!d((function(){t=!0}))||t}))?function(t){if(!r(t))return!1;switch(o(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}return f||!!h.call(l,a(t))}:d},function(t,e,n){"use strict";var i,r,o=n(21),s=n(114),a=n(115),u=n(78),c=n(43),l=n(30).get,h=n(149),f=n(150),d=RegExp.prototype.exec,p=u("native-string-replace",String.prototype.replace),v=d,g=(i=/a/,r=/b*/g,d.call(i,"a"),d.call(r,"a"),0!==i.lastIndex||0!==r.lastIndex),m=a.UNSUPPORTED_Y||a.BROKEN_CARET,y=void 0!==/()??/.exec("")[1];(g||y||m||h||f)&&(v=function(t){var e,n,i,r,a,u,h,f=this,b=l(f),w=o(t),x=b.raw;if(x)return x.lastIndex=f.lastIndex,e=v.call(x,w),f.lastIndex=x.lastIndex,e;var k=b.groups,E=m&&f.sticky,S=s.call(f),O=f.source,T=0,_=w;if(E&&(-1===(S=S.replace("y","")).indexOf("g")&&(S+="g"),_=w.slice(f.lastIndex),f.lastIndex>0&&(!f.multiline||f.multiline&&"\n"!==w.charAt(f.lastIndex-1))&&(O="(?: "+O+")",_=" "+_,T++),n=new RegExp("^(?:"+O+")",S)),y&&(n=new RegExp("^"+O+"$(?!\\s)",S)),g&&(i=f.lastIndex),r=d.call(E?n:f,_),E?r?(r.input=r.input.slice(T),r[0]=r[0].slice(T),r.index=f.lastIndex,f.lastIndex+=r[0].length):f.lastIndex=0:g&&r&&(f.lastIndex=f.global?r.index+r[0].length:i),y&&r&&r.length>1&&p.call(r[0],n,(function(){for(a=1;a<arguments.length-2;a++)void 0===arguments[a]&&(r[a]=void 0)})),r&&k)for(r.groups=u=c(null),a=0;a<k.length;a++)u[(h=k[a])[0]]=r[h[1]];return r}),t.exports=v},function(t,e,n){"use strict";n(15);var i=n(25),r=n(90),o=n(3),s=n(6),a=n(33),u=s("species"),c=RegExp.prototype;t.exports=function(t,e,n,l){var h=s(t),f=!o((function(){var e={};return e[h]=function(){return 7},7!=""[t](e)})),d=f&&!o((function(){var e=!1,n=/a/;return"split"===t&&((n={}).constructor={},n.constructor[u]=function(){return n},n.flags="",n[h]=/./[h]),n.exec=function(){return e=!0,null},n[h](""),!e}));if(!f||!d||n){var p=/./[h],v=e(h,""[t],(function(t,e,n,i,o){var s=e.exec;return s===r||s===c.exec?f&&!o?{done:!0,value:p.call(e,n,i)}:{done:!0,value:t.call(n,e,i)}:{done:!1}}));i(String.prototype,t,v[0]),i(c,h,v[1])}l&&a(c[h],"sham",!0)}},function(t,e,n){var i=n(8),r=n(7),o=n(47),s=n(90);t.exports=function(t,e){var n=t.exec;if(r(n)){var a=n.call(t,e);return null!==a&&i(a),a}if("RegExp"===o(t))return s.call(t,e);throw TypeError("RegExp#exec called on incompatible receiver")}},function(t,e,n){var i=n(47);t.exports=Array.isArray||function(t){return"Array"==i(t)}},function(t,e,n){"use strict";var i=n(75),r=n(20),o=n(46);t.exports=function(t,e,n){var s=i(e);s in t?r.f(t,s,o(0,n)):t[s]=n}},function(t,e,n){"use strict";var i=n(5),r=n(83),o=n(41),s=n(36),a=n(24),u=n(117),c=n(94),l=n(64)("splice"),h=Math.max,f=Math.min;i({target:"Array",proto:!0,forced:!l},{splice:function(t,e){var n,i,l,d,p,v,g=a(this),m=s(g),y=r(t,m),b=arguments.length;if(0===b?n=i=0:1===b?(n=0,i=m-y):(n=b-2,i=f(h(o(e),0),m-y)),m+n-i>9007199254740991)throw TypeError("Maximum allowed length exceeded");for(l=u(g,i),d=0;d<i;d++)(p=y+d)in g&&c(l,d,g[p]);if(l.length=i,n<i){for(d=y;d<m-i;d++)v=d+n,(p=d+i)in g?g[v]=g[p]:delete g[v];for(d=m;d>m-i+n;d--)delete g[d-1]}else if(n>i)for(d=m-i;d>y;d--)v=d+n-1,(p=d+i-1)in g?g[v]=g[p]:delete g[v];for(d=0;d<n;d++)g[d+y]=arguments[d+2];return g.length=m-i+n,l}})},function(t,e,n){"use strict";function i(t,e){return void 0===e&&(e=Object),e&&"function"==typeof e.freeze?e.freeze(t):t}var r=i({HTML:"text/html",isHTML:function(t){return t===r.HTML},XML_APPLICATION:"application/xml",XML_TEXT:"text/xml",XML_XHTML_APPLICATION:"application/xhtml+xml",XML_SVG_IMAGE:"image/svg+xml"}),o=i({HTML:"http://www.w3.org/1999/xhtml",isHTML:function(t){return t===o.HTML},SVG:"http://www.w3.org/2000/svg",XML:"http://www.w3.org/XML/1998/namespace",XMLNS:"http://www.w3.org/2000/xmlns/"});e.freeze=i,e.MIME_TYPE=r,e.NAMESPACE=o},function(t,e,n){"use strict";var i=n(5),r=n(223).trim;i({target:"String",proto:!0,forced:n(224)("trim")},{trim:function(){return r(this)}})},function(t,e,n){var i=n(156);e.DOMImplementation=i.DOMImplementation,e.XMLSerializer=i.XMLSerializer,e.DOMParser=n(215).DOMParser},function(t,e,n){"use strict";n(11),n(66),n(39),n(10),n(27),n(67),n(68),n(69),n(14),n(18),n(22);var i=n(12),r=n.n(i),o=n(0),s=n(23),a=n(2),u=n(54),c=(n(70),n(1));function l(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}var h=function(){function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.settings=e,this.name=e.layout||"reflowable",this._spread="none"!==e.spread,this._minSpreadWidth=e.minSpreadWidth||800,this._evenSpreads=e.evenSpreads||!1,"scrolled"===e.flow||"scrolled-continuous"===e.flow||"scrolled-doc"===e.flow?this._flow="scrolled":this._flow="paginated",this.width=0,this.height=0,this.spreadWidth=0,this.delta=0,this.columnWidth=0,this.gap=0,this.divisor=1,this.props={name:this.name,spread:this._spread,flow:this._flow,width:0,height:0,spreadWidth:0,delta:0,columnWidth:0,gap:0,divisor:1}}var e,n,i;return e=t,(n=[{key:"flow",value:function(t){return void 0!==t&&(this._flow="scrolled"===t||"scrolled-continuous"===t||"scrolled-doc"===t?"scrolled":"paginated",this.update({flow:this._flow})),this._flow}},{key:"spread",value:function(t,e){return t&&(this._spread="none"!==t,this.update({spread:this._spread})),e>=0&&(this._minSpreadWidth=e),this._spread}},{key:"calculate",value:function(t,e,n){var i,r,o,s,a=1,u=n||0,c=t,l=e,h=Math.floor(c/12);a=this._spread&&c>=this._minSpreadWidth?2:1,"reflowable"!==this.name||"paginated"!==this._flow||n>=0||(u=h%2==0?h:h-1),"pre-paginated"===this.name&&(u=0),a>1?o=(i=c/a-u)+u:(i=c,o=c),"pre-paginated"===this.name&&a>1&&(c=i),r=i*a+u,s=c,this.width=c,this.height=l,this.spreadWidth=r,this.pageWidth=o,this.delta=s,this.columnWidth=i,this.gap=u,this.divisor=a,this.update({width:c,height:l,spreadWidth:r,pageWidth:o,delta:s,columnWidth:i,gap:u,divisor:a})}},{key:"format",value:function(t,e,n){return"pre-paginated"===this.name?t.fit(this.columnWidth,this.height,e):"paginated"===this._flow?t.columns(this.width,this.height,this.columnWidth,this.gap,this.settings.direction):n&&"horizontal"===n?t.size(null,this.height):t.size(this.width,null)}},{key:"count",value:function(t,e){var n,i;return"pre-paginated"===this.name?(n=1,i=1):"paginated"===this._flow?(e=e||this.delta,i=(n=Math.ceil(t/e))*this.divisor):(e=e||this.height,i=n=Math.ceil(t/e)),{spreads:n,pages:i}}},{key:"update",value:function(t){var e=this;if(Object.keys(t).forEach((function(n){e.props[n]===t[n]&&delete t[n]})),Object.keys(t).length>0){var n=Object(o.extend)(this.props,t);this.emit(c.c.LAYOUT.UPDATED,n,t)}}}])&&l(e.prototype,n),i&&l(e,i),t}();r()(h.prototype);var f=h,d=(n(34),n(19));function p(t){return(p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function v(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}var g=function(){function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.rendition=e,this._themes={default:{rules:{},url:"",serialized:""}},this._overrides={},this._current="default",this._injected=[],this.rendition.hooks.content.register(this.inject.bind(this)),this.rendition.hooks.content.register(this.overrides.bind(this))}var e,n,i;return e=t,(n=[{key:"register",value:function(){if(0!==arguments.length)return 1===arguments.length&&"object"===p(arguments[0])?this.registerThemes(arguments[0]):1===arguments.length&&"string"==typeof arguments[0]?this.default(arguments[0]):2===arguments.length&&"string"==typeof arguments[1]?this.registerUrl(arguments[0],arguments[1]):2===arguments.length&&"object"===p(arguments[1])?this.registerRules(arguments[0],arguments[1]):void 0}},{key:"default",value:function(t){if(t)return"string"==typeof t?this.registerUrl("default",t):"object"===p(t)?this.registerRules("default",t):void 0}},{key:"registerThemes",value:function(t){for(var e in t)t.hasOwnProperty(e)&&("string"==typeof t[e]?this.registerUrl(e,t[e]):this.registerRules(e,t[e]))}},{key:"registerCss",value:function(t,e){this._themes[t]={serialized:e},(this._injected[t]||"default"==t)&&this.update(t)}},{key:"registerUrl",value:function(t,e){var n=new d.a(e);this._themes[t]={url:n.toString()},(this._injected[t]||"default"==t)&&this.update(t)}},{key:"registerRules",value:function(t,e){this._themes[t]={rules:e},(this._injected[t]||"default"==t)&&this.update(t)}},{key:"select",value:function(t){var e=this._current;this._current=t,this.update(t),this.rendition.getContents().forEach((function(n){n.removeClass(e),n.addClass(t)}))}},{key:"update",value:function(t){var e=this;this.rendition.getContents().forEach((function(n){e.add(t,n)}))}},{key:"inject",value:function(t){var e,n=[],i=this._themes;for(var r in i)!i.hasOwnProperty(r)||r!==this._current&&"default"!==r||(((e=i[r]).rules&&Object.keys(e.rules).length>0||e.url&&-1===n.indexOf(e.url))&&this.add(r,t),this._injected.push(r));"default"!=this._current&&t.addClass(this._current)}},{key:"add",value:function(t,e){var n=this._themes[t];n&&e&&(n.url?e.addStylesheet(n.url):n.serialized?(e.addStylesheetCss(n.serialized,t),n.injected=!0):n.rules&&(e.addStylesheetRules(n.rules,t),n.injected=!0))}},{key:"override",value:function(t,e,n){var i=this,r=this.rendition.getContents();this._overrides[t]={value:e,priority:!0===n},r.forEach((function(e){e.css(t,i._overrides[t].value,i._overrides[t].priority)}))}},{key:"removeOverride",value:function(t){var e=this.rendition.getContents();delete this._overrides[t],e.forEach((function(e){e.css(t)}))}},{key:"overrides",value:function(t){var e=this._overrides;for(var n in e)e.hasOwnProperty(n)&&t.css(n,e[n].value,e[n].priority)}},{key:"fontSize",value:function(t){this.override("font-size",t)}},{key:"font",value:function(t){this.override("font-family",t,!0)}},{key:"destroy",value:function(){this.rendition=void 0,this._themes=void 0,this._overrides=void 0,this._current=void 0,this._injected=void 0}}])&&v(e.prototype,n),i&&v(e,i),t}();n(71);function m(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function y(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function b(t,e,n){return e&&y(t.prototype,e),n&&y(t,n),t}var w=function(){function t(e){m(this,t),this.rendition=e,this.highlights=[],this.underlines=[],this.marks=[],this._annotations={},this._annotationsBySectionIndex={},this.rendition.hooks.render.register(this.inject.bind(this)),this.rendition.hooks.unloaded.register(this.clear.bind(this))}return b(t,[{key:"add",value:function(t,e,n,i,r,o){var s=encodeURI(e+t),u=new a.a(e).spinePos,c=new x({type:t,cfiRange:e,data:n,sectionIndex:u,cb:i,className:r,styles:o});return this._annotations[s]=c,u in this._annotationsBySectionIndex?this._annotationsBySectionIndex[u].push(s):this._annotationsBySectionIndex[u]=[s],this.rendition.views().forEach((function(t){c.sectionIndex===t.index&&c.attach(t)})),c}},{key:"remove",value:function(t,e){var n=this,i=encodeURI(t+e);if(i in this._annotations){var r=this._annotations[i];if(e&&r.type!==e)return;this.rendition.views().forEach((function(t){n._removeFromAnnotationBySectionIndex(r.sectionIndex,i),r.sectionIndex===t.index&&r.detach(t)})),delete this._annotations[i]}}},{key:"_removeFromAnnotationBySectionIndex",value:function(t,e){this._annotationsBySectionIndex[t]=this._annotationsAt(t).filter((function(t){return t!==e}))}},{key:"_annotationsAt",value:function(t){return this._annotationsBySectionIndex[t]}},{key:"highlight",value:function(t,e,n,i,r){return this.add("highlight",t,e,n,i,r)}},{key:"underline",value:function(t,e,n,i,r){return this.add("underline",t,e,n,i,r)}},{key:"mark",value:function(t,e,n){return this.add("mark",t,e,n)}},{key:"each",value:function(){return this._annotations.forEach.apply(this._annotations,arguments)}},{key:"inject",value:function(t){var e=this,n=t.index;n in this._annotationsBySectionIndex&&this._annotationsBySectionIndex[n].forEach((function(n){e._annotations[n].attach(t)}))}},{key:"clear",value:function(t){var e=this,n=t.index;n in this._annotationsBySectionIndex&&this._annotationsBySectionIndex[n].forEach((function(n){e._annotations[n].detach(t)}))}},{key:"show",value:function(){}},{key:"hide",value:function(){}}]),t}(),x=function(){function t(e){var n=e.type,i=e.cfiRange,r=e.data,o=e.sectionIndex,s=e.cb,a=e.className,u=e.styles;m(this,t),this.type=n,this.cfiRange=i,this.data=r,this.sectionIndex=o,this.mark=void 0,this.cb=s,this.className=a,this.styles=u}return b(t,[{key:"update",value:function(t){this.data=t}},{key:"attach",value:function(t){var e,n=this.cfiRange,i=this.data,r=this.type,o=(this.mark,this.cb),s=this.className,a=this.styles;return"highlight"===r?e=t.highlight(n,i,o,s,a):"underline"===r?e=t.underline(n,i,o,s,a):"mark"===r&&(e=t.mark(n,i,o)),this.mark=e,this.emit(c.c.ANNOTATION.ATTACH,e),e}},{key:"detach",value:function(t){var e,n=this.cfiRange,i=this.type;return t&&("highlight"===i?e=t.unhighlight(n):"underline"===i?e=t.ununderline(n):"mark"===i&&(e=t.unmark(n))),this.mark=void 0,this.emit(c.c.ANNOTATION.DETACH,e),e}},{key:"text",value:function(){}}]),t}();r()(x.prototype);var k=w,E=n(122),S=n(55),O=n(124);function T(t){return(T="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function _(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}var N=function(){function t(e,n){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.settings=Object(o.extend)(this.settings||{},{width:null,height:null,ignoreClass:"",manager:"default",view:"iframe",flow:null,layout:null,spread:null,minSpreadWidth:800,stylesheet:null,resizeOnOrientationChange:!0,script:null,snap:!1,defaultDirection:"ltr",allowScriptedContent:!1,allowPopups:!1}),Object(o.extend)(this.settings,n),"object"===T(this.settings.manager)&&(this.manager=this.settings.manager),this.book=e,this.hooks={},this.hooks.display=new s.a(this),this.hooks.serialize=new s.a(this),this.hooks.content=new s.a(this),this.hooks.unloaded=new s.a(this),this.hooks.layout=new s.a(this),this.hooks.render=new s.a(this),this.hooks.show=new s.a(this),this.hooks.content.register(this.handleLinks.bind(this)),this.hooks.content.register(this.passEvents.bind(this)),this.hooks.content.register(this.adjustImages.bind(this)),this.book.spine.hooks.content.register(this.injectIdentifier.bind(this)),this.settings.stylesheet&&this.book.spine.hooks.content.register(this.injectStylesheet.bind(this)),this.settings.script&&this.book.spine.hooks.content.register(this.injectScript.bind(this)),this.themes=new g(this),this.annotations=new k(this),this.epubcfi=new a.a,this.q=new u.a(this),this.location=void 0,this.q.enqueue(this.book.opened),this.starting=new o.defer,this.started=this.starting.promise,this.q.enqueue(this.start)}var e,n,i;return e=t,(n=[{key:"setManager",value:function(t){this.manager=t}},{key:"requireManager",value:function(t){return"string"==typeof t&&"default"===t?S.a:"string"==typeof t&&"continuous"===t?O.a:t}},{key:"requireView",value:function(t){return"string"==typeof t&&"iframe"===t?E.a:t}},{key:"start",value:function(){switch(this.settings.layout||"pre-paginated"!==this.book.package.metadata.layout&&"true"!==this.book.displayOptions.fixedLayout||(this.settings.layout="pre-paginated"),this.book.package.metadata.spread){case"none":this.settings.spread="none";break;case"both":this.settings.spread=!0}this.manager||(this.ViewManager=this.requireManager(this.settings.manager),this.View=this.requireView(this.settings.view),this.manager=new this.ViewManager({view:this.View,queue:this.q,request:this.book.load.bind(this.book),settings:this.settings})),this.direction(this.book.package.metadata.direction||this.settings.defaultDirection),this.settings.globalLayoutProperties=this.determineLayoutProperties(this.book.package.metadata),this.flow(this.settings.globalLayoutProperties.flow),this.layout(this.settings.globalLayoutProperties),this.manager.on(c.c.MANAGERS.ADDED,this.afterDisplayed.bind(this)),this.manager.on(c.c.MANAGERS.REMOVED,this.afterRemoved.bind(this)),this.manager.on(c.c.MANAGERS.RESIZED,this.onResized.bind(this)),this.manager.on(c.c.MANAGERS.ORIENTATION_CHANGE,this.onOrientationChange.bind(this)),this.manager.on(c.c.MANAGERS.SCROLLED,this.reportLocation.bind(this)),this.emit(c.c.RENDITION.STARTED),this.starting.resolve()}},{key:"attachTo",value:function(t){return this.q.enqueue(function(){this.manager.render(t,{width:this.settings.width,height:this.settings.height}),this.emit(c.c.RENDITION.ATTACHED)}.bind(this))}},{key:"display",value:function(t){return this.displaying&&this.displaying.resolve(),this.q.enqueue(this._display,t)}},{key:"_display",value:function(t){var e=this;if(this.book){this.epubcfi.isCfiString(t);var n,i=new o.defer,r=i.promise;return this.displaying=i,this.book.locations.length()&&Object(o.isFloat)(t)&&(t=this.book.locations.cfiFromPercentage(parseFloat(t))),(n=this.book.spine.get(t))?(this.manager.display(n,t).then((function(){i.resolve(n),e.displaying=void 0,e.emit(c.c.RENDITION.DISPLAYED,n),e.reportLocation()}),(function(t){e.emit(c.c.RENDITION.DISPLAY_ERROR,t)})),r):(i.reject(new Error("No Section Found")),r)}}},{key:"afterDisplayed",value:function(t){var e=this;t.on(c.c.VIEWS.MARK_CLICKED,(function(n,i){return e.triggerMarkEvent(n,i,t.contents)})),this.hooks.render.trigger(t,this).then((function(){t.contents?e.hooks.content.trigger(t.contents,e).then((function(){e.emit(c.c.RENDITION.RENDERED,t.section,t)})):e.emit(c.c.RENDITION.RENDERED,t.section,t)}))}},{key:"afterRemoved",value:function(t){var e=this;this.hooks.unloaded.trigger(t,this).then((function(){e.emit(c.c.RENDITION.REMOVED,t.section,t)}))}},{key:"onResized",value:function(t,e){this.emit(c.c.RENDITION.RESIZED,{width:t.width,height:t.height},e),this.location&&this.location.start&&this.display(e||this.location.start.cfi)}},{key:"onOrientationChange",value:function(t){this.emit(c.c.RENDITION.ORIENTATION_CHANGE,t)}},{key:"moveTo",value:function(t){this.manager.moveTo(t)}},{key:"resize",value:function(t,e,n){t&&(this.settings.width=t),e&&(this.settings.height=e),this.manager.resize(t,e,n)}},{key:"clear",value:function(){this.manager.clear()}},{key:"next",value:function(){return this.q.enqueue(this.manager.next.bind(this.manager)).then(this.reportLocation.bind(this))}},{key:"prev",value:function(){return this.q.enqueue(this.manager.prev.bind(this.manager)).then(this.reportLocation.bind(this))}},{key:"determineLayoutProperties",value:function(t){var e=this.settings.layout||t.layout||"reflowable",n=this.settings.spread||t.spread||"auto",i=this.settings.orientation||t.orientation||"auto",r=this.settings.flow||t.flow||"auto",o=t.viewport||"",s=this.settings.minSpreadWidth||t.minSpreadWidth||800,a=this.settings.direction||t.direction||"ltr";return(0===this.settings.width||this.settings.width>0)&&(0===this.settings.height||this.settings.height),{layout:e,spread:n,orientation:i,flow:r,viewport:o,minSpreadWidth:s,direction:a}}},{key:"flow",value:function(t){var e=t;"scrolled"!==t&&"scrolled-doc"!==t&&"scrolled-continuous"!==t||(e="scrolled"),"auto"!==t&&"paginated"!==t||(e="paginated"),this.settings.flow=t,this._layout&&this._layout.flow(e),this.manager&&this._layout&&this.manager.applyLayout(this._layout),this.manager&&this.manager.updateFlow(e),this.manager&&this.manager.isRendered()&&this.location&&(this.manager.clear(),this.display(this.location.start.cfi))}},{key:"layout",value:function(t){var e=this;return t&&(this._layout=new f(t),this._layout.spread(t.spread,this.settings.minSpreadWidth),this._layout.on(c.c.LAYOUT.UPDATED,(function(t,n){e.emit(c.c.RENDITION.LAYOUT,t,n)}))),this.manager&&this._layout&&this.manager.applyLayout(this._layout),this._layout}},{key:"spread",value:function(t,e){this.settings.spread=t,e&&(this.settings.minSpreadWidth=e),this._layout&&this._layout.spread(t,e),this.manager&&this.manager.isRendered()&&this.manager.updateLayout()}},{key:"direction",value:function(t){this.settings.direction=t||"ltr",this.manager&&this.manager.direction(this.settings.direction),this.manager&&this.manager.isRendered()&&this.location&&(this.manager.clear(),this.display(this.location.start.cfi))}},{key:"reportLocation",value:function(){return this.q.enqueue(function(){requestAnimationFrame(function(){var t=this.manager.currentLocation();if(t&&t.then&&"function"==typeof t.then)t.then(function(t){var e=this.located(t);e&&e.start&&e.end&&(this.location=e,this.emit(c.c.RENDITION.LOCATION_CHANGED,{index:this.location.start.index,href:this.location.start.href,start:this.location.start.cfi,end:this.location.end.cfi,percentage:this.location.start.percentage}),this.emit(c.c.RENDITION.RELOCATED,this.location))}.bind(this));else if(t){var e=this.located(t);if(!e||!e.start||!e.end)return;this.location=e,this.emit(c.c.RENDITION.LOCATION_CHANGED,{index:this.location.start.index,href:this.location.start.href,start:this.location.start.cfi,end:this.location.end.cfi,percentage:this.location.start.percentage}),this.emit(c.c.RENDITION.RELOCATED,this.location)}}.bind(this))}.bind(this))}},{key:"currentLocation",value:function(){var t=this.manager.currentLocation();if(t&&t.then&&"function"==typeof t.then)t.then(function(t){return this.located(t)}.bind(this));else if(t)return this.located(t)}},{key:"located",value:function(t){if(!t.length)return{};var e=t[0],n=t[t.length-1],i={start:{index:e.index,href:e.href,cfi:e.mapping.start,displayed:{page:e.pages[0]||1,total:e.totalPages}},end:{index:n.index,href:n.href,cfi:n.mapping.end,displayed:{page:n.pages[n.pages.length-1]||1,total:n.totalPages}}},r=this.book.locations.locationFromCfi(e.mapping.start),o=this.book.locations.locationFromCfi(n.mapping.end);null!=r&&(i.start.location=r,i.start.percentage=this.book.locations.percentageFromLocation(r)),null!=o&&(i.end.location=o,i.end.percentage=this.book.locations.percentageFromLocation(o));var s=this.book.pageList.pageFromCfi(e.mapping.start),a=this.book.pageList.pageFromCfi(n.mapping.end);return-1!=s&&(i.start.page=s),-1!=a&&(i.end.page=a),n.index===this.book.spine.last().index&&i.end.displayed.page>=i.end.displayed.total&&(i.atEnd=!0),e.index===this.book.spine.first().index&&1===i.start.displayed.page&&(i.atStart=!0),i}},{key:"destroy",value:function(){this.manager&&this.manager.destroy(),this.book=void 0}},{key:"passEvents",value:function(t){var e=this;c.a.forEach((function(n){t.on(n,(function(n){return e.triggerViewEvent(n,t)}))})),t.on(c.c.CONTENTS.SELECTED,(function(n){return e.triggerSelectedEvent(n,t)}))}},{key:"triggerViewEvent",value:function(t,e){this.emit(t.type,t,e)}},{key:"triggerSelectedEvent",value:function(t,e){this.emit(c.c.RENDITION.SELECTED,t,e)}},{key:"triggerMarkEvent",value:function(t,e,n){this.emit(c.c.RENDITION.MARK_CLICKED,t,e,n)}},{key:"getRange",value:function(t,e){var n=new a.a(t),i=this.manager.visible().filter((function(t){if(n.spinePos===t.index)return!0}));if(i.length)return i[0].contents.range(n,e)}},{key:"adjustImages",value:function(t){if("pre-paginated"===this._layout.name)return new Promise((function(t){t()}));var e=t.window.getComputedStyle(t.content,null),n=.95*(t.content.offsetHeight-(parseFloat(e.paddingTop)+parseFloat(e.paddingBottom))),i=parseFloat(e.paddingLeft)+parseFloat(e.paddingRight);return t.addStylesheetRules({img:{"max-width":(this._layout.columnWidth?this._layout.columnWidth-i+"px":"100%")+"!important","max-height":n+"px!important","object-fit":"contain","page-break-inside":"avoid","break-inside":"avoid","box-sizing":"border-box"},svg:{"max-width":(this._layout.columnWidth?this._layout.columnWidth-i+"px":"100%")+"!important","max-height":n+"px!important","page-break-inside":"avoid","break-inside":"avoid"}}),new Promise((function(t,e){setTimeout((function(){t()}),1)}))}},{key:"getContents",value:function(){return this.manager?this.manager.getContents():[]}},{key:"views",value:function(){var t=this.manager?this.manager.views:void 0;return t||[]}},{key:"handleLinks",value:function(t){var e=this;t&&t.on(c.c.CONTENTS.LINK_CLICKED,(function(t){var n=e.book.path.relative(t);e.display(n)}))}},{key:"injectStylesheet",value:function(t,e){var n=t.createElement("link");n.setAttribute("type","text/css"),n.setAttribute("rel","stylesheet"),n.setAttribute("href",this.settings.stylesheet),t.getElementsByTagName("head")[0].appendChild(n)}},{key:"injectScript",value:function(t,e){var n=t.createElement("script");n.setAttribute("type","text/javascript"),n.setAttribute("src",this.settings.script),n.textContent=" ",t.getElementsByTagName("head")[0].appendChild(n)}},{key:"injectIdentifier",value:function(t,e){var n=this.book.packaging.metadata.identifier,i=t.createElement("meta");i.setAttribute("name","dc.relation.ispartof"),n&&i.setAttribute("content",n),t.getElementsByTagName("head")[0].appendChild(i)}}])&&_(e.prototype,n),i&&_(e,i),t}();r()(N.prototype);e.a=N},function(t,e,n){"use strict";var i={}.propertyIsEnumerable,r=Object.getOwnPropertyDescriptor,o=r&&!i.call({1:2},1);e.f=o?function(t){var e=r(this,t);return!!e&&e.enumerable}:i},function(t,e,n){var i=n(7),r=n(29),o=n(127);t.exports=o?function(t){return"symbol"==typeof t}:function(t){var e=r("Symbol");return i(e)&&Object(t)instanceof e}},function(t,e,n){var i=n(76),r=n(3);t.exports=!!Object.getOwnPropertySymbols&&!r((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&i&&i<41}))},function(t,e,n){var i=n(4),r=n(104),o=i["__core-js_shared__"]||r("__core-js_shared__",{});t.exports=o},function(t,e,n){var i=n(4);t.exports=function(t,e){try{Object.defineProperty(i,t,{value:e,configurable:!0,writable:!0})}catch(n){i[t]=e}return e}},function(t,e){t.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},function(t,e){e.f=Object.getOwnPropertySymbols},function(t,e,n){var i=n(3),r=n(7),o=/#|\.prototype\./,s=function(t,e){var n=u[a(t)];return n==l||n!=c&&(r(e)?i(e):!!e)},a=s.normalize=function(t){return String(t).replace(o,".").toLowerCase()},u=s.data={},c=s.NATIVE="N",l=s.POLYFILL="P";t.exports=s},function(t,e,n){var i=n(25);t.exports=function(t,e,n){for(var r in e)i(t,r,e[r],n);return t}},function(t,e,n){"use strict";var i=n(29),r=n(20),o=n(6),s=n(17),a=o("species");t.exports=function(t){var e=i(t),n=r.f;s&&e&&!e[a]&&n(e,a,{configurable:!0,get:function(){return this}})}},function(t,e,n){var i={};i[n(6)("toStringTag")]="z",t.exports="[object z]"===String(i)},function(t,e,n){var i=n(48),r=n(8),o=n(88);t.exports=function(t,e){var n=arguments.length<2?o(t):e;if(i(n))return r(n.call(t));throw TypeError(String(t)+" is not iterable")}},function(t,e,n){var i=n(47),r=n(4);t.exports="process"==i(r.process)},function(t,e,n){var i=n(41),r=n(21),o=n(32),s=function(t){return function(e,n){var s,a,u=r(o(e)),c=i(n),l=u.length;return c<0||c>=l?t?"":void 0:(s=u.charCodeAt(c))<55296||s>56319||c+1===l||(a=u.charCodeAt(c+1))<56320||a>57343?t?u.charAt(c):s:t?u.slice(c,c+2):a-56320+(s-55296<<10)+65536}};t.exports={codeAt:s(!1),charAt:s(!0)}},function(t,e,n){"use strict";var i=n(8);t.exports=function(){var t=i(this),e="";return t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.dotAll&&(e+="s"),t.unicode&&(e+="u"),t.sticky&&(e+="y"),e}},function(t,e,n){var i=n(3),r=n(4).RegExp;e.UNSUPPORTED_Y=i((function(){var t=r("a","y");return t.lastIndex=2,null!=t.exec("abcd")})),e.BROKEN_CARET=i((function(){var t=r("^r","gy");return t.lastIndex=2,null!=t.exec("str")}))},function(t,e,n){"use strict";var i=n(113).charAt;t.exports=function(t,e,n){return e+(n?i(t,e).length:1)}},function(t,e,n){var i=n(192);t.exports=function(t,e){return new(i(t))(0===e?0:e)}},function(t,e,n){"use strict";var i=n(200)();t.exports=function(t){return t!==i&&null!==t}},function(t,e,n){var i=n(31),r=n(51).f,o={}.toString,s="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];t.exports.f=function(t){return s&&"[object Window]"==o.call(t)?function(t){try{return r(t)}catch(t){return s.slice()}}(t):r(i(t))}},function(t,e,n){var i=n(9),r=n(47),o=n(6)("match");t.exports=function(t){var e;return i(t)&&(void 0!==(e=t[o])?!!e:"RegExp"==r(t))}},function(t,e){t.exports=function(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}},function(t,e,n){"use strict";n(10),n(27),n(39),n(227),n(11),n(70);var i=n(12),r=n.n(i),o=n(0),s=n(2),a=n(71),u=n(1),c=n(72);function l(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}var h=function(){function t(e,n){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.settings=Object(o.extend)({ignoreClass:"",axis:void 0,direction:void 0,width:0,height:0,layout:void 0,globalLayoutProperties:{},method:void 0,forceRight:!1,allowScriptedContent:!1,allowPopups:!1},n||{}),this.id="epubjs-view-"+Object(o.uuid)(),this.section=e,this.index=e.index,this.element=this.container(this.settings.axis),this.added=!1,this.displayed=!1,this.rendered=!1,this.fixedWidth=0,this.fixedHeight=0,this.epubcfi=new s.a,this.layout=this.settings.layout,this.pane=void 0,this.highlights={},this.underlines={},this.marks={}}var e,n,i;return e=t,(n=[{key:"container",value:function(t){var e=document.createElement("div");return e.classList.add("epub-view"),e.style.height="0px",e.style.width="0px",e.style.overflow="hidden",e.style.position="relative",e.style.display="block",e.style.flex=t&&"horizontal"==t?"none":"initial",e}},{key:"create",value:function(){return this.iframe||(this.element||(this.element=this.createContainer()),this.iframe=document.createElement("iframe"),this.iframe.id=this.id,this.iframe.scrolling="no",this.iframe.style.overflow="hidden",this.iframe.seamless="seamless",this.iframe.style.border="none",this.iframe.sandbox="allow-same-origin",this.settings.allowScriptedContent&&(this.iframe.sandbox+=" allow-scripts"),this.settings.allowPopups&&(this.iframe.sandbox+=" allow-popups"),this.iframe.setAttribute("enable-annotation","true"),this.resizing=!0,this.element.style.visibility="hidden",this.iframe.style.visibility="hidden",this.iframe.style.width="0",this.iframe.style.height="0",this._width=0,this._height=0,this.element.setAttribute("ref",this.index),this.added=!0,this.elementBounds=Object(o.bounds)(this.element),"srcdoc"in this.iframe?this.supportsSrcdoc=!0:this.supportsSrcdoc=!1,this.settings.method||(this.settings.method=this.supportsSrcdoc?"srcdoc":"write")),this.iframe}},{key:"render",value:function(t,e){return this.create(),this.size(),this.sectionRender||(this.sectionRender=this.section.render(t)),this.sectionRender.then(function(t){return this.load(t)}.bind(this)).then(function(){var t,e=this,n=this.contents.writingMode();return t="scrolled"===this.settings.flow?0===n.indexOf("vertical")?"horizontal":"vertical":0===n.indexOf("vertical")?"vertical":"horizontal",0===n.indexOf("vertical")&&"paginated"===this.settings.flow&&(this.layout.delta=this.layout.height),this.setAxis(t),this.emit(u.c.VIEWS.AXIS,t),this.setWritingMode(n),this.emit(u.c.VIEWS.WRITING_MODE,n),this.layout.format(this.contents,this.section,this.axis),this.addListeners(),new Promise((function(t,n){e.expand(),e.settings.forceRight&&(e.element.style.marginLeft=e.width()+"px"),t()}))}.bind(this),function(t){return this.emit(u.c.VIEWS.LOAD_ERROR,t),new Promise((function(e,n){n(t)}))}.bind(this)).then(function(){this.emit(u.c.VIEWS.RENDERED,this.section)}.bind(this))}},{key:"reset",value:function(){this.iframe&&(this.iframe.style.width="0",this.iframe.style.height="0",this._width=0,this._height=0,this._textWidth=void 0,this._contentWidth=void 0,this._textHeight=void 0,this._contentHeight=void 0),this._needsReframe=!0}},{key:"size",value:function(t,e){var n=t||this.settings.width,i=e||this.settings.height;"pre-paginated"===this.layout.name?this.lock("both",n,i):"horizontal"===this.settings.axis?this.lock("height",n,i):this.lock("width",n,i),this.settings.width=n,this.settings.height=i}},{key:"lock",value:function(t,e,n){var i,r=Object(o.borders)(this.element);i=this.iframe?Object(o.borders)(this.iframe):{width:0,height:0},"width"==t&&Object(o.isNumber)(e)&&(this.lockedWidth=e-r.width-i.width),"height"==t&&Object(o.isNumber)(n)&&(this.lockedHeight=n-r.height-i.height),"both"===t&&Object(o.isNumber)(e)&&Object(o.isNumber)(n)&&(this.lockedWidth=e-r.width-i.width,this.lockedHeight=n-r.height-i.height),this.displayed&&this.iframe&&this.expand()}},{key:"expand",value:function(t){var e,n=this.lockedWidth,i=this.lockedHeight;this.iframe&&!this._expanding&&(this._expanding=!0,"pre-paginated"===this.layout.name?(n=this.layout.columnWidth,i=this.layout.height):"horizontal"===this.settings.axis?((n=this.contents.textWidth())%this.layout.pageWidth>0&&(n=Math.ceil(n/this.layout.pageWidth)*this.layout.pageWidth),this.settings.forceEvenPages&&(e=n/this.layout.pageWidth,this.layout.divisor>1&&"reflowable"===this.layout.name&&e%2>0&&(n+=this.layout.pageWidth))):"vertical"===this.settings.axis&&(i=this.contents.textHeight(),"paginated"===this.settings.flow&&i%this.layout.height>0&&(i=Math.ceil(i/this.layout.height)*this.layout.height)),(this._needsReframe||n!=this._width||i!=this._height)&&this.reframe(n,i),this._expanding=!1)}},{key:"reframe",value:function(t,e){var n,i=this;Object(o.isNumber)(t)&&(this.element.style.width=t+"px",this.iframe.style.width=t+"px",this._width=t),Object(o.isNumber)(e)&&(this.element.style.height=e+"px",this.iframe.style.height=e+"px",this._height=e),n={width:t,height:e,widthDelta:this.prevBounds?t-this.prevBounds.width:t,heightDelta:this.prevBounds?e-this.prevBounds.height:e},this.pane&&this.pane.render(),requestAnimationFrame((function(){var t;for(var e in i.marks)i.marks.hasOwnProperty(e)&&(t=i.marks[e],i.placeMark(t.element,t.range))})),this.onResize(this,n),this.emit(u.c.VIEWS.RESIZED,n),this.prevBounds=n,this.elementBounds=Object(o.bounds)(this.element)}},{key:"load",value:function(t){var e=new o.defer,n=e.promise;if(!this.iframe)return e.reject(new Error("No Iframe Available")),n;if(this.iframe.onload=function(t){this.onLoad(t,e)}.bind(this),"blobUrl"===this.settings.method)this.blobUrl=Object(o.createBlobUrl)(t,"application/xhtml+xml"),this.iframe.src=this.blobUrl,this.element.appendChild(this.iframe);else if("srcdoc"===this.settings.method)this.iframe.srcdoc=t,this.element.appendChild(this.iframe);else{if(this.element.appendChild(this.iframe),this.document=this.iframe.contentDocument,!this.document)return e.reject(new Error("No Document Available")),n;if(this.iframe.contentDocument.open(),window.MSApp&&MSApp.execUnsafeLocalFunction){var i=this;MSApp.execUnsafeLocalFunction((function(){i.iframe.contentDocument.write(t)}))}else this.iframe.contentDocument.write(t);this.iframe.contentDocument.close()}return n}},{key:"onLoad",value:function(t,e){var n=this;this.window=this.iframe.contentWindow,this.document=this.iframe.contentDocument,this.contents=new a.a(this.document,this.document.body,this.section.cfiBase,this.section.index),this.rendering=!1;var i=this.document.querySelector("link[rel='canonical']");i?i.setAttribute("href",this.section.canonical):((i=this.document.createElement("link")).setAttribute("rel","canonical"),i.setAttribute("href",this.section.canonical),this.document.querySelector("head").appendChild(i)),this.contents.on(u.c.CONTENTS.EXPAND,(function(){n.displayed&&n.iframe&&(n.expand(),n.contents&&n.layout.format(n.contents))})),this.contents.on(u.c.CONTENTS.RESIZE,(function(t){n.displayed&&n.iframe&&(n.expand(),n.contents&&n.layout.format(n.contents))})),e.resolve(this.contents)}},{key:"setLayout",value:function(t){this.layout=t,this.contents&&(this.layout.format(this.contents),this.expand())}},{key:"setAxis",value:function(t){this.settings.axis=t,this.element.style.flex="horizontal"==t?"none":"initial",this.size()}},{key:"setWritingMode",value:function(t){this.writingMode=t}},{key:"addListeners",value:function(){}},{key:"removeListeners",value:function(t){}},{key:"display",value:function(t){var e=new o.defer;return this.displayed?e.resolve(this):this.render(t).then(function(){this.emit(u.c.VIEWS.DISPLAYED,this),this.onDisplayed(this),this.displayed=!0,e.resolve(this)}.bind(this),(function(t){e.reject(t,this)})),e.promise}},{key:"show",value:function(){this.element.style.visibility="visible",this.iframe&&(this.iframe.style.visibility="visible",this.iframe.style.transform="translateZ(0)",this.iframe.offsetWidth,this.iframe.style.transform=null),this.emit(u.c.VIEWS.SHOWN,this)}},{key:"hide",value:function(){this.element.style.visibility="hidden",this.iframe.style.visibility="hidden",this.stopExpanding=!0,this.emit(u.c.VIEWS.HIDDEN,this)}},{key:"offset",value:function(){return{top:this.element.offsetTop,left:this.element.offsetLeft}}},{key:"width",value:function(){return this._width}},{key:"height",value:function(){return this._height}},{key:"position",value:function(){return this.element.getBoundingClientRect()}},{key:"locationOf",value:function(t){this.iframe.getBoundingClientRect();var e=this.contents.locationOf(t,this.settings.ignoreClass);return{left:e.left,top:e.top}}},{key:"onDisplayed",value:function(t){}},{key:"onResize",value:function(t,e){}},{key:"bounds",value:function(t){return!t&&this.elementBounds||(this.elementBounds=Object(o.bounds)(this.element)),this.elementBounds}},{key:"highlight",value:function(t){var e=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments.length>2?arguments[2]:void 0,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"epubjs-hl",o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{};if(this.contents){var s=Object.assign({fill:"yellow","fill-opacity":"0.3","mix-blend-mode":"multiply"},o),a=this.contents.range(t),l=function(){e.emit(u.c.VIEWS.MARK_CLICKED,t,n)};n.epubcfi=t,this.pane||(this.pane=new c.Pane(this.iframe,this.element));var h=new c.Highlight(a,r,n,s),f=this.pane.addMark(h);return this.highlights[t]={mark:f,element:f.element,listeners:[l,i]},f.element.setAttribute("ref",r),f.element.addEventListener("click",l),f.element.addEventListener("touchstart",l),i&&(f.element.addEventListener("click",i),f.element.addEventListener("touchstart",i)),f}}},{key:"underline",value:function(t){var e=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments.length>2?arguments[2]:void 0,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"epubjs-ul",o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{};if(this.contents){var s=Object.assign({stroke:"black","stroke-opacity":"0.3","mix-blend-mode":"multiply"},o),a=this.contents.range(t),l=function(){e.emit(u.c.VIEWS.MARK_CLICKED,t,n)};n.epubcfi=t,this.pane||(this.pane=new c.Pane(this.iframe,this.element));var h=new c.Underline(a,r,n,s),f=this.pane.addMark(h);return this.underlines[t]={mark:f,element:f.element,listeners:[l,i]},f.element.setAttribute("ref",r),f.element.addEventListener("click",l),f.element.addEventListener("touchstart",l),i&&(f.element.addEventListener("click",i),f.element.addEventListener("touchstart",i)),f}}},{key:"mark",value:function(t){var e=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments.length>2?arguments[2]:void 0;if(this.contents){if(t in this.marks){var r=this.marks[t];return r}var o=this.contents.range(t);if(o){var s=o.commonAncestorContainer,a=1===s.nodeType?s:s.parentNode,c=function(i){e.emit(u.c.VIEWS.MARK_CLICKED,t,n)};o.collapsed&&1===s.nodeType?(o=new Range).selectNodeContents(s):o.collapsed&&(o=new Range).selectNodeContents(a);var l=this.document.createElement("a");return l.setAttribute("ref","epubjs-mk"),l.style.position="absolute",l.dataset.epubcfi=t,n&&Object.keys(n).forEach((function(t){l.dataset[t]=n[t]})),i&&(l.addEventListener("click",i),l.addEventListener("touchstart",i)),l.addEventListener("click",c),l.addEventListener("touchstart",c),this.placeMark(l,o),this.element.appendChild(l),this.marks[t]={element:l,range:o,listeners:[c,i]},a}}}},{key:"placeMark",value:function(t,e){var n,i,r;if("pre-paginated"===this.layout.name||"horizontal"!==this.settings.axis){var o=e.getBoundingClientRect();n=o.top,i=o.right}else for(var s,a=e.getClientRects(),u=0;u!=a.length;u++)s=a[u],(!r||s.left<r)&&(r=s.left,i=Math.ceil(r/this.layout.props.pageWidth)*this.layout.props.pageWidth-this.layout.gap/2,n=s.top);t.style.top="".concat(n,"px"),t.style.left="".concat(i,"px")}},{key:"unhighlight",value:function(t){var e;t in this.highlights&&(e=this.highlights[t],this.pane.removeMark(e.mark),e.listeners.forEach((function(t){t&&(e.element.removeEventListener("click",t),e.element.removeEventListener("touchstart",t))})),delete this.highlights[t])}},{key:"ununderline",value:function(t){var e;t in this.underlines&&(e=this.underlines[t],this.pane.removeMark(e.mark),e.listeners.forEach((function(t){t&&(e.element.removeEventListener("click",t),e.element.removeEventListener("touchstart",t))})),delete this.underlines[t])}},{key:"unmark",value:function(t){var e;t in this.marks&&(e=this.marks[t],this.element.removeChild(e.element),e.listeners.forEach((function(t){t&&(e.element.removeEventListener("click",t),e.element.removeEventListener("touchstart",t))})),delete this.marks[t])}},{key:"destroy",value:function(){for(var t in this.highlights)this.unhighlight(t);for(var e in this.underlines)this.ununderline(e);for(var n in this.marks)this.unmark(n);this.blobUrl&&Object(o.revokeBlobUrl)(this.blobUrl),this.displayed&&(this.displayed=!1,this.removeListeners(),this.contents.destroy(),this.stopExpanding=!0,this.element.removeChild(this.iframe),this.pane&&(this.pane.element.remove(),this.pane=void 0),this.iframe=void 0,this.contents=void 0,this._textWidth=null,this._textHeight=null,this._width=null,this._height=null)}}])&&l(e.prototype,n),i&&l(e,i),t}();r()(h.prototype),e.a=h},function(t,e,n){var i=n(121),r=n(230),o=n(232),s=Math.max,a=Math.min;t.exports=function(t,e,n){var u,c,l,h,f,d,p=0,v=!1,g=!1,m=!0;if("function"!=typeof t)throw new TypeError("Expected a function");function y(e){var n=u,i=c;return u=c=void 0,p=e,h=t.apply(i,n)}function b(t){return p=t,f=setTimeout(x,e),v?y(t):h}function w(t){var n=t-d;return void 0===d||n>=e||n<0||g&&t-p>=l}function x(){var t=r();if(w(t))return k(t);f=setTimeout(x,function(t){var n=e-(t-d);return g?a(n,l-(t-p)):n}(t))}function k(t){return f=void 0,m&&u?y(t):(u=c=void 0,h)}function E(){var t=r(),n=w(t);if(u=arguments,c=this,d=t,n){if(void 0===f)return b(d);if(g)return clearTimeout(f),f=setTimeout(x,e),y(d)}return void 0===f&&(f=setTimeout(x,e)),h}return e=o(e)||0,i(n)&&(v=!!n.leading,l=(g="maxWait"in n)?s(o(n.maxWait)||0,e):l,m="trailing"in n?!!n.trailing:m),E.cancel=function(){void 0!==f&&clearTimeout(f),p=0,u=d=c=f=void 0},E.flush=function(){return void 0===f?h:k(r())},E}},function(t,e,n){"use strict";n(39),n(240),n(97),n(14),n(10),n(27),n(18),n(22),n(38),n(35),n(241),n(242),n(244),n(154),n(67),n(68),n(69);var i=n(0),r=n(55),o=n(1),s=n(12),a=n.n(s);function u(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}var c=Math.PI/2,l={easeOutSine:function(t){return Math.sin(t*c)},easeInOutSine:function(t){return-.5*(Math.cos(Math.PI*t)-1)},easeInOutQuint:function(t){return(t/=.5)<1?.5*Math.pow(t,5):.5*(Math.pow(t-2,5)+2)},easeInCubic:function(t){return Math.pow(t,3)}},h=function(){function t(e,n){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.settings=Object(i.extend)({duration:80,minVelocity:.2,minDistance:10,easing:l.easeInCubic},n||{}),this.supportsTouch=this.supportsTouch(),this.supportsTouch&&this.setup(e)}var e,n,r;return e=t,(n=[{key:"setup",value:function(t){this.manager=t,this.layout=this.manager.layout,this.fullsize=this.manager.settings.fullsize,this.fullsize?(this.element=this.manager.stage.element,this.scroller=window,this.disableScroll()):(this.element=this.manager.stage.container,this.scroller=this.element,this.element.style.WebkitOverflowScrolling="touch"),this.manager.settings.offset=this.layout.width,this.manager.settings.afterScrolledTimeout=2*this.settings.duration,this.isVertical="vertical"===this.manager.settings.axis,this.manager.isPaginated&&!this.isVertical&&(this.touchCanceler=!1,this.resizeCanceler=!1,this.snapping=!1,this.scrollLeft,this.scrollTop,this.startTouchX=void 0,this.startTouchY=void 0,this.startTime=void 0,this.endTouchX=void 0,this.endTouchY=void 0,this.endTime=void 0,this.addListeners())}},{key:"supportsTouch",value:function(){return!!("ontouchstart"in window||window.DocumentTouch&&document instanceof DocumentTouch)}},{key:"disableScroll",value:function(){this.element.style.overflow="hidden"}},{key:"enableScroll",value:function(){this.element.style.overflow=""}},{key:"addListeners",value:function(){this._onResize=this.onResize.bind(this),window.addEventListener("resize",this._onResize),this._onScroll=this.onScroll.bind(this),this.scroller.addEventListener("scroll",this._onScroll),this._onTouchStart=this.onTouchStart.bind(this),this.scroller.addEventListener("touchstart",this._onTouchStart,{passive:!0}),this.on("touchstart",this._onTouchStart),this._onTouchMove=this.onTouchMove.bind(this),this.scroller.addEventListener("touchmove",this._onTouchMove,{passive:!0}),this.on("touchmove",this._onTouchMove),this._onTouchEnd=this.onTouchEnd.bind(this),this.scroller.addEventListener("touchend",this._onTouchEnd,{passive:!0}),this.on("touchend",this._onTouchEnd),this._afterDisplayed=this.afterDisplayed.bind(this),this.manager.on(o.c.MANAGERS.ADDED,this._afterDisplayed)}},{key:"removeListeners",value:function(){window.removeEventListener("resize",this._onResize),this._onResize=void 0,this.scroller.removeEventListener("scroll",this._onScroll),this._onScroll=void 0,this.scroller.removeEventListener("touchstart",this._onTouchStart,{passive:!0}),this.off("touchstart",this._onTouchStart),this._onTouchStart=void 0,this.scroller.removeEventListener("touchmove",this._onTouchMove,{passive:!0}),this.off("touchmove",this._onTouchMove),this._onTouchMove=void 0,this.scroller.removeEventListener("touchend",this._onTouchEnd,{passive:!0}),this.off("touchend",this._onTouchEnd),this._onTouchEnd=void 0,this.manager.off(o.c.MANAGERS.ADDED,this._afterDisplayed),this._afterDisplayed=void 0}},{key:"afterDisplayed",value:function(t){var e=this,n=t.contents;["touchstart","touchmove","touchend"].forEach((function(t){n.on(t,(function(t){return e.triggerViewEvent(t,n)}))}))}},{key:"triggerViewEvent",value:function(t,e){this.emit(t.type,t,e)}},{key:"onScroll",value:function(t){this.scrollLeft=this.fullsize?window.scrollX:this.scroller.scrollLeft,this.scrollTop=this.fullsize?window.scrollY:this.scroller.scrollTop}},{key:"onResize",value:function(t){this.resizeCanceler=!0}},{key:"onTouchStart",value:function(t){var e=t.touches[0],n=e.screenX,i=e.screenY;this.fullsize&&this.enableScroll(),this.touchCanceler=!0,this.startTouchX||(this.startTouchX=n,this.startTouchY=i,this.startTime=this.now()),this.endTouchX=n,this.endTouchY=i,this.endTime=this.now()}},{key:"onTouchMove",value:function(t){var e=t.touches[0],n=e.screenX,i=e.screenY,r=Math.abs(i-this.endTouchY);this.touchCanceler=!0,!this.fullsize&&r<10&&(this.element.scrollLeft-=n-this.endTouchX),this.endTouchX=n,this.endTouchY=i,this.endTime=this.now()}},{key:"onTouchEnd",value:function(t){this.fullsize&&this.disableScroll(),this.touchCanceler=!1;var e=this.wasSwiped();0!==e?this.snap(e):this.snap(),this.startTouchX=void 0,this.startTouchY=void 0,this.startTime=void 0,this.endTouchX=void 0,this.endTouchY=void 0,this.endTime=void 0}},{key:"wasSwiped",value:function(){var t=this.layout.pageWidth*this.layout.divisor,e=this.endTouchX-this.startTouchX,n=Math.abs(e),i=e/(this.endTime-this.startTime),r=this.settings.minVelocity;return n<=this.settings.minDistance||n>=t?0:i>r?-1:i<-r?1:void 0}},{key:"needsSnap",value:function(){return this.scrollLeft%(this.layout.pageWidth*this.layout.divisor)!=0}},{key:"snap",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=this.scrollLeft,n=this.layout.pageWidth*this.layout.divisor,i=Math.round(e/n)*n;return t&&(i+=t*n),this.smoothScrollTo(i)}},{key:"smoothScrollTo",value:function(t){var e=new i.defer,n=this.scrollLeft,r=this.now(),o=this.settings.duration,s=this.settings.easing;return this.snapping=!0,function i(){var a=this.now(),u=Math.min(1,(a-r)/o);if(s(u),this.touchCanceler||this.resizeCanceler)return this.resizeCanceler=!1,this.snapping=!1,void e.resolve();u<1?(window.requestAnimationFrame(i.bind(this)),this.scrollTo(n+(t-n)*u,0)):(this.scrollTo(t,0),this.snapping=!1,e.resolve())}.call(this),e.promise}},{key:"scrollTo",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;this.fullsize?window.scroll(t,e):(this.scroller.scrollLeft=t,this.scroller.scrollTop=e)}},{key:"now",value:function(){return"now"in window.performance?performance.now():(new Date).getTime()}},{key:"destroy",value:function(){this.scroller&&(this.fullsize&&this.enableScroll(),this.removeListeners(),this.scroller=void 0)}}])&&u(e.prototype,n),r&&u(e,r),t}();a()(h.prototype);var f=h,d=n(123),p=n.n(d);function v(t){return(v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function g(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function m(t,e,n){return(m="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,n){var i=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=x(t)););return t}(t,e);if(i){var r=Object.getOwnPropertyDescriptor(i,e);return r.get?r.get.call(n):r.value}})(t,e,n||t)}function y(t,e){return(y=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}function b(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var n,i=x(t);if(e){var r=x(this).constructor;n=Reflect.construct(i,arguments,r)}else n=i.apply(this,arguments);return w(this,n)}}function w(t,e){if(e&&("object"===v(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function x(t){return(x=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var k=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&y(t,e)}(u,t);var e,n,s,a=b(u);function u(t){var e;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,u),(e=a.call(this,t)).name="continuous",e.settings=Object(i.extend)(e.settings||{},{infinite:!0,overflow:void 0,axis:void 0,writingMode:void 0,flow:"scrolled",offset:500,offsetDelta:250,width:void 0,height:void 0,snap:!1,afterScrolledTimeout:10,allowScriptedContent:!1,allowPopups:!1}),Object(i.extend)(e.settings,t.settings||{}),"undefined"!=t.settings.gap&&0===t.settings.gap&&(e.settings.gap=t.settings.gap),e.viewSettings={ignoreClass:e.settings.ignoreClass,axis:e.settings.axis,flow:e.settings.flow,layout:e.layout,width:0,height:0,forceEvenPages:!1,allowScriptedContent:e.settings.allowScriptedContent,allowPopups:e.settings.allowPopups},e.scrollTop=0,e.scrollLeft=0,e}return e=u,(n=[{key:"display",value:function(t,e){return r.a.prototype.display.call(this,t,e).then(function(){return this.fill()}.bind(this))}},{key:"fill",value:function(t){var e=this,n=t||new i.defer;return this.q.enqueue((function(){return e.check()})).then((function(t){t?e.fill(n):n.resolve()})),n.promise}},{key:"moveTo",value:function(t){var e=0,n=0;this.isPaginated?(e=Math.floor(t.left/this.layout.delta)*this.layout.delta,this.settings.offsetDelta):(n=t.top,t.top,this.settings.offsetDelta),(e>0||n>0)&&this.scrollBy(e,n,!0)}},{key:"afterResized",value:function(t){this.emit(o.c.MANAGERS.RESIZE,t.section)}},{key:"removeShownListeners",value:function(t){t.onDisplayed=function(){}}},{key:"add",value:function(t){var e=this,n=this.createView(t);return this.views.append(n),n.on(o.c.VIEWS.RESIZED,(function(t){n.expanded=!0})),n.on(o.c.VIEWS.AXIS,(function(t){e.updateAxis(t)})),n.on(o.c.VIEWS.WRITING_MODE,(function(t){e.updateWritingMode(t)})),n.onDisplayed=this.afterDisplayed.bind(this),n.onResize=this.afterResized.bind(this),n.display(this.request)}},{key:"append",value:function(t){var e=this,n=this.createView(t);return n.on(o.c.VIEWS.RESIZED,(function(t){n.expanded=!0})),n.on(o.c.VIEWS.AXIS,(function(t){e.updateAxis(t)})),n.on(o.c.VIEWS.WRITING_MODE,(function(t){e.updateWritingMode(t)})),this.views.append(n),n.onDisplayed=this.afterDisplayed.bind(this),n}},{key:"prepend",value:function(t){var e=this,n=this.createView(t);return n.on(o.c.VIEWS.RESIZED,(function(t){e.counter(t),n.expanded=!0})),n.on(o.c.VIEWS.AXIS,(function(t){e.updateAxis(t)})),n.on(o.c.VIEWS.WRITING_MODE,(function(t){e.updateWritingMode(t)})),this.views.prepend(n),n.onDisplayed=this.afterDisplayed.bind(this),n}},{key:"counter",value:function(t){"vertical"===this.settings.axis?this.scrollBy(0,t.heightDelta,!0):this.scrollBy(t.widthDelta,0,!0)}},{key:"update",value:function(t){for(var e,n=this.bounds(),r=this.views.all(),o=r.length,s=[],a=void 0!==t?t:this.settings.offset||0,u=new i.defer,c=[],l=0;l<o;l++)if(e=r[l],!0===this.isVisible(e,a,a,n)){if(e.displayed)e.show();else{var h=e.display(this.request).then((function(t){t.show()}),(function(t){e.hide()}));c.push(h)}s.push(e)}else this.q.enqueue(e.destroy.bind(e)),clearTimeout(this.trimTimeout),this.trimTimeout=setTimeout(function(){this.q.enqueue(this.trim.bind(this))}.bind(this),250);return c.length?Promise.all(c).catch((function(t){u.reject(t)})):(u.resolve(),u.promise)}},{key:"check",value:function(t,e){var n=this,r=new i.defer,o=[],s="horizontal"===this.settings.axis,a=this.settings.offset||0;t&&s&&(a=t),e&&!s&&(a=e);var u=this._bounds,c=s?this.scrollLeft:this.scrollTop,l=s?Math.floor(u.width):u.height,h=s?this.container.scrollWidth:this.container.scrollHeight,f=this.writingMode&&0===this.writingMode.indexOf("vertical")?"vertical":"horizontal",d=this.settings.rtlScrollType,p="rtl"===this.settings.direction;this.settings.fullsize?(s&&p&&"negative"===d||!s&&p&&"default"===d)&&(c*=-1):(p&&"default"===d&&"horizontal"===f&&(c=h-l-c),p&&"negative"===d&&"horizontal"===f&&(c*=-1));var v,g,m,y,b=c-a;c+l+a>=h&&(v=n.views.last(),(g=v&&v.section.next())&&o.push(n.append(g))),b<0&&(m=n.views.first(),(y=m&&m.section.prev())&&o.push(n.prepend(y)));var w=o.map((function(t){return t.display(n.request)}));return o.length?Promise.all(w).then((function(){return n.check()})).then((function(){return n.update(a)}),(function(t){return t})):(this.q.enqueue(function(){this.update()}.bind(this)),r.resolve(!1),r.promise)}},{key:"trim",value:function(){for(var t=new i.defer,e=this.views.displayed(),n=e[0],r=e[e.length-1],o=this.views.indexOf(n),s=this.views.indexOf(r),a=this.views.slice(0,o),u=this.views.slice(s+1),c=0;c<a.length-1;c++)this.erase(a[c],a);for(var l=1;l<u.length;l++)this.erase(u[l]);return t.resolve(),t.promise}},{key:"erase",value:function(t,e){var n,i;this.settings.fullsize?(n=window.scrollY,i=window.scrollX):(n=this.container.scrollTop,i=this.container.scrollLeft);var r=t.bounds();this.views.remove(t),e&&("vertical"===this.settings.axis?this.scrollTo(0,n-r.height,!0):"rtl"===this.settings.direction?this.settings.fullsize?this.scrollTo(i+Math.floor(r.width),0,!0):this.scrollTo(i,0,!0):this.scrollTo(i-Math.floor(r.width),0,!0))}},{key:"addEventListeners",value:function(t){window.addEventListener("unload",function(t){this.ignore=!0,this.destroy()}.bind(this)),this.addScrollListeners(),this.isPaginated&&this.settings.snap&&(this.snapper=new f(this,this.settings.snap&&"object"===v(this.settings.snap)&&this.settings.snap))}},{key:"addScrollListeners",value:function(){var t;this.tick=i.requestAnimationFrame;var e="rtl"===this.settings.direction&&"default"===this.settings.rtlScrollType?-1:1;this.scrollDeltaVert=0,this.scrollDeltaHorz=0,this.settings.fullsize?(t=window,this.scrollTop=window.scrollY*e,this.scrollLeft=window.scrollX*e):(t=this.container,this.scrollTop=this.container.scrollTop,this.scrollLeft=this.container.scrollLeft),this._onScroll=this.onScroll.bind(this),t.addEventListener("scroll",this._onScroll),this._scrolled=p()(this.scrolled.bind(this),30),this.didScroll=!1}},{key:"removeEventListeners",value:function(){(this.settings.fullsize?window:this.container).removeEventListener("scroll",this._onScroll),this._onScroll=void 0}},{key:"onScroll",value:function(){var t,e,n="rtl"===this.settings.direction&&"default"===this.settings.rtlScrollType?-1:1;this.settings.fullsize?(t=window.scrollY*n,e=window.scrollX*n):(t=this.container.scrollTop,e=this.container.scrollLeft),this.scrollTop=t,this.scrollLeft=e,this.ignore?this.ignore=!1:this._scrolled(),this.scrollDeltaVert+=Math.abs(t-this.prevScrollTop),this.scrollDeltaHorz+=Math.abs(e-this.prevScrollLeft),this.prevScrollTop=t,this.prevScrollLeft=e,clearTimeout(this.scrollTimeout),this.scrollTimeout=setTimeout(function(){this.scrollDeltaVert=0,this.scrollDeltaHorz=0}.bind(this),150),clearTimeout(this.afterScrolled),this.didScroll=!1}},{key:"scrolled",value:function(){this.q.enqueue(function(){return this.check()}.bind(this)),this.emit(o.c.MANAGERS.SCROLL,{top:this.scrollTop,left:this.scrollLeft}),clearTimeout(this.afterScrolled),this.afterScrolled=setTimeout(function(){this.snapper&&this.snapper.supportsTouch&&this.snapper.needsSnap()||this.emit(o.c.MANAGERS.SCROLLED,{top:this.scrollTop,left:this.scrollLeft})}.bind(this),this.settings.afterScrolledTimeout)}},{key:"next",value:function(){var t="pre-paginated"===this.layout.props.name&&this.layout.props.spread?2*this.layout.props.delta:this.layout.props.delta;this.views.length&&(this.isPaginated&&"horizontal"===this.settings.axis?this.scrollBy(t,0,!0):this.scrollBy(0,this.layout.height,!0),this.q.enqueue(function(){return this.check()}.bind(this)))}},{key:"prev",value:function(){var t="pre-paginated"===this.layout.props.name&&this.layout.props.spread?2*this.layout.props.delta:this.layout.props.delta;this.views.length&&(this.isPaginated&&"horizontal"===this.settings.axis?this.scrollBy(-t,0,!0):this.scrollBy(0,-this.layout.height,!0),this.q.enqueue(function(){return this.check()}.bind(this)))}},{key:"updateFlow",value:function(t){this.rendered&&this.snapper&&(this.snapper.destroy(),this.snapper=void 0),m(x(u.prototype),"updateFlow",this).call(this,t,"scroll"),this.rendered&&this.isPaginated&&this.settings.snap&&(this.snapper=new f(this,this.settings.snap&&"object"===v(this.settings.snap)&&this.settings.snap))}},{key:"destroy",value:function(){m(x(u.prototype),"destroy",this).call(this),this.snapper&&this.snapper.destroy()}}])&&g(e.prototype,n),s&&g(e,s),u}(r.a);e.a=k},function(t,e,n){(function(e){t.exports=function t(e,n,i){function r(s,a){if(!n[s]){if(!e[s]){if(o)return o(s,!0);var u=new Error("Cannot find module '"+s+"'");throw u.code="MODULE_NOT_FOUND",u}var c=n[s]={exports:{}};e[s][0].call(c.exports,(function(t){var n=e[s][1][t];return r(n||t)}),c,c.exports,t,e,n,i)}return n[s].exports}for(var o=!1,s=0;s<i.length;s++)r(i[s]);return r}({1:[function(t,n,i){(function(t){"use strict";var e,i,r=t.MutationObserver||t.WebKitMutationObserver;if(r){var o=0,s=new r(l),a=t.document.createTextNode("");s.observe(a,{characterData:!0}),e=function(){a.data=o=++o%2}}else if(t.setImmediate||void 0===t.MessageChannel)e="document"in t&&"onreadystatechange"in t.document.createElement("script")?function(){var e=t.document.createElement("script");e.onreadystatechange=function(){l(),e.onreadystatechange=null,e.parentNode.removeChild(e),e=null},t.document.documentElement.appendChild(e)}:function(){setTimeout(l,0)};else{var u=new t.MessageChannel;u.port1.onmessage=l,e=function(){u.port2.postMessage(0)}}var c=[];function l(){var t,e;i=!0;for(var n=c.length;n;){for(e=c,c=[],t=-1;++t<n;)e[t]();n=c.length}i=!1}n.exports=function(t){1!==c.push(t)||i||e()}}).call(this,void 0!==e?e:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],2:[function(t,e,n){"use strict";var i=t(1);function r(){}var o={},s=["REJECTED"],a=["FULFILLED"],u=["PENDING"];function c(t){if("function"!=typeof t)throw new TypeError("resolver must be a function");this.state=u,this.queue=[],this.outcome=void 0,t!==r&&d(this,t)}function l(t,e,n){this.promise=t,"function"==typeof e&&(this.onFulfilled=e,this.callFulfilled=this.otherCallFulfilled),"function"==typeof n&&(this.onRejected=n,this.callRejected=this.otherCallRejected)}function h(t,e,n){i((function(){var i;try{i=e(n)}catch(e){return o.reject(t,e)}i===t?o.reject(t,new TypeError("Cannot resolve promise with itself")):o.resolve(t,i)}))}function f(t){var e=t&&t.then;if(t&&("object"==typeof t||"function"==typeof t)&&"function"==typeof e)return function(){e.apply(t,arguments)}}function d(t,e){var n=!1;function i(e){n||(n=!0,o.reject(t,e))}function r(e){n||(n=!0,o.resolve(t,e))}var s=p((function(){e(r,i)}));"error"===s.status&&i(s.value)}function p(t,e){var n={};try{n.value=t(e),n.status="success"}catch(t){n.status="error",n.value=t}return n}e.exports=c,c.prototype.catch=function(t){return this.then(null,t)},c.prototype.then=function(t,e){if("function"!=typeof t&&this.state===a||"function"!=typeof e&&this.state===s)return this;var n=new this.constructor(r);return this.state!==u?h(n,this.state===a?t:e,this.outcome):this.queue.push(new l(n,t,e)),n},l.prototype.callFulfilled=function(t){o.resolve(this.promise,t)},l.prototype.otherCallFulfilled=function(t){h(this.promise,this.onFulfilled,t)},l.prototype.callRejected=function(t){o.reject(this.promise,t)},l.prototype.otherCallRejected=function(t){h(this.promise,this.onRejected,t)},o.resolve=function(t,e){var n=p(f,e);if("error"===n.status)return o.reject(t,n.value);var i=n.value;if(i)d(t,i);else{t.state=a,t.outcome=e;for(var r=-1,s=t.queue.length;++r<s;)t.queue[r].callFulfilled(e)}return t},o.reject=function(t,e){t.state=s,t.outcome=e;for(var n=-1,i=t.queue.length;++n<i;)t.queue[n].callRejected(e);return t},c.resolve=function(t){return t instanceof this?t:o.resolve(new this(r),t)},c.reject=function(t){var e=new this(r);return o.reject(e,t)},c.all=function(t){var e=this;if("[object Array]"!==Object.prototype.toString.call(t))return this.reject(new TypeError("must be an array"));var n=t.length,i=!1;if(!n)return this.resolve([]);for(var s=new Array(n),a=0,u=-1,c=new this(r);++u<n;)l(t[u],u);return c;function l(t,r){e.resolve(t).then((function(t){s[r]=t,++a!==n||i||(i=!0,o.resolve(c,s))}),(function(t){i||(i=!0,o.reject(c,t))}))}},c.race=function(t){var e=this;if("[object Array]"!==Object.prototype.toString.call(t))return this.reject(new TypeError("must be an array"));var n=t.length,i=!1;if(!n)return this.resolve([]);for(var s,a=-1,u=new this(r);++a<n;)s=t[a],e.resolve(s).then((function(t){i||(i=!0,o.resolve(u,t))}),(function(t){i||(i=!0,o.reject(u,t))}));return u}},{1:1}],3:[function(t,n,i){(function(e){"use strict";"function"!=typeof e.Promise&&(e.Promise=t(2))}).call(this,void 0!==e?e:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{2:2}],4:[function(t,e,n){"use strict";var i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},r=function(){try{if("undefined"!=typeof indexedDB)return indexedDB;if("undefined"!=typeof webkitIndexedDB)return webkitIndexedDB;if("undefined"!=typeof mozIndexedDB)return mozIndexedDB;if("undefined"!=typeof OIndexedDB)return OIndexedDB;if("undefined"!=typeof msIndexedDB)return msIndexedDB}catch(t){return}}();function o(t,e){t=t||[],e=e||{};try{return new Blob(t,e)}catch(r){if("TypeError"!==r.name)throw r;for(var n=new("undefined"!=typeof BlobBuilder?BlobBuilder:"undefined"!=typeof MSBlobBuilder?MSBlobBuilder:"undefined"!=typeof MozBlobBuilder?MozBlobBuilder:WebKitBlobBuilder),i=0;i<t.length;i+=1)n.append(t[i]);return n.getBlob(e.type)}}"undefined"==typeof Promise&&t(3);var s=Promise;function a(t,e){e&&t.then((function(t){e(null,t)}),(function(t){e(t)}))}function u(t,e,n){"function"==typeof e&&t.then(e),"function"==typeof n&&t.catch(n)}function c(t){return"string"!=typeof t&&(console.warn(t+" used as a key, but it is not a string."),t=String(t)),t}function l(){if(arguments.length&&"function"==typeof arguments[arguments.length-1])return arguments[arguments.length-1]}var h=void 0,f={},d=Object.prototype.toString;function p(t){return"boolean"==typeof h?s.resolve(h):function(t){return new s((function(e){var n=t.transaction("local-forage-detect-blob-support","readwrite"),i=o([""]);n.objectStore("local-forage-detect-blob-support").put(i,"key"),n.onabort=function(t){t.preventDefault(),t.stopPropagation(),e(!1)},n.oncomplete=function(){var t=navigator.userAgent.match(/Chrome\/(\d+)/),n=navigator.userAgent.match(/Edge\//);e(n||!t||parseInt(t[1],10)>=43)}})).catch((function(){return!1}))}(t).then((function(t){return h=t}))}function v(t){var e=f[t.name],n={};n.promise=new s((function(t,e){n.resolve=t,n.reject=e})),e.deferredOperations.push(n),e.dbReady?e.dbReady=e.dbReady.then((function(){return n.promise})):e.dbReady=n.promise}function g(t){var e=f[t.name].deferredOperations.pop();if(e)return e.resolve(),e.promise}function m(t,e){var n=f[t.name].deferredOperations.pop();if(n)return n.reject(e),n.promise}function y(t,e){return new s((function(n,i){if(f[t.name]=f[t.name]||{forages:[],db:null,dbReady:null,deferredOperations:[]},t.db){if(!e)return n(t.db);v(t),t.db.close()}var o=[t.name];e&&o.push(t.version);var s=r.open.apply(r,o);e&&(s.onupgradeneeded=function(e){var n=s.result;try{n.createObjectStore(t.storeName),e.oldVersion<=1&&n.createObjectStore("local-forage-detect-blob-support")}catch(n){if("ConstraintError"!==n.name)throw n;console.warn('The database "'+t.name+'" has been upgraded from version '+e.oldVersion+" to version "+e.newVersion+', but the storage "'+t.storeName+'" already exists.')}}),s.onerror=function(t){t.preventDefault(),i(s.error)},s.onsuccess=function(){var e=s.result;e.onversionchange=function(t){t.target.close()},n(e),g(t)}}))}function b(t){return y(t,!1)}function w(t){return y(t,!0)}function x(t,e){if(!t.db)return!0;var n=!t.db.objectStoreNames.contains(t.storeName),i=t.version<t.db.version,r=t.version>t.db.version;if(i&&(t.version!==e&&console.warn('The database "'+t.name+"\" can't be downgraded from version "+t.db.version+" to version "+t.version+"."),t.version=t.db.version),r||n){if(n){var o=t.db.version+1;o>t.version&&(t.version=o)}return!0}return!1}function k(t){return o([function(t){for(var e=t.length,n=new ArrayBuffer(e),i=new Uint8Array(n),r=0;r<e;r++)i[r]=t.charCodeAt(r);return n}(atob(t.data))],{type:t.type})}function E(t){return t&&t.__local_forage_encoded_blob}function S(t){var e=this,n=e._initReady().then((function(){var t=f[e._dbInfo.name];if(t&&t.dbReady)return t.dbReady}));return u(n,t,t),n}function O(t,e,n,i){void 0===i&&(i=1);try{var r=t.db.transaction(t.storeName,e);n(null,r)}catch(r){if(i>0&&(!t.db||"InvalidStateError"===r.name||"NotFoundError"===r.name))return s.resolve().then((function(){if(!t.db||"NotFoundError"===r.name&&!t.db.objectStoreNames.contains(t.storeName)&&t.version<=t.db.version)return t.db&&(t.version=t.db.version+1),w(t)})).then((function(){return function(t){v(t);for(var e=f[t.name],n=e.forages,i=0;i<n.length;i++){var r=n[i];r._dbInfo.db&&(r._dbInfo.db.close(),r._dbInfo.db=null)}return t.db=null,b(t).then((function(e){return t.db=e,x(t)?w(t):e})).then((function(i){t.db=e.db=i;for(var r=0;r<n.length;r++)n[r]._dbInfo.db=i})).catch((function(e){throw m(t,e),e}))}(t).then((function(){O(t,e,n,i-1)}))})).catch(n);n(r)}}var T={_driver:"asyncStorage",_initStorage:function(t){var e=this,n={db:null};if(t)for(var i in t)n[i]=t[i];var r=f[n.name];r||(r={forages:[],db:null,dbReady:null,deferredOperations:[]},f[n.name]=r),r.forages.push(e),e._initReady||(e._initReady=e.ready,e.ready=S);var o=[];function a(){return s.resolve()}for(var u=0;u<r.forages.length;u++){var c=r.forages[u];c!==e&&o.push(c._initReady().catch(a))}var l=r.forages.slice(0);return s.all(o).then((function(){return n.db=r.db,b(n)})).then((function(t){return n.db=t,x(n,e._defaultConfig.version)?w(n):t})).then((function(t){n.db=r.db=t,e._dbInfo=n;for(var i=0;i<l.length;i++){var o=l[i];o!==e&&(o._dbInfo.db=n.db,o._dbInfo.version=n.version)}}))},_support:function(){try{if(!r||!r.open)return!1;var t="undefined"!=typeof openDatabase&&/(Safari|iPhone|iPad|iPod)/.test(navigator.userAgent)&&!/Chrome/.test(navigator.userAgent)&&!/BlackBerry/.test(navigator.platform),e="function"==typeof fetch&&-1!==fetch.toString().indexOf("[native code");return(!t||e)&&"undefined"!=typeof indexedDB&&"undefined"!=typeof IDBKeyRange}catch(t){return!1}}(),iterate:function(t,e){var n=this,i=new s((function(e,i){n.ready().then((function(){O(n._dbInfo,"readonly",(function(r,o){if(r)return i(r);try{var s=o.objectStore(n._dbInfo.storeName).openCursor(),a=1;s.onsuccess=function(){var n=s.result;if(n){var i=n.value;E(i)&&(i=k(i));var r=t(i,n.key,a++);void 0!==r?e(r):n.continue()}else e()},s.onerror=function(){i(s.error)}}catch(t){i(t)}}))})).catch(i)}));return a(i,e),i},getItem:function(t,e){var n=this;t=c(t);var i=new s((function(e,i){n.ready().then((function(){O(n._dbInfo,"readonly",(function(r,o){if(r)return i(r);try{var s=o.objectStore(n._dbInfo.storeName).get(t);s.onsuccess=function(){var t=s.result;void 0===t&&(t=null),E(t)&&(t=k(t)),e(t)},s.onerror=function(){i(s.error)}}catch(t){i(t)}}))})).catch(i)}));return a(i,e),i},setItem:function(t,e,n){var i=this;t=c(t);var r=new s((function(n,r){var o;i.ready().then((function(){return o=i._dbInfo,"[object Blob]"===d.call(e)?p(o.db).then((function(t){return t?e:(n=e,new s((function(t,e){var i=new FileReader;i.onerror=e,i.onloadend=function(e){var i=btoa(e.target.result||"");t({__local_forage_encoded_blob:!0,data:i,type:n.type})},i.readAsBinaryString(n)})));var n})):e})).then((function(e){O(i._dbInfo,"readwrite",(function(o,s){if(o)return r(o);try{var a=s.objectStore(i._dbInfo.storeName);null===e&&(e=void 0);var u=a.put(e,t);s.oncomplete=function(){void 0===e&&(e=null),n(e)},s.onabort=s.onerror=function(){var t=u.error?u.error:u.transaction.error;r(t)}}catch(t){r(t)}}))})).catch(r)}));return a(r,n),r},removeItem:function(t,e){var n=this;t=c(t);var i=new s((function(e,i){n.ready().then((function(){O(n._dbInfo,"readwrite",(function(r,o){if(r)return i(r);try{var s=o.objectStore(n._dbInfo.storeName).delete(t);o.oncomplete=function(){e()},o.onerror=function(){i(s.error)},o.onabort=function(){var t=s.error?s.error:s.transaction.error;i(t)}}catch(t){i(t)}}))})).catch(i)}));return a(i,e),i},clear:function(t){var e=this,n=new s((function(t,n){e.ready().then((function(){O(e._dbInfo,"readwrite",(function(i,r){if(i)return n(i);try{var o=r.objectStore(e._dbInfo.storeName).clear();r.oncomplete=function(){t()},r.onabort=r.onerror=function(){var t=o.error?o.error:o.transaction.error;n(t)}}catch(t){n(t)}}))})).catch(n)}));return a(n,t),n},length:function(t){var e=this,n=new s((function(t,n){e.ready().then((function(){O(e._dbInfo,"readonly",(function(i,r){if(i)return n(i);try{var o=r.objectStore(e._dbInfo.storeName).count();o.onsuccess=function(){t(o.result)},o.onerror=function(){n(o.error)}}catch(t){n(t)}}))})).catch(n)}));return a(n,t),n},key:function(t,e){var n=this,i=new s((function(e,i){t<0?e(null):n.ready().then((function(){O(n._dbInfo,"readonly",(function(r,o){if(r)return i(r);try{var s=o.objectStore(n._dbInfo.storeName),a=!1,u=s.openKeyCursor();u.onsuccess=function(){var n=u.result;n?0===t||a?e(n.key):(a=!0,n.advance(t)):e(null)},u.onerror=function(){i(u.error)}}catch(t){i(t)}}))})).catch(i)}));return a(i,e),i},keys:function(t){var e=this,n=new s((function(t,n){e.ready().then((function(){O(e._dbInfo,"readonly",(function(i,r){if(i)return n(i);try{var o=r.objectStore(e._dbInfo.storeName).openKeyCursor(),s=[];o.onsuccess=function(){var e=o.result;e?(s.push(e.key),e.continue()):t(s)},o.onerror=function(){n(o.error)}}catch(t){n(t)}}))})).catch(n)}));return a(n,t),n},dropInstance:function(t,e){e=l.apply(this,arguments);var n=this.config();(t="function"!=typeof t&&t||{}).name||(t.name=t.name||n.name,t.storeName=t.storeName||n.storeName);var i,o=this;if(t.name){var u=t.name===n.name&&o._dbInfo.db,c=u?s.resolve(o._dbInfo.db):b(t).then((function(e){var n=f[t.name],i=n.forages;n.db=e;for(var r=0;r<i.length;r++)i[r]._dbInfo.db=e;return e}));i=t.storeName?c.then((function(e){if(e.objectStoreNames.contains(t.storeName)){var n=e.version+1;v(t);var i=f[t.name],o=i.forages;e.close();for(var a=0;a<o.length;a++){var u=o[a];u._dbInfo.db=null,u._dbInfo.version=n}return new s((function(e,i){var o=r.open(t.name,n);o.onerror=function(t){o.result.close(),i(t)},o.onupgradeneeded=function(){o.result.deleteObjectStore(t.storeName)},o.onsuccess=function(){var t=o.result;t.close(),e(t)}})).then((function(t){i.db=t;for(var e=0;e<o.length;e++){var n=o[e];n._dbInfo.db=t,g(n._dbInfo)}})).catch((function(e){throw(m(t,e)||s.resolve()).catch((function(){})),e}))}})):c.then((function(e){v(t);var n=f[t.name],i=n.forages;e.close();for(var o=0;o<i.length;o++)i[o]._dbInfo.db=null;return new s((function(e,n){var i=r.deleteDatabase(t.name);i.onerror=function(){var t=i.result;t&&t.close(),n(i.error)},i.onblocked=function(){console.warn('dropInstance blocked for database "'+t.name+'" until all open connections are closed')},i.onsuccess=function(){var t=i.result;t&&t.close(),e(t)}})).then((function(t){n.db=t;for(var e=0;e<i.length;e++)g(i[e]._dbInfo)})).catch((function(e){throw(m(t,e)||s.resolve()).catch((function(){})),e}))}))}else i=s.reject("Invalid arguments");return a(i,e),i}},_="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",N=/^~~local_forage_type~([^~]+)~/,C="__lfsc__:".length,R=C+"arbf".length,I=Object.prototype.toString;function A(t){var e,n,i,r,o,s=.75*t.length,a=t.length,u=0;"="===t[t.length-1]&&(s--,"="===t[t.length-2]&&s--);var c=new ArrayBuffer(s),l=new Uint8Array(c);for(e=0;e<a;e+=4)n=_.indexOf(t[e]),i=_.indexOf(t[e+1]),r=_.indexOf(t[e+2]),o=_.indexOf(t[e+3]),l[u++]=n<<2|i>>4,l[u++]=(15&i)<<4|r>>2,l[u++]=(3&r)<<6|63&o;return c}function j(t){var e,n=new Uint8Array(t),i="";for(e=0;e<n.length;e+=3)i+=_[n[e]>>2],i+=_[(3&n[e])<<4|n[e+1]>>4],i+=_[(15&n[e+1])<<2|n[e+2]>>6],i+=_[63&n[e+2]];return n.length%3==2?i=i.substring(0,i.length-1)+"=":n.length%3==1&&(i=i.substring(0,i.length-2)+"=="),i}var L={serialize:function(t,e){var n="";if(t&&(n=I.call(t)),t&&("[object ArrayBuffer]"===n||t.buffer&&"[object ArrayBuffer]"===I.call(t.buffer))){var i,r="__lfsc__:";t instanceof ArrayBuffer?(i=t,r+="arbf"):(i=t.buffer,"[object Int8Array]"===n?r+="si08":"[object Uint8Array]"===n?r+="ui08":"[object Uint8ClampedArray]"===n?r+="uic8":"[object Int16Array]"===n?r+="si16":"[object Uint16Array]"===n?r+="ur16":"[object Int32Array]"===n?r+="si32":"[object Uint32Array]"===n?r+="ui32":"[object Float32Array]"===n?r+="fl32":"[object Float64Array]"===n?r+="fl64":e(new Error("Failed to get type for BinaryArray"))),e(r+j(i))}else if("[object Blob]"===n){var o=new FileReader;o.onload=function(){var n="~~local_forage_type~"+t.type+"~"+j(this.result);e("__lfsc__:blob"+n)},o.readAsArrayBuffer(t)}else try{e(JSON.stringify(t))}catch(n){console.error("Couldn't convert value into a JSON string: ",t),e(null,n)}},deserialize:function(t){if("__lfsc__:"!==t.substring(0,C))return JSON.parse(t);var e,n=t.substring(R),i=t.substring(C,R);if("blob"===i&&N.test(n)){var r=n.match(N);e=r[1],n=n.substring(r[0].length)}var s=A(n);switch(i){case"arbf":return s;case"blob":return o([s],{type:e});case"si08":return new Int8Array(s);case"ui08":return new Uint8Array(s);case"uic8":return new Uint8ClampedArray(s);case"si16":return new Int16Array(s);case"ur16":return new Uint16Array(s);case"si32":return new Int32Array(s);case"ui32":return new Uint32Array(s);case"fl32":return new Float32Array(s);case"fl64":return new Float64Array(s);default:throw new Error("Unkown type: "+i)}},stringToBuffer:A,bufferToString:j};function P(t,e,n,i){t.executeSql("CREATE TABLE IF NOT EXISTS "+e.storeName+" (id INTEGER PRIMARY KEY, key unique, value)",[],n,i)}function D(t,e,n,i,r,o){t.executeSql(n,i,r,(function(t,s){s.code===s.SYNTAX_ERR?t.executeSql("SELECT name FROM sqlite_master WHERE type='table' AND name = ?",[e.storeName],(function(t,a){a.rows.length?o(t,s):P(t,e,(function(){t.executeSql(n,i,r,o)}),o)}),o):o(t,s)}),o)}function M(t,e,n,i){var r=this;t=c(t);var o=new s((function(o,s){r.ready().then((function(){void 0===e&&(e=null);var a=e,u=r._dbInfo;u.serializer.serialize(e,(function(e,c){c?s(c):u.db.transaction((function(n){D(n,u,"INSERT OR REPLACE INTO "+u.storeName+" (key, value) VALUES (?, ?)",[t,e],(function(){o(a)}),(function(t,e){s(e)}))}),(function(e){if(e.code===e.QUOTA_ERR){if(i>0)return void o(M.apply(r,[t,a,n,i-1]));s(e)}}))}))})).catch(s)}));return a(o,n),o}function z(t){return new s((function(e,n){t.transaction((function(i){i.executeSql("SELECT name FROM sqlite_master WHERE type='table' AND name <> '__WebKitDatabaseInfoTable__'",[],(function(n,i){for(var r=[],o=0;o<i.rows.length;o++)r.push(i.rows.item(o).name);e({db:t,storeNames:r})}),(function(t,e){n(e)}))}),(function(t){n(t)}))}))}var B={_driver:"webSQLStorage",_initStorage:function(t){var e=this,n={db:null};if(t)for(var i in t)n[i]="string"!=typeof t[i]?t[i].toString():t[i];var r=new s((function(t,i){try{n.db=openDatabase(n.name,String(n.version),n.description,n.size)}catch(t){return i(t)}n.db.transaction((function(r){P(r,n,(function(){e._dbInfo=n,t()}),(function(t,e){i(e)}))}),i)}));return n.serializer=L,r},_support:"function"==typeof openDatabase,iterate:function(t,e){var n=this,i=new s((function(e,i){n.ready().then((function(){var r=n._dbInfo;r.db.transaction((function(n){D(n,r,"SELECT * FROM "+r.storeName,[],(function(n,i){for(var o=i.rows,s=o.length,a=0;a<s;a++){var u=o.item(a),c=u.value;if(c&&(c=r.serializer.deserialize(c)),void 0!==(c=t(c,u.key,a+1)))return void e(c)}e()}),(function(t,e){i(e)}))}))})).catch(i)}));return a(i,e),i},getItem:function(t,e){var n=this;t=c(t);var i=new s((function(e,i){n.ready().then((function(){var r=n._dbInfo;r.db.transaction((function(n){D(n,r,"SELECT * FROM "+r.storeName+" WHERE key = ? LIMIT 1",[t],(function(t,n){var i=n.rows.length?n.rows.item(0).value:null;i&&(i=r.serializer.deserialize(i)),e(i)}),(function(t,e){i(e)}))}))})).catch(i)}));return a(i,e),i},setItem:function(t,e,n){return M.apply(this,[t,e,n,1])},removeItem:function(t,e){var n=this;t=c(t);var i=new s((function(e,i){n.ready().then((function(){var r=n._dbInfo;r.db.transaction((function(n){D(n,r,"DELETE FROM "+r.storeName+" WHERE key = ?",[t],(function(){e()}),(function(t,e){i(e)}))}))})).catch(i)}));return a(i,e),i},clear:function(t){var e=this,n=new s((function(t,n){e.ready().then((function(){var i=e._dbInfo;i.db.transaction((function(e){D(e,i,"DELETE FROM "+i.storeName,[],(function(){t()}),(function(t,e){n(e)}))}))})).catch(n)}));return a(n,t),n},length:function(t){var e=this,n=new s((function(t,n){e.ready().then((function(){var i=e._dbInfo;i.db.transaction((function(e){D(e,i,"SELECT COUNT(key) as c FROM "+i.storeName,[],(function(e,n){var i=n.rows.item(0).c;t(i)}),(function(t,e){n(e)}))}))})).catch(n)}));return a(n,t),n},key:function(t,e){var n=this,i=new s((function(e,i){n.ready().then((function(){var r=n._dbInfo;r.db.transaction((function(n){D(n,r,"SELECT key FROM "+r.storeName+" WHERE id = ? LIMIT 1",[t+1],(function(t,n){var i=n.rows.length?n.rows.item(0).key:null;e(i)}),(function(t,e){i(e)}))}))})).catch(i)}));return a(i,e),i},keys:function(t){var e=this,n=new s((function(t,n){e.ready().then((function(){var i=e._dbInfo;i.db.transaction((function(e){D(e,i,"SELECT key FROM "+i.storeName,[],(function(e,n){for(var i=[],r=0;r<n.rows.length;r++)i.push(n.rows.item(r).key);t(i)}),(function(t,e){n(e)}))}))})).catch(n)}));return a(n,t),n},dropInstance:function(t,e){e=l.apply(this,arguments);var n=this.config();(t="function"!=typeof t&&t||{}).name||(t.name=t.name||n.name,t.storeName=t.storeName||n.storeName);var i,r=this;return a(i=t.name?new s((function(e){var i;i=t.name===n.name?r._dbInfo.db:openDatabase(t.name,"","",0),t.storeName?e({db:i,storeNames:[t.storeName]}):e(z(i))})).then((function(t){return new s((function(e,n){t.db.transaction((function(i){function r(t){return new s((function(e,n){i.executeSql("DROP TABLE IF EXISTS "+t,[],(function(){e()}),(function(t,e){n(e)}))}))}for(var o=[],a=0,u=t.storeNames.length;a<u;a++)o.push(r(t.storeNames[a]));s.all(o).then((function(){e()})).catch((function(t){n(t)}))}),(function(t){n(t)}))}))})):s.reject("Invalid arguments"),e),i}};function q(t,e){var n=t.name+"/";return t.storeName!==e.storeName&&(n+=t.storeName+"/"),n}function U(){return!function(){try{return localStorage.setItem("_localforage_support_test",!0),localStorage.removeItem("_localforage_support_test"),!1}catch(t){return!0}}()||localStorage.length>0}var F={_driver:"localStorageWrapper",_initStorage:function(t){var e={};if(t)for(var n in t)e[n]=t[n];return e.keyPrefix=q(t,this._defaultConfig),U()?(this._dbInfo=e,e.serializer=L,s.resolve()):s.reject()},_support:function(){try{return"undefined"!=typeof localStorage&&"setItem"in localStorage&&!!localStorage.setItem}catch(t){return!1}}(),iterate:function(t,e){var n=this,i=n.ready().then((function(){for(var e=n._dbInfo,i=e.keyPrefix,r=i.length,o=localStorage.length,s=1,a=0;a<o;a++){var u=localStorage.key(a);if(0===u.indexOf(i)){var c=localStorage.getItem(u);if(c&&(c=e.serializer.deserialize(c)),void 0!==(c=t(c,u.substring(r),s++)))return c}}}));return a(i,e),i},getItem:function(t,e){var n=this;t=c(t);var i=n.ready().then((function(){var e=n._dbInfo,i=localStorage.getItem(e.keyPrefix+t);return i&&(i=e.serializer.deserialize(i)),i}));return a(i,e),i},setItem:function(t,e,n){var i=this;t=c(t);var r=i.ready().then((function(){void 0===e&&(e=null);var n=e;return new s((function(r,o){var s=i._dbInfo;s.serializer.serialize(e,(function(e,i){if(i)o(i);else try{localStorage.setItem(s.keyPrefix+t,e),r(n)}catch(t){"QuotaExceededError"!==t.name&&"NS_ERROR_DOM_QUOTA_REACHED"!==t.name||o(t),o(t)}}))}))}));return a(r,n),r},removeItem:function(t,e){var n=this;t=c(t);var i=n.ready().then((function(){var e=n._dbInfo;localStorage.removeItem(e.keyPrefix+t)}));return a(i,e),i},clear:function(t){var e=this,n=e.ready().then((function(){for(var t=e._dbInfo.keyPrefix,n=localStorage.length-1;n>=0;n--){var i=localStorage.key(n);0===i.indexOf(t)&&localStorage.removeItem(i)}}));return a(n,t),n},length:function(t){var e=this.keys().then((function(t){return t.length}));return a(e,t),e},key:function(t,e){var n=this,i=n.ready().then((function(){var e,i=n._dbInfo;try{e=localStorage.key(t)}catch(t){e=null}return e&&(e=e.substring(i.keyPrefix.length)),e}));return a(i,e),i},keys:function(t){var e=this,n=e.ready().then((function(){for(var t=e._dbInfo,n=localStorage.length,i=[],r=0;r<n;r++){var o=localStorage.key(r);0===o.indexOf(t.keyPrefix)&&i.push(o.substring(t.keyPrefix.length))}return i}));return a(n,t),n},dropInstance:function(t,e){if(e=l.apply(this,arguments),!(t="function"!=typeof t&&t||{}).name){var n=this.config();t.name=t.name||n.name,t.storeName=t.storeName||n.storeName}var i,r=this;return a(i=t.name?new s((function(e){t.storeName?e(q(t,r._defaultConfig)):e(t.name+"/")})).then((function(t){for(var e=localStorage.length-1;e>=0;e--){var n=localStorage.key(e);0===n.indexOf(t)&&localStorage.removeItem(n)}})):s.reject("Invalid arguments"),e),i}},W=function(t,e){for(var n,i,r=t.length,o=0;o<r;){if((n=t[o])===(i=e)||"number"==typeof n&&"number"==typeof i&&isNaN(n)&&isNaN(i))return!0;o++}return!1},H=Array.isArray||function(t){return"[object Array]"===Object.prototype.toString.call(t)},V={},X={},G={INDEXEDDB:T,WEBSQL:B,LOCALSTORAGE:F},Y=[G.INDEXEDDB._driver,G.WEBSQL._driver,G.LOCALSTORAGE._driver],$=["dropInstance"],K=["clear","getItem","iterate","key","keys","length","removeItem","setItem"].concat($),Z={description:"",driver:Y.slice(),name:"localforage",size:4980736,storeName:"keyvaluepairs",version:1};function J(t,e){t[e]=function(){var n=arguments;return t.ready().then((function(){return t[e].apply(t,n)}))}}function Q(){for(var t=1;t<arguments.length;t++){var e=arguments[t];if(e)for(var n in e)e.hasOwnProperty(n)&&(H(e[n])?arguments[0][n]=e[n].slice():arguments[0][n]=e[n])}return arguments[0]}var tt=new(function(){function t(e){for(var n in function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),G)if(G.hasOwnProperty(n)){var i=G[n],r=i._driver;this[n]=r,V[r]||this.defineDriver(i)}this._defaultConfig=Q({},Z),this._config=Q({},this._defaultConfig,e),this._driverSet=null,this._initDriver=null,this._ready=!1,this._dbInfo=null,this._wrapLibraryMethodsWithReady(),this.setDriver(this._config.driver).catch((function(){}))}return t.prototype.config=function(t){if("object"===(void 0===t?"undefined":i(t))){if(this._ready)return new Error("Can't call config() after localforage has been used.");for(var e in t){if("storeName"===e&&(t[e]=t[e].replace(/\W/g,"_")),"version"===e&&"number"!=typeof t[e])return new Error("Database version must be a number.");this._config[e]=t[e]}return!("driver"in t)||!t.driver||this.setDriver(this._config.driver)}return"string"==typeof t?this._config[t]:this._config},t.prototype.defineDriver=function(t,e,n){var i=new s((function(e,n){try{var i=t._driver,r=new Error("Custom driver not compliant; see https://mozilla.github.io/localForage/#definedriver");if(!t._driver)return void n(r);for(var o=K.concat("_initStorage"),u=0,c=o.length;u<c;u++){var l=o[u];if((!W($,l)||t[l])&&"function"!=typeof t[l])return void n(r)}!function(){for(var e=function(t){return function(){var e=new Error("Method "+t+" is not implemented by the current driver"),n=s.reject(e);return a(n,arguments[arguments.length-1]),n}},n=0,i=$.length;n<i;n++){var r=$[n];t[r]||(t[r]=e(r))}}();var h=function(n){V[i]&&console.info("Redefining LocalForage driver: "+i),V[i]=t,X[i]=n,e()};"_support"in t?t._support&&"function"==typeof t._support?t._support().then(h,n):h(!!t._support):h(!0)}catch(t){n(t)}}));return u(i,e,n),i},t.prototype.driver=function(){return this._driver||null},t.prototype.getDriver=function(t,e,n){var i=V[t]?s.resolve(V[t]):s.reject(new Error("Driver not found."));return u(i,e,n),i},t.prototype.getSerializer=function(t){var e=s.resolve(L);return u(e,t),e},t.prototype.ready=function(t){var e=this,n=e._driverSet.then((function(){return null===e._ready&&(e._ready=e._initDriver()),e._ready}));return u(n,t,t),n},t.prototype.setDriver=function(t,e,n){var i=this;H(t)||(t=[t]);var r=this._getSupportedDrivers(t);function o(){i._config.driver=i.driver()}function a(t){return i._extend(t),o(),i._ready=i._initStorage(i._config),i._ready}var c=null!==this._driverSet?this._driverSet.catch((function(){return s.resolve()})):s.resolve();return this._driverSet=c.then((function(){var t=r[0];return i._dbInfo=null,i._ready=null,i.getDriver(t).then((function(t){i._driver=t._driver,o(),i._wrapLibraryMethodsWithReady(),i._initDriver=function(t){return function(){var e=0;return function n(){for(;e<t.length;){var r=t[e];return e++,i._dbInfo=null,i._ready=null,i.getDriver(r).then(a).catch(n)}o();var u=new Error("No available storage method found.");return i._driverSet=s.reject(u),i._driverSet}()}}(r)}))})).catch((function(){o();var t=new Error("No available storage method found.");return i._driverSet=s.reject(t),i._driverSet})),u(this._driverSet,e,n),this._driverSet},t.prototype.supports=function(t){return!!X[t]},t.prototype._extend=function(t){Q(this,t)},t.prototype._getSupportedDrivers=function(t){for(var e=[],n=0,i=t.length;n<i;n++){var r=t[n];this.supports(r)&&e.push(r)}return e},t.prototype._wrapLibraryMethodsWithReady=function(){for(var t=0,e=K.length;t<e;t++)J(this,K[t])},t.prototype.createInstance=function(e){return new t(e)},t}());e.exports=tt},{3:3}]},{},[4])(4)}).call(this,n(73))},function(t,e,n){"use strict";n(170),n(10),n(14),n(27),n(18),n(22),n(34),n(15),n(62),n(63);var i=n(12),r=n.n(i),o=n(0),s=n(19),a=n(16),u=(n(11),n(37),n(95),n(2)),c=n(23),l=(n(161),n(35),n(28));n(53),n(44);var h=function(t,e,n,i){var r,s="undefined"!=typeof window&&window.URL,u=s?"blob":"arraybuffer",c=new o.defer,l=new XMLHttpRequest,h=XMLHttpRequest.prototype;for(r in"overrideMimeType"in h||Object.defineProperty(h,"overrideMimeType",{value:function(){}}),n&&(l.withCredentials=!0),l.onreadystatechange=function(){if(this.readyState===XMLHttpRequest.DONE){var t=!1;if(""!==this.responseType&&"document"!==this.responseType||(t=this.responseXML),200===this.status||0===this.status||t){var n;if(!this.response&&!t)return c.reject({status:this.status,message:"Empty Response",stack:(new Error).stack}),c.promise;if(403===this.status)return c.reject({status:this.status,response:this.response,message:"Forbidden",stack:(new Error).stack}),c.promise;n=t?this.responseXML:Object(o.isXml)(e)?Object(o.parse)(this.response,"text/xml"):"xhtml"==e?Object(o.parse)(this.response,"application/xhtml+xml"):"html"==e||"htm"==e?Object(o.parse)(this.response,"text/html"):"json"==e?JSON.parse(this.response):"blob"==e?s?this.response:new Blob([this.response]):this.response,c.resolve(n)}else c.reject({status:this.status,message:this.response,stack:(new Error).stack})}},l.onerror=function(t){c.reject(t)},l.open("GET",t,!0),i)l.setRequestHeader(r,i[r]);return"json"==e&&l.setRequestHeader("Accept","application/json"),e||(e=new a.a(t).extension),"blob"==e&&(l.responseType=u),Object(o.isXml)(e)&&l.overrideMimeType("text/xml"),"binary"==e&&(l.responseType="arraybuffer"),l.send(),c.promise},f=n(98);function d(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}var p=function(){function t(e,n){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.idref=e.idref,this.linear="yes"===e.linear,this.properties=e.properties,this.index=e.index,this.href=e.href,this.url=e.url,this.canonical=e.canonical,this.next=e.next,this.prev=e.prev,this.cfiBase=e.cfiBase,n?this.hooks=n:(this.hooks={},this.hooks.serialize=new c.a(this),this.hooks.content=new c.a(this)),this.document=void 0,this.contents=void 0,this.output=void 0}var e,n,i;return e=t,(n=[{key:"load",value:function(t){var e=t||this.request||h,n=new o.defer,i=n.promise;return this.contents?n.resolve(this.contents):e(this.url).then(function(t){return this.document=t,this.contents=t.documentElement,this.hooks.content.trigger(this.document,this)}.bind(this)).then(function(){n.resolve(this.contents)}.bind(this)).catch((function(t){n.reject(t)})),i}},{key:"base",value:function(){return Object(l.a)(this.document,this)}},{key:"render",value:function(t){var e=new o.defer,n=e.promise;return this.output,this.load(t).then(function(t){var e=("undefined"!=typeof navigator&&navigator.userAgent||"").indexOf("Trident")>=0,n=new("undefined"==typeof XMLSerializer||e?f.DOMParser:XMLSerializer);return this.output=n.serializeToString(t),this.output}.bind(this)).then(function(){return this.hooks.serialize.trigger(this.output,this)}.bind(this)).then(function(){e.resolve(this.output)}.bind(this)).catch((function(t){e.reject(t)})),n}},{key:"find",value:function(t){var e=this,n=[],i=t.toLowerCase(),r=function(t){for(var r,o,s,a=t.textContent.toLowerCase(),u=e.document.createRange(),c=-1;-1!=o;)-1!=(o=a.indexOf(i,c+1))&&((u=e.document.createRange()).setStart(t,o),u.setEnd(t,o+i.length),r=e.cfiFromRange(u),s=t.textContent.length<150?t.textContent:"..."+(s=t.textContent.substring(o-75,o+75))+"...",n.push({cfi:r,excerpt:s})),c=o};return Object(o.sprint)(e.document,(function(t){r(t)})),n}},{key:"search",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:5;if(void 0===document.createTreeWalker)return this.find(t);for(var n,i=[],r=150,o=this,s=t.toLowerCase(),a=function(t){var e=t.reduce((function(t,e){return t+e.textContent}),"").toLowerCase().indexOf(s);if(-1!=e){var n=e+s.length,a=0,u=0;if(e<t[0].length){for(var c;a<t.length-1&&!(n<=(u+=t[a].length));)a+=1;var l=t[0],h=t[a],f=o.document.createRange();f.setStart(l,e);var d=t.slice(0,a).reduce((function(t,e){return t+e.textContent.length}),0);f.setEnd(h,d>n?n:n-d),c=o.cfiFromRange(f);var p=t.slice(0,a+1).reduce((function(t,e){return t+e.textContent}),"");p.length>r&&(p="..."+(p=p.substring(e-r/2,e+r/2))+"..."),i.push({cfi:c,excerpt:p})}}},u=document.createTreeWalker(o.document,NodeFilter.SHOW_TEXT,null,!1),c=[];n=u.nextNode();)c.push(n),c.length==e&&(a(c.slice(0,e)),c=c.slice(1,e));return c.length>0&&a(c),i}},{key:"reconcileLayoutSettings",value:function(t){var e={layout:t.layout,spread:t.spread,orientation:t.orientation};return this.properties.forEach((function(t){var n,i,r=t.replace("rendition:",""),o=r.indexOf("-");-1!=o&&(n=r.slice(0,o),i=r.slice(o+1),e[n]=i)})),e}},{key:"cfiFromRange",value:function(t){return new u.a(t,this.cfiBase).toString()}},{key:"cfiFromElement",value:function(t){return new u.a(t,this.cfiBase).toString()}},{key:"unload",value:function(){this.document=void 0,this.contents=void 0,this.output=void 0}},{key:"destroy",value:function(){this.unload(),this.hooks.serialize.clear(),this.hooks.content.clear(),this.hooks=void 0,this.idref=void 0,this.linear=void 0,this.properties=void 0,this.index=void 0,this.href=void 0,this.url=void 0,this.next=void 0,this.prev=void 0,this.cfiBase=void 0}}])&&d(e.prototype,n),i&&d(e,i),t}();function v(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}var g=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.spineItems=[],this.spineByHref={},this.spineById={},this.hooks={},this.hooks.serialize=new c.a,this.hooks.content=new c.a,this.hooks.content.register(l.a),this.hooks.content.register(l.b),this.hooks.content.register(l.d),this.epubcfi=new u.a,this.loaded=!1,this.items=void 0,this.manifest=void 0,this.spineNodeIndex=void 0,this.baseUrl=void 0,this.length=void 0}var e,n,i;return e=t,(n=[{key:"unpack",value:function(t,e,n){var i=this;this.items=t.spine,this.manifest=t.manifest,this.spineNodeIndex=t.spineNodeIndex,this.baseUrl=t.baseUrl||t.basePath||"",this.length=this.items.length,this.items.forEach((function(t,r){var o,s=i.manifest[t.idref];t.index=r,t.cfiBase=i.epubcfi.generateChapterComponent(i.spineNodeIndex,t.index,t.id),t.href&&(t.url=e(t.href,!0),t.canonical=n(t.href)),s&&(t.href=s.href,t.url=e(t.href,!0),t.canonical=n(t.href),s.properties.length&&t.properties.push.apply(t.properties,s.properties)),"yes"===t.linear?(t.prev=function(){for(var e=t.index;e>0;){var n=this.get(e-1);if(n&&n.linear)return n;e-=1}}.bind(i),t.next=function(){for(var e=t.index;e<this.spineItems.length-1;){var n=this.get(e+1);if(n&&n.linear)return n;e+=1}}.bind(i)):(t.prev=function(){},t.next=function(){}),o=new p(t,i.hooks),i.append(o)})),this.loaded=!0}},{key:"get",value:function(t){var e=0;if(void 0===t)for(;e<this.spineItems.length;){var n=this.spineItems[e];if(n&&n.linear)break;e+=1}else this.epubcfi.isCfiString(t)?e=new u.a(t).spinePos:"number"==typeof t||!1===isNaN(t)?e=t:"string"==typeof t&&0===t.indexOf("#")?e=this.spineById[t.substring(1)]:"string"==typeof t&&(t=t.split("#")[0],e=this.spineByHref[t]||this.spineByHref[encodeURI(t)]);return this.spineItems[e]||null}},{key:"append",value:function(t){var e=this.spineItems.length;return t.index=e,this.spineItems.push(t),this.spineByHref[decodeURI(t.href)]=e,this.spineByHref[encodeURI(t.href)]=e,this.spineByHref[t.href]=e,this.spineById[t.idref]=e,e}},{key:"prepend",value:function(t){return this.spineByHref[t.href]=0,this.spineById[t.idref]=0,this.spineItems.forEach((function(t,e){t.index=e})),0}},{key:"remove",value:function(t){var e=this.spineItems.indexOf(t);if(e>-1)return delete this.spineByHref[t.href],delete this.spineById[t.idref],this.spineItems.splice(e,1)}},{key:"each",value:function(){return this.spineItems.forEach.apply(this.spineItems,arguments)}},{key:"first",value:function(){var t=0;do{var e=this.get(t);if(e&&e.linear)return e;t+=1}while(t<this.spineItems.length)}},{key:"last",value:function(){var t=this.spineItems.length-1;do{var e=this.get(t);if(e&&e.linear)return e;t-=1}while(t>=0)}},{key:"destroy",value:function(){this.each((function(t){return t.destroy()})),this.spineItems=void 0,this.spineByHref=void 0,this.spineById=void 0,this.hooks.serialize.clear(),this.hooks.content.clear(),this.hooks=void 0,this.epubcfi=void 0,this.loaded=!1,this.items=void 0,this.manifest=void 0,this.spineNodeIndex=void 0,this.baseUrl=void 0,this.length=void 0}}])&&v(e.prototype,n),i&&v(e,i),t}(),m=(n(97),n(54)),y=n(1);function b(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}var w=function(){function t(e,n,i){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.spine=e,this.request=n,this.pause=i||100,this.q=new m.a(this),this.epubcfi=new u.a,this._locations=[],this._locationsWords=[],this.total=0,this.break=150,this._current=0,this._wordCounter=0,this.currentLocation="",this._currentCfi="",this.processingTimeout=void 0}var e,n,i;return e=t,(n=[{key:"generate",value:function(t){return t&&(this.break=t),this.q.pause(),this.spine.each(function(t){t.linear&&this.q.enqueue(this.process.bind(this),t)}.bind(this)),this.q.run().then(function(){return this.total=this._locations.length-1,this._currentCfi&&(this.currentLocation=this._currentCfi),this._locations}.bind(this))}},{key:"createRange",value:function(){return{startContainer:void 0,startOffset:void 0,endContainer:void 0,endOffset:void 0}}},{key:"process",value:function(t){return t.load(this.request).then(function(e){var n=new o.defer,i=this.parse(e,t.cfiBase);return this._locations=this._locations.concat(i),t.unload(),this.processingTimeout=setTimeout((function(){return n.resolve(i)}),this.pause),n.promise}.bind(this))}},{key:"parse",value:function(t,e,n){var i,r,s=[],a=t.ownerDocument,c=Object(o.qs)(a,"body"),l=0,h=n||this.break;if(Object(o.sprint)(c,function(t){var n,o=t.length,a=0;if(0===t.textContent.trim().length)return!1;for(0==l&&((i=this.createRange()).startContainer=t,i.startOffset=0),(n=h-l)>o&&(l+=o,a=o);a<o;)if(n=h-l,0===l&&(a+=1,(i=this.createRange()).startContainer=t,i.startOffset=a),a+n>=o)l+=o-a,a=o;else{a+=n,i.endContainer=t,i.endOffset=a;var c=new u.a(i,e).toString();s.push(c),l=0}r=t}.bind(this)),i&&i.startContainer&&r){i.endContainer=r,i.endOffset=r.length;var f=new u.a(i,e).toString();s.push(f),l=0}return s}},{key:"generateFromWords",value:function(t,e,n){var i=t?new u.a(t):void 0;return this.q.pause(),this._locationsWords=[],this._wordCounter=0,this.spine.each(function(t){t.linear&&(i?t.index>=i.spinePos&&this.q.enqueue(this.processWords.bind(this),t,e,i,n):this.q.enqueue(this.processWords.bind(this),t,e,i,n))}.bind(this)),this.q.run().then(function(){return this._currentCfi&&(this.currentLocation=this._currentCfi),this._locationsWords}.bind(this))}},{key:"processWords",value:function(t,e,n,i){return i&&this._locationsWords.length>=i?Promise.resolve():t.load(this.request).then(function(r){var s=new o.defer,a=this.parseWords(r,t,e,n),u=i-this._locationsWords.length;return this._locationsWords=this._locationsWords.concat(a.length>=i?a.slice(0,u):a),t.unload(),this.processingTimeout=setTimeout((function(){return s.resolve(a)}),this.pause),s.promise}.bind(this))}},{key:"countWords",value:function(t){return(t=(t=(t=t.replace(/(^\s*)|(\s*$)/gi,"")).replace(/[ ]{2,}/gi," ")).replace(/\n /,"\n")).split(" ").length}},{key:"parseWords",value:function(t,e,n,i){var r,s=e.cfiBase,a=[],c=t.ownerDocument,l=Object(o.qs)(c,"body"),h=n,f=!i||i.spinePos!==e.index;return i&&e.index===i.spinePos&&(r=i.findNode(i.range?i.path.steps.concat(i.start.steps):i.path.steps,t.ownerDocument)),Object(o.sprint)(l,function(t){if(!f){if(t!==r)return!1;f=!0}if(t.textContent.length<10&&0===t.textContent.trim().length)return!1;var e,n=this.countWords(t.textContent),i=0;if(0===n)return!1;for((e=h-this._wordCounter)>n&&(this._wordCounter+=n,i=n);i<n;)if(i+(e=h-this._wordCounter)>=n)this._wordCounter+=n-i,i=n;else{i+=e;var o=new u.a(t,s);a.push({cfi:o.toString(),wordCount:this._wordCounter}),this._wordCounter=0}}.bind(this)),a}},{key:"locationFromCfi",value:function(t){var e;return u.a.prototype.isCfiString(t)&&(t=new u.a(t)),0===this._locations.length?-1:(e=Object(o.locationOf)(t,this._locations,this.epubcfi.compare))>this.total?this.total:e}},{key:"percentageFromCfi",value:function(t){if(0===this._locations.length)return null;var e=this.locationFromCfi(t);return this.percentageFromLocation(e)}},{key:"percentageFromLocation",value:function(t){return t&&this.total?t/this.total:0}},{key:"cfiFromLocation",value:function(t){var e=-1;return"number"!=typeof t&&(t=parseInt(t)),t>=0&&t<this._locations.length&&(e=this._locations[t]),e}},{key:"cfiFromPercentage",value:function(t){var e;if(t>1&&console.warn("Normalize cfiFromPercentage value to between 0 - 1"),t>=1){var n=new u.a(this._locations[this.total]);return n.collapse(),n.toString()}return e=Math.ceil(this.total*t),this.cfiFromLocation(e)}},{key:"load",value:function(t){return this._locations="string"==typeof t?JSON.parse(t):t,this.total=this._locations.length-1,this._locations}},{key:"save",value:function(){return JSON.stringify(this._locations)}},{key:"getCurrent",value:function(){return this._current}},{key:"setCurrent",value:function(t){var e;if("string"==typeof t)this._currentCfi=t;else{if("number"!=typeof t)return;this._current=t}0!==this._locations.length&&("string"==typeof t?(e=this.locationFromCfi(t),this._current=e):e=t,this.emit(y.c.LOCATIONS.CHANGED,{percentage:this.percentageFromLocation(e)}))}},{key:"currentLocation",get:function(){return this._current},set:function(t){this.setCurrent(t)}},{key:"length",value:function(){return this._locations.length}},{key:"destroy",value:function(){this.spine=void 0,this.request=void 0,this.pause=void 0,this.q.stop(),this.q=void 0,this.epubcfi=void 0,this._locations=void 0,this.total=void 0,this.break=void 0,this._current=void 0,this.currentLocation=void 0,this._currentCfi=void 0,clearTimeout(this.processingTimeout)}}])&&b(e.prototype,n),i&&b(e,i),t}();r()(w.prototype);var x=w,k=n(26),E=n.n(k);function S(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}var O=function(){function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.packagePath="",this.directory="",this.encoding="",e&&this.parse(e)}var e,n,i;return e=t,(n=[{key:"parse",value:function(t){var e;if(!t)throw new Error("Container File Not Found");if(!(e=Object(o.qs)(t,"rootfile")))throw new Error("No RootFile Found");this.packagePath=e.getAttribute("full-path"),this.directory=E.a.dirname(this.packagePath),this.encoding=t.xmlEncoding}},{key:"destroy",value:function(){this.packagePath=void 0,this.directory=void 0,this.encoding=void 0}}])&&S(e.prototype,n),i&&S(e,i),t}();n(38);function T(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}var _=function(){function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.manifest={},this.navPath="",this.ncxPath="",this.coverPath="",this.spineNodeIndex=0,this.spine=[],this.metadata={},e&&this.parse(e)}var e,n,i;return e=t,(n=[{key:"parse",value:function(t){var e,n,i;if(!t)throw new Error("Package File Not Found");if(!(e=Object(o.qs)(t,"metadata")))throw new Error("No Metadata Found");if(!(n=Object(o.qs)(t,"manifest")))throw new Error("No Manifest Found");if(!(i=Object(o.qs)(t,"spine")))throw new Error("No Spine Found");return this.manifest=this.parseManifest(n),this.navPath=this.findNavPath(n),this.ncxPath=this.findNcxPath(n,i),this.coverPath=this.findCoverPath(t),this.spineNodeIndex=Object(o.indexOfElementNode)(i),this.spine=this.parseSpine(i,this.manifest),this.uniqueIdentifier=this.findUniqueIdentifier(t),this.metadata=this.parseMetadata(e),this.metadata.direction=i.getAttribute("page-progression-direction"),{metadata:this.metadata,spine:this.spine,manifest:this.manifest,navPath:this.navPath,ncxPath:this.ncxPath,coverPath:this.coverPath,spineNodeIndex:this.spineNodeIndex}}},{key:"parseMetadata",value:function(t){var e={};return e.title=this.getElementText(t,"title"),e.creator=this.getElementText(t,"creator"),e.description=this.getElementText(t,"description"),e.pubdate=this.getElementText(t,"date"),e.publisher=this.getElementText(t,"publisher"),e.identifier=this.getElementText(t,"identifier"),e.language=this.getElementText(t,"language"),e.rights=this.getElementText(t,"rights"),e.modified_date=this.getPropertyText(t,"dcterms:modified"),e.layout=this.getPropertyText(t,"rendition:layout"),e.orientation=this.getPropertyText(t,"rendition:orientation"),e.flow=this.getPropertyText(t,"rendition:flow"),e.viewport=this.getPropertyText(t,"rendition:viewport"),e.media_active_class=this.getPropertyText(t,"media:active-class"),e.spread=this.getPropertyText(t,"rendition:spread"),e}},{key:"parseManifest",value:function(t){var e={},n=Object(o.qsa)(t,"item");return Array.prototype.slice.call(n).forEach((function(t){var n=t.getAttribute("id"),i=t.getAttribute("href")||"",r=t.getAttribute("media-type")||"",o=t.getAttribute("media-overlay")||"",s=t.getAttribute("properties")||"";e[n]={href:i,type:r,overlay:o,properties:s.length?s.split(" "):[]}})),e}},{key:"parseSpine",value:function(t,e){var n=[],i=Object(o.qsa)(t,"itemref");return Array.prototype.slice.call(i).forEach((function(t,e){var i=t.getAttribute("idref"),r=t.getAttribute("properties")||"",o=r.length?r.split(" "):[],s={id:t.getAttribute("id"),idref:i,linear:t.getAttribute("linear")||"yes",properties:o,index:e};n.push(s)})),n}},{key:"findUniqueIdentifier",value:function(t){var e=t.documentElement.getAttribute("unique-identifier");if(!e)return"";var n=t.getElementById(e);return n&&"identifier"===n.localName&&"http://purl.org/dc/elements/1.1/"===n.namespaceURI&&n.childNodes.length>0?n.childNodes[0].nodeValue.trim():""}},{key:"findNavPath",value:function(t){var e=Object(o.qsp)(t,"item",{properties:"nav"});return!!e&&e.getAttribute("href")}},{key:"findNcxPath",value:function(t,e){var n,i=Object(o.qsp)(t,"item",{"media-type":"application/x-dtbncx+xml"});return i||(n=e.getAttribute("toc"))&&(i=t.querySelector("#".concat(n))),!!i&&i.getAttribute("href")}},{key:"findCoverPath",value:function(t){Object(o.qs)(t,"package").getAttribute("version");var e=Object(o.qsp)(t,"item",{properties:"cover-image"});if(e)return e.getAttribute("href");var n=Object(o.qsp)(t,"meta",{name:"cover"});if(n){var i=n.getAttribute("content"),r=t.getElementById(i);return r?r.getAttribute("href"):""}return!1}},{key:"getElementText",value:function(t,e){var n,i=t.getElementsByTagNameNS("http://purl.org/dc/elements/1.1/",e);return i&&0!==i.length&&(n=i[0]).childNodes.length?n.childNodes[0].nodeValue:""}},{key:"getPropertyText",value:function(t,e){var n=Object(o.qsp)(t,"meta",{property:e});return n&&n.childNodes.length?n.childNodes[0].nodeValue:""}},{key:"load",value:function(t){var e=this;this.metadata=t.metadata;var n=t.readingOrder||t.spine;return this.spine=n.map((function(t,e){return t.index=e,t.linear=t.linear||"yes",t})),t.resources.forEach((function(t,n){e.manifest[n]=t,t.rel&&"cover"===t.rel[0]&&(e.coverPath=t.href)})),this.spineNodeIndex=0,this.toc=t.toc.map((function(t,e){return t.label=t.title,t})),{metadata:this.metadata,spine:this.spine,manifest:this.manifest,navPath:this.navPath,ncxPath:this.ncxPath,coverPath:this.coverPath,spineNodeIndex:this.spineNodeIndex,toc:this.toc}}},{key:"destroy",value:function(){this.manifest=void 0,this.navPath=void 0,this.ncxPath=void 0,this.coverPath=void 0,this.spineNodeIndex=void 0,this.spine=void 0,this.metadata=void 0}}])&&T(e.prototype,n),i&&T(e,i),t}();function N(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}var C=function(){function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.toc=[],this.tocByHref={},this.tocById={},this.landmarks=[],this.landmarksByType={},this.length=0,e&&this.parse(e)}var e,n,i;return e=t,(n=[{key:"parse",value:function(t){var e,n,i=t.nodeType;i&&(e=Object(o.qs)(t,"html"),n=Object(o.qs)(t,"ncx")),i?e?(this.toc=this.parseNav(t),this.landmarks=this.parseLandmarks(t)):n&&(this.toc=this.parseNcx(t)):this.toc=this.load(t),this.length=0,this.unpack(this.toc)}},{key:"unpack",value:function(t){for(var e,n=0;n<t.length;n++)(e=t[n]).href&&(this.tocByHref[e.href]=n),e.id&&(this.tocById[e.id]=n),this.length++,e.subitems.length&&this.unpack(e.subitems)}},{key:"get",value:function(t){var e;return t?(0===t.indexOf("#")?e=this.tocById[t.substring(1)]:t in this.tocByHref&&(e=this.tocByHref[t]),this.getByIndex(t,e,this.toc)):this.toc}},{key:"getByIndex",value:function(t,e,n){if(0!==n.length){var i=n[e];if(!i||t!==i.id&&t!==i.href){for(var r,o=0;o<n.length&&!(r=this.getByIndex(t,e,n[o].subitems));++o);return r}return i}}},{key:"landmark",value:function(t){var e;return t?(e=this.landmarksByType[t],this.landmarks[e]):this.landmarks}},{key:"parseNav",value:function(t){var e=Object(o.querySelectorByType)(t,"nav","toc"),n=[];if(!e)return n;var i=Object(o.filterChildren)(e,"ol",!0);return i?n=this.parseNavList(i):n}},{key:"parseNavList",value:function(t,e){var n=[];if(!t)return n;if(!t.children)return n;for(var i=0;i<t.children.length;i++){var r=this.navItem(t.children[i],e);r&&n.push(r)}return n}},{key:"navItem",value:function(t,e){var n=t.getAttribute("id")||void 0,i=Object(o.filterChildren)(t,"a",!0)||Object(o.filterChildren)(t,"span",!0);if(i){var r=i.getAttribute("href")||"";n||(n=r);var s=i.textContent||"",a=[],u=Object(o.filterChildren)(t,"ol",!0);return u&&(a=this.parseNavList(u,n)),{id:n,href:r,label:s,subitems:a,parent:e}}}},{key:"parseLandmarks",value:function(t){var e,n,i=Object(o.querySelectorByType)(t,"nav","landmarks"),r=i?Object(o.qsa)(i,"li"):[],s=r.length,a=[];if(!r||0===s)return a;for(e=0;e<s;++e)(n=this.landmarkItem(r[e]))&&(a.push(n),this.landmarksByType[n.type]=e);return a}},{key:"landmarkItem",value:function(t){var e=Object(o.filterChildren)(t,"a",!0);if(e){var n=e.getAttributeNS("http://www.idpf.org/2007/ops","type")||void 0;return{href:e.getAttribute("href")||"",label:e.textContent||"",type:n}}}},{key:"parseNcx",value:function(t){var e,n,i=Object(o.qsa)(t,"navPoint"),r=i.length,s={},a=[];if(!i||0===r)return a;for(e=0;e<r;++e)s[(n=this.ncxItem(i[e])).id]=n,n.parent?s[n.parent].subitems.push(n):a.push(n);return a}},{key:"ncxItem",value:function(t){var e,n=t.getAttribute("id")||!1,i=Object(o.qs)(t,"content").getAttribute("src"),r=Object(o.qs)(t,"navLabel"),s=r.textContent?r.textContent:"",a=t.parentNode;return!a||"navPoint"!==a.nodeName&&"navPoint"!==a.nodeName.split(":").slice(-1)[0]||(e=a.getAttribute("id")),{id:n,href:i,label:s,subitems:[],parent:e}}},{key:"load",value:function(t){var e=this;return t.map((function(t){return t.label=t.title,t.subitems=t.children?e.load(t.children):[],t}))}},{key:"forEach",value:function(t){return this.toc.forEach(t)}}])&&N(e.prototype,n),i&&N(e,i),t}(),R=(n(70),n(66),{application:{ecmascript:["es","ecma"],javascript:"js",ogg:"ogx",pdf:"pdf",postscript:["ps","ai","eps","epsi","epsf","eps2","eps3"],"rdf+xml":"rdf",smil:["smi","smil"],"xhtml+xml":["xhtml","xht"],xml:["xml","xsl","xsd","opf","ncx"],zip:"zip","x-httpd-eruby":"rhtml","x-latex":"latex","x-maker":["frm","maker","frame","fm","fb","book","fbdoc"],"x-object":"o","x-shockwave-flash":["swf","swfl"],"x-silverlight":"scr","epub+zip":"epub","font-tdpfr":"pfr","inkml+xml":["ink","inkml"],json:"json","jsonml+json":"jsonml","mathml+xml":"mathml","metalink+xml":"metalink",mp4:"mp4s","omdoc+xml":"omdoc",oxps:"oxps","vnd.amazon.ebook":"azw",widget:"wgt","x-dtbook+xml":"dtb","x-dtbresource+xml":"res","x-font-bdf":"bdf","x-font-ghostscript":"gsf","x-font-linux-psf":"psf","x-font-otf":"otf","x-font-pcf":"pcf","x-font-snf":"snf","x-font-ttf":["ttf","ttc"],"x-font-type1":["pfa","pfb","pfm","afm"],"x-font-woff":"woff","x-mobipocket-ebook":["prc","mobi"],"x-mspublisher":"pub","x-nzb":"nzb","x-tgif":"obj","xaml+xml":"xaml","xml-dtd":"dtd","xproc+xml":"xpl","xslt+xml":"xslt","internet-property-stream":"acx","x-compress":"z","x-compressed":"tgz","x-gzip":"gz"},audio:{flac:"flac",midi:["mid","midi","kar","rmi"],mpeg:["mpga","mpega","mp2","mp3","m4a","mp2a","m2a","m3a"],mpegurl:"m3u",ogg:["oga","ogg","spx"],"x-aiff":["aif","aiff","aifc"],"x-ms-wma":"wma","x-wav":"wav",adpcm:"adp",mp4:"mp4a",webm:"weba","x-aac":"aac","x-caf":"caf","x-matroska":"mka","x-pn-realaudio-plugin":"rmp",xm:"xm",mid:["mid","rmi"]},image:{gif:"gif",ief:"ief",jpeg:["jpeg","jpg","jpe"],pcx:"pcx",png:"png","svg+xml":["svg","svgz"],tiff:["tiff","tif"],"x-icon":"ico",bmp:"bmp",webp:"webp","x-pict":["pic","pct"],"x-tga":"tga","cis-cod":"cod"},text:{"cache-manifest":["manifest","appcache"],css:"css",csv:"csv",html:["html","htm","shtml","stm"],mathml:"mml",plain:["txt","text","brf","conf","def","list","log","in","bas"],richtext:"rtx","tab-separated-values":"tsv","x-bibtex":"bib"},video:{mpeg:["mpeg","mpg","mpe","m1v","m2v","mp2","mpa","mpv2"],mp4:["mp4","mp4v","mpg4"],quicktime:["qt","mov"],ogg:"ogv","vnd.mpegurl":["mxu","m4u"],"x-flv":"flv","x-la-asf":["lsf","lsx"],"x-mng":"mng","x-ms-asf":["asf","asx","asr"],"x-ms-wm":"wm","x-ms-wmv":"wmv","x-ms-wmx":"wmx","x-ms-wvx":"wvx","x-msvideo":"avi","x-sgi-movie":"movie","x-matroska":["mpv","mkv","mk3d","mks"],"3gpp2":"3g2",h261:"h261",h263:"h263",h264:"h264",jpeg:"jpgv",jpm:["jpm","jpgm"],mj2:["mj2","mjp2"],"vnd.ms-playready.media.pyv":"pyv","vnd.uvvu.mp4":["uvu","uvvu"],"vnd.vivo":"viv",webm:"webm","x-f4v":"f4v","x-m4v":"m4v","x-ms-vob":"vob","x-smv":"smv"}}),I=function(){var t,e,n,i,r={};for(t in R)if(R.hasOwnProperty(t))for(e in R[t])if(R[t].hasOwnProperty(e))if("string"==typeof(n=R[t][e]))r[n]=t+"/"+e;else for(i=0;i<n.length;i++)r[n[i]]=t+"/"+e;return r}();var A={lookup:function(t){return t&&I[t.split(".").pop().toLowerCase()]||"text/plain"}};function j(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}var L=function(){function t(e,n){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.settings={replacements:n&&n.replacements||"base64",archive:n&&n.archive,resolver:n&&n.resolver,request:n&&n.request},this.process(e)}var e,n,i;return e=t,(n=[{key:"process",value:function(t){this.manifest=t,this.resources=Object.keys(t).map((function(e){return t[e]})),this.replacementUrls=[],this.html=[],this.assets=[],this.css=[],this.urls=[],this.cssUrls=[],this.split(),this.splitUrls()}},{key:"split",value:function(){this.html=this.resources.filter((function(t){if("application/xhtml+xml"===t.type||"text/html"===t.type)return!0})),this.assets=this.resources.filter((function(t){if("application/xhtml+xml"!==t.type&&"text/html"!==t.type)return!0})),this.css=this.resources.filter((function(t){if("text/css"===t.type)return!0}))}},{key:"splitUrls",value:function(){this.urls=this.assets.map(function(t){return t.href}.bind(this)),this.cssUrls=this.css.map((function(t){return t.href}))}},{key:"createUrl",value:function(t){var e=new s.a(t),n=A.lookup(e.filename);return this.settings.archive?this.settings.archive.createUrl(t,{base64:"base64"===this.settings.replacements}):"base64"===this.settings.replacements?this.settings.request(t,"blob").then((function(t){return Object(o.blob2base64)(t)})).then((function(t){return Object(o.createBase64Url)(t,n)})):this.settings.request(t,"blob").then((function(t){return Object(o.createBlobUrl)(t,n)}))}},{key:"replacements",value:function(){var t=this;if("none"===this.settings.replacements)return new Promise(function(t){t(this.urls)}.bind(this));var e=this.urls.map((function(e){var n=t.settings.resolver(e);return t.createUrl(n).catch((function(t){return console.error(t),null}))}));return Promise.all(e).then((function(e){return t.replacementUrls=e.filter((function(t){return"string"==typeof t})),e}))}},{key:"replaceCss",value:function(t,e){var n=[];return t=t||this.settings.archive,e=e||this.settings.resolver,this.cssUrls.forEach(function(i){var r=this.createCssFile(i,t,e).then(function(t){var e=this.urls.indexOf(i);e>-1&&(this.replacementUrls[e]=t)}.bind(this));n.push(r)}.bind(this)),Promise.all(n)}},{key:"createCssFile",value:function(t){var e=this;if(E.a.isAbsolute(t))return new Promise((function(t){t()}));var n,i=this.settings.resolver(t);n=this.settings.archive?this.settings.archive.getText(i):this.settings.request(i,"text");var r=this.urls.map((function(t){var n=e.settings.resolver(t);return new a.a(i).relative(n)}));return n?n.then((function(t){return t=Object(l.e)(t,r,e.replacementUrls),"base64"===e.settings.replacements?Object(o.createBase64Url)(t,"text/css"):Object(o.createBlobUrl)(t,"text/css")}),(function(t){return new Promise((function(t){t()}))})):new Promise((function(t){t()}))}},{key:"relativeTo",value:function(t,e){return e=e||this.settings.resolver,this.urls.map(function(n){var i=e(n);return new a.a(t).relative(i)}.bind(this))}},{key:"get",value:function(t){var e=this.urls.indexOf(t);if(-1!==e)return this.replacementUrls.length?new Promise(function(t,n){t(this.replacementUrls[e])}.bind(this)):this.createUrl(t)}},{key:"substitute",value:function(t,e){var n;return n=e?this.relativeTo(e):this.urls,Object(l.e)(t,n,this.replacementUrls)}},{key:"destroy",value:function(){this.settings=void 0,this.manifest=void 0,this.resources=void 0,this.replacementUrls=void 0,this.html=void 0,this.assets=void 0,this.css=void 0,this.urls=void 0,this.cssUrls=void 0}}])&&j(e.prototype,n),i&&j(e,i),t}();function P(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}var D=function(){function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.pages=[],this.locations=[],this.epubcfi=new u.a,this.firstPage=0,this.lastPage=0,this.totalPages=0,this.toc=void 0,this.ncx=void 0,e&&(this.pageList=this.parse(e)),this.pageList&&this.pageList.length&&this.process(this.pageList)}var e,n,i;return e=t,(n=[{key:"parse",value:function(t){var e=Object(o.qs)(t,"html"),n=Object(o.qs)(t,"ncx");return e?this.parseNav(t):n?this.parseNcx(t):void 0}},{key:"parseNav",value:function(t){var e,n,i=Object(o.querySelectorByType)(t,"nav","page-list"),r=i?Object(o.qsa)(i,"li"):[],s=r.length,a=[];if(!r||0===s)return a;for(e=0;e<s;++e)n=this.item(r[e]),a.push(n);return a}},{key:"parseNcx",value:function(t){var e,n,i,r,s=[],a=0;if(!(n=Object(o.qs)(t,"pageList")))return s;if(r=(i=Object(o.qsa)(n,"pageTarget")).length,!i||0===i.length)return s;for(a=0;a<r;++a)e=this.ncxItem(i[a]),s.push(e);return s}},{key:"ncxItem",value:function(t){var e=Object(o.qs)(t,"navLabel"),n=Object(o.qs)(e,"text").textContent;return{href:Object(o.qs)(t,"content").getAttribute("src"),page:parseInt(n,10)}}},{key:"item",value:function(t){var e,n,i=Object(o.qs)(t,"a"),r=i.getAttribute("href")||"",s=i.textContent||"",a=parseInt(s);return-1!=r.indexOf("epubcfi")?(n=(e=r.split("#"))[0],{cfi:e.length>1&&e[1],href:r,packageUrl:n,page:a}):{href:r,page:a}}},{key:"process",value:function(t){t.forEach((function(t){this.pages.push(t.page),t.cfi&&this.locations.push(t.cfi)}),this),this.firstPage=parseInt(this.pages[0]),this.lastPage=parseInt(this.pages[this.pages.length-1]),this.totalPages=this.lastPage-this.firstPage}},{key:"pageFromCfi",value:function(t){var e=-1;if(0===this.locations.length)return-1;var n=Object(o.indexOfSorted)(t,this.locations,this.epubcfi.compare);return-1!=n?e=this.pages[n]:void 0!==(e=(n=Object(o.locationOf)(t,this.locations,this.epubcfi.compare))-1>=0?this.pages[n-1]:this.pages[0])||(e=-1),e}},{key:"cfiFromPage",value:function(t){var e=-1;"number"!=typeof t&&(t=parseInt(t));var n=this.pages.indexOf(t);return-1!=n&&(e=this.locations[n]),e}},{key:"pageFromPercentage",value:function(t){return Math.round(this.totalPages*t)}},{key:"percentageFromPage",value:function(t){var e=(t-this.firstPage)/this.totalPages;return Math.round(1e3*e)/1e3}},{key:"percentageFromCfi",value:function(t){var e=this.pageFromCfi(t);return this.percentageFromPage(e)}},{key:"destroy",value:function(){this.pages=void 0,this.locations=void 0,this.epubcfi=void 0,this.pageList=void 0,this.toc=void 0,this.ncx=void 0}}])&&P(e.prototype,n),i&&P(e,i),t}(),M=n(99),z=(n(39),n(168)),B=n.n(z);function q(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}var U=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.zip=void 0,this.urlCache={},this.checkRequirements()}var e,n,i;return e=t,(n=[{key:"checkRequirements",value:function(){try{this.zip=new B.a}catch(t){throw new Error("JSZip lib not loaded")}}},{key:"open",value:function(t,e){return this.zip.loadAsync(t,{base64:e})}},{key:"openUrl",value:function(t,e){return h(t,"binary").then(function(t){return this.zip.loadAsync(t,{base64:e})}.bind(this))}},{key:"request",value:function(t,e){var n,i=new o.defer,r=new a.a(t);return e||(e=r.extension),(n="blob"==e?this.getBlob(t):this.getText(t))?n.then(function(t){var n=this.handleResponse(t,e);i.resolve(n)}.bind(this)):i.reject({message:"File not found in the epub: "+t,stack:(new Error).stack}),i.promise}},{key:"handleResponse",value:function(t,e){return"json"==e?JSON.parse(t):Object(o.isXml)(e)?Object(o.parse)(t,"text/xml"):"xhtml"==e?Object(o.parse)(t,"application/xhtml+xml"):"html"==e||"htm"==e?Object(o.parse)(t,"text/html"):t}},{key:"getBlob",value:function(t,e){var n=window.decodeURIComponent(t.substr(1)),i=this.zip.file(n);if(i)return e=e||A.lookup(i.name),i.async("uint8array").then((function(t){return new Blob([t],{type:e})}))}},{key:"getText",value:function(t,e){var n=window.decodeURIComponent(t.substr(1)),i=this.zip.file(n);if(i)return i.async("string").then((function(t){return t}))}},{key:"getBase64",value:function(t,e){var n=window.decodeURIComponent(t.substr(1)),i=this.zip.file(n);if(i)return e=e||A.lookup(i.name),i.async("base64").then((function(t){return"data:"+e+";base64,"+t}))}},{key:"createUrl",value:function(t,e){var n,i,r=new o.defer,s=window.URL||window.webkitURL||window.mozURL,a=e&&e.base64;return t in this.urlCache?(r.resolve(this.urlCache[t]),r.promise):(a?(i=this.getBase64(t))&&i.then(function(e){this.urlCache[t]=e,r.resolve(e)}.bind(this)):(i=this.getBlob(t))&&i.then(function(e){n=s.createObjectURL(e),this.urlCache[t]=n,r.resolve(n)}.bind(this)),i||r.reject({message:"File not found in the epub: "+t,stack:(new Error).stack}),r.promise)}},{key:"revokeUrl",value:function(t){var e=window.URL||window.webkitURL||window.mozURL,n=this.urlCache[t];n&&e.revokeObjectURL(n)}},{key:"destroy",value:function(){var t=window.URL||window.webkitURL||window.mozURL;for(var e in this.urlCache)t.revokeObjectURL(e);this.zip=void 0,this.urlCache={}}}])&&q(e.prototype,n),i&&q(e,i),t}(),F=n(125),W=n.n(F);function H(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}var V=function(){function t(e,n,i){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.urlCache={},this.storage=void 0,this.name=e,this.requester=n||h,this.resolver=i,this.online=!0,this.checkRequirements(),this.addListeners()}var e,n,i;return e=t,(n=[{key:"checkRequirements",value:function(){try{var t;void 0===W.a&&(t=W.a),this.storage=t.createInstance({name:this.name})}catch(t){throw new Error("localForage lib not loaded")}}},{key:"addListeners",value:function(){this._status=this.status.bind(this),window.addEventListener("online",this._status),window.addEventListener("offline",this._status)}},{key:"removeListeners",value:function(){window.removeEventListener("online",this._status),window.removeEventListener("offline",this._status),this._status=void 0}},{key:"status",value:function(t){var e=navigator.onLine;this.online=e,e?this.emit("online",this):this.emit("offline",this)}},{key:"add",value:function(t,e){var n=this,i=t.resources.map((function(t){var i=t.href,r=n.resolver(i),o=window.encodeURIComponent(r);return n.storage.getItem(o).then((function(t){return!t||e?n.requester(r,"binary").then((function(t){return n.storage.setItem(o,t)})):t}))}));return Promise.all(i)}},{key:"put",value:function(t,e,n){var i=this,r=window.encodeURIComponent(t);return this.storage.getItem(r).then((function(o){return o||i.requester(t,"binary",e,n).then((function(t){return i.storage.setItem(r,t)}))}))}},{key:"request",value:function(t,e,n,i){var r=this;return this.online?this.requester(t,e,n,i).then((function(e){return r.put(t),e})):this.retrieve(t,e)}},{key:"retrieve",value:function(t,e){var n=this,i=(new o.defer,new a.a(t));return e||(e=i.extension),("blob"==e?this.getBlob(t):this.getText(t)).then((function(i){var r,s=new o.defer;return i?(r=n.handleResponse(i,e),s.resolve(r)):s.reject({message:"File not found in storage: "+t,stack:(new Error).stack}),s.promise}))}},{key:"handleResponse",value:function(t,e){return"json"==e?JSON.parse(t):Object(o.isXml)(e)?Object(o.parse)(t,"text/xml"):"xhtml"==e?Object(o.parse)(t,"application/xhtml+xml"):"html"==e||"htm"==e?Object(o.parse)(t,"text/html"):t}},{key:"getBlob",value:function(t,e){var n=window.encodeURIComponent(t);return this.storage.getItem(n).then((function(n){if(n)return e=e||A.lookup(t),new Blob([n],{type:e})}))}},{key:"getText",value:function(t,e){var n=window.encodeURIComponent(t);return e=e||A.lookup(t),this.storage.getItem(n).then((function(t){var n,i=new o.defer,r=new FileReader;if(t)return n=new Blob([t],{type:e}),r.addEventListener("loadend",(function(){i.resolve(r.result)})),r.readAsText(n,e),i.promise}))}},{key:"getBase64",value:function(t,e){var n=window.encodeURIComponent(t);return e=e||A.lookup(t),this.storage.getItem(n).then((function(t){var n,i=new o.defer,r=new FileReader;if(t)return n=new Blob([t],{type:e}),r.addEventListener("loadend",(function(){i.resolve(r.result)})),r.readAsDataURL(n,e),i.promise}))}},{key:"createUrl",value:function(t,e){var n,i,r=new o.defer,s=window.URL||window.webkitURL||window.mozURL,a=e&&e.base64;return t in this.urlCache?(r.resolve(this.urlCache[t]),r.promise):(a?(i=this.getBase64(t))&&i.then(function(e){this.urlCache[t]=e,r.resolve(e)}.bind(this)):(i=this.getBlob(t))&&i.then(function(e){n=s.createObjectURL(e),this.urlCache[t]=n,r.resolve(n)}.bind(this)),i||r.reject({message:"File not found in storage: "+t,stack:(new Error).stack}),r.promise)}},{key:"revokeUrl",value:function(t){var e=window.URL||window.webkitURL||window.mozURL,n=this.urlCache[t];n&&e.revokeObjectURL(n)}},{key:"destroy",value:function(){var t=window.URL||window.webkitURL||window.mozURL;for(var e in this.urlCache)t.revokeObjectURL(e);this.urlCache={},this.removeListeners()}}])&&H(e.prototype,n),i&&H(e,i),t}();r()(V.prototype);var X=V;function G(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}var Y=function(){function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.interactive="",this.fixedLayout="",this.openToSpread="",this.orientationLock="",e&&this.parse(e)}var e,n,i;return e=t,(n=[{key:"parse",value:function(t){var e=this;if(!t)return this;var n=Object(o.qs)(t,"display_options");return n?(Object(o.qsa)(n,"option").forEach((function(t){var n="";switch(t.childNodes.length&&(n=t.childNodes[0].nodeValue),t.attributes.name.value){case"interactive":e.interactive=n;break;case"fixed-layout":e.fixedLayout=n;break;case"open-to-spread":e.openToSpread=n;break;case"orientation-lock":e.orientationLock=n}})),this):this}},{key:"destroy",value:function(){this.interactive=void 0,this.fixedLayout=void 0,this.openToSpread=void 0,this.orientationLock=void 0}}])&&G(e.prototype,n),i&&G(e,i),t}();function $(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}var K="binary",Z="base64",J="epub",Q="opf",tt="json",et="directory",nt=function(){function t(e,n){var i=this;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),void 0===n&&"string"!=typeof e&&e instanceof Blob==!1&&e instanceof ArrayBuffer==!1&&(n=e,e=void 0),this.settings=Object(o.extend)(this.settings||{},{requestMethod:void 0,requestCredentials:void 0,requestHeaders:void 0,encoding:void 0,replacements:void 0,canonical:void 0,openAs:void 0,store:void 0}),Object(o.extend)(this.settings,n),this.opening=new o.defer,this.opened=this.opening.promise,this.isOpen=!1,this.loading={manifest:new o.defer,spine:new o.defer,metadata:new o.defer,cover:new o.defer,navigation:new o.defer,pageList:new o.defer,resources:new o.defer,displayOptions:new o.defer},this.loaded={manifest:this.loading.manifest.promise,spine:this.loading.spine.promise,metadata:this.loading.metadata.promise,cover:this.loading.cover.promise,navigation:this.loading.navigation.promise,pageList:this.loading.pageList.promise,resources:this.loading.resources.promise,displayOptions:this.loading.displayOptions.promise},this.ready=Promise.all([this.loaded.manifest,this.loaded.spine,this.loaded.metadata,this.loaded.cover,this.loaded.navigation,this.loaded.resources,this.loaded.displayOptions]),this.isRendered=!1,this.request=this.settings.requestMethod||h,this.spine=new g,this.locations=new x(this.spine,this.load.bind(this)),this.navigation=void 0,this.pageList=void 0,this.url=void 0,this.path=void 0,this.archived=!1,this.archive=void 0,this.storage=void 0,this.resources=void 0,this.rendition=void 0,this.container=void 0,this.packaging=void 0,this.displayOptions=void 0,this.settings.store&&this.store(this.settings.store),e&&this.open(e,this.settings.openAs).catch((function(t){var n=new Error("Cannot load book at "+e);i.emit(y.c.BOOK.OPEN_FAILED,n)}))}var e,n,i;return e=t,(n=[{key:"open",value:function(t,e){var n,i=e||this.determineType(t);return i===K?(this.archived=!0,this.url=new s.a("/",""),n=this.openEpub(t)):i===Z?(this.archived=!0,this.url=new s.a("/",""),n=this.openEpub(t,i)):i===J?(this.archived=!0,this.url=new s.a("/",""),n=this.request(t,"binary",this.settings.requestCredentials,this.settings.requestHeaders).then(this.openEpub.bind(this))):i==Q?(this.url=new s.a(t),n=this.openPackaging(this.url.Path.toString())):i==tt?(this.url=new s.a(t),n=this.openManifest(this.url.Path.toString())):(this.url=new s.a(t),n=this.openContainer("META-INF/container.xml").then(this.openPackaging.bind(this))),n}},{key:"openEpub",value:function(t,e){var n=this;return this.unarchive(t,e||this.settings.encoding).then((function(){return n.openContainer("META-INF/container.xml")})).then((function(t){return n.openPackaging(t)}))}},{key:"openContainer",value:function(t){var e=this;return this.load(t).then((function(t){return e.container=new O(t),e.resolve(e.container.packagePath)}))}},{key:"openPackaging",value:function(t){var e=this;return this.path=new a.a(t),this.load(t).then((function(t){return e.packaging=new _(t),e.unpack(e.packaging)}))}},{key:"openManifest",value:function(t){var e=this;return this.path=new a.a(t),this.load(t).then((function(t){return e.packaging=new _,e.packaging.load(t),e.unpack(e.packaging)}))}},{key:"load",value:function(t){var e=this.resolve(t);return this.archived?this.archive.request(e):this.request(e,null,this.settings.requestCredentials,this.settings.requestHeaders)}},{key:"resolve",value:function(t,e){if(t){var n=t;return t.indexOf("://")>-1?t:(this.path&&(n=this.path.resolve(t)),0!=e&&this.url&&(n=this.url.resolve(n)),n)}}},{key:"canonical",value:function(t){return t?this.settings.canonical?this.settings.canonical(t):this.resolve(t,!0):""}},{key:"determineType",value:function(t){var e;return"base64"===this.settings.encoding?Z:"string"!=typeof t?K:((e=new s.a(t).path().extension)&&(e=e.replace(/\?.*$/,"")),e?"epub"===e?J:"opf"===e?Q:"json"===e?tt:void 0:et)}},{key:"unpack",value:function(t){var e=this;this.package=t,""===this.packaging.metadata.layout?this.load(this.url.resolve("META-INF/com.apple.ibooks.display-options.xml")).then((function(t){e.displayOptions=new Y(t),e.loading.displayOptions.resolve(e.displayOptions)})).catch((function(t){e.displayOptions=new Y,e.loading.displayOptions.resolve(e.displayOptions)})):(this.displayOptions=new Y,this.loading.displayOptions.resolve(this.displayOptions)),this.spine.unpack(this.packaging,this.resolve.bind(this),this.canonical.bind(this)),this.resources=new L(this.packaging.manifest,{archive:this.archive,resolver:this.resolve.bind(this),request:this.request.bind(this),replacements:this.settings.replacements||(this.archived?"blobUrl":"base64")}),this.loadNavigation(this.packaging).then((function(){e.loading.navigation.resolve(e.navigation)})),this.packaging.coverPath&&(this.cover=this.resolve(this.packaging.coverPath)),this.loading.manifest.resolve(this.packaging.manifest),this.loading.metadata.resolve(this.packaging.metadata),this.loading.spine.resolve(this.spine),this.loading.cover.resolve(this.cover),this.loading.resources.resolve(this.resources),this.loading.pageList.resolve(this.pageList),this.isOpen=!0,this.archived||this.settings.replacements&&"none"!=this.settings.replacements?this.replacements().then((function(){e.loaded.displayOptions.then((function(){e.opening.resolve(e)}))})).catch((function(t){console.error(t)})):this.loaded.displayOptions.then((function(){e.opening.resolve(e)}))}},{key:"loadNavigation",value:function(t){var e=this,n=t.navPath||t.ncxPath,i=t.toc;return i?new Promise((function(n,r){e.navigation=new C(i),t.pageList&&(e.pageList=new D(t.pageList)),n(e.navigation)})):n?this.load(n,"xml").then((function(t){return e.navigation=new C(t),e.pageList=new D(t),e.navigation})):new Promise((function(t,n){e.navigation=new C,e.pageList=new D,t(e.navigation)}))}},{key:"section",value:function(t){return this.spine.get(t)}},{key:"renderTo",value:function(t,e){return this.rendition=new M.a(this,e),this.rendition.attachTo(t),this.rendition}},{key:"setRequestCredentials",value:function(t){this.settings.requestCredentials=t}},{key:"setRequestHeaders",value:function(t){this.settings.requestHeaders=t}},{key:"unarchive",value:function(t,e){return this.archive=new U,this.archive.open(t,e)}},{key:"store",value:function(t){var e=this,n=this.settings.replacements&&"none"!==this.settings.replacements,i=this.url,r=this.settings.requestMethod||h.bind(this);return this.storage=new X(t,r,this.resolve.bind(this)),this.request=this.storage.request.bind(this.storage),this.opened.then((function(){e.archived&&(e.storage.requester=e.archive.request.bind(e.archive));var t=function(t,n){n.output=e.resources.substitute(t,n.url)};e.resources.settings.replacements=n||"blobUrl",e.resources.replacements().then((function(){return e.resources.replaceCss()})),e.storage.on("offline",(function(){e.url=new s.a("/",""),e.spine.hooks.serialize.register(t)})),e.storage.on("online",(function(){e.url=i,e.spine.hooks.serialize.deregister(t)}))})),this.storage}},{key:"coverUrl",value:function(){var t=this;return this.loaded.cover.then((function(){return t.cover?t.archived?t.archive.createUrl(t.cover):t.cover:null}))}},{key:"replacements",value:function(){var t=this;return this.spine.hooks.serialize.register((function(e,n){n.output=t.resources.substitute(e,n.url)})),this.resources.replacements().then((function(){return t.resources.replaceCss()}))}},{key:"getRange",value:function(t){var e=new u.a(t),n=this.spine.get(e.spinePos),i=this.load.bind(this);return n?n.load(i).then((function(t){return e.toRange(n.document)})):new Promise((function(t,e){e("CFI could not be found")}))}},{key:"key",value:function(t){var e=t||this.packaging.metadata.identifier||this.url.filename;return"epubjs:".concat(y.b,":").concat(e)}},{key:"destroy",value:function(){this.opened=void 0,this.loading=void 0,this.loaded=void 0,this.ready=void 0,this.isOpen=!1,this.isRendered=!1,this.spine&&this.spine.destroy(),this.locations&&this.locations.destroy(),this.pageList&&this.pageList.destroy(),this.archive&&this.archive.destroy(),this.resources&&this.resources.destroy(),this.container&&this.container.destroy(),this.packaging&&this.packaging.destroy(),this.rendition&&this.rendition.destroy(),this.displayOptions&&this.displayOptions.destroy(),this.spine=void 0,this.locations=void 0,this.pageList=void 0,this.archive=void 0,this.resources=void 0,this.container=void 0,this.packaging=void 0,this.rendition=void 0,this.navigation=void 0,this.url=void 0,this.path=void 0,this.archived=!1}}])&&$(e.prototype,n),i&&$(e,i),t}();r()(nt.prototype);e.a=nt},function(t,e,n){var i=n(102);t.exports=i&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},function(t,e){t.exports=function(t){try{return String(t)}catch(t){return"Object"}}},function(t,e,n){var i=n(17),r=n(3),o=n(80);t.exports=!i&&!r((function(){return 7!=Object.defineProperty(o("div"),"a",{get:function(){return 7}}).a}))},function(t,e,n){var i=n(13),r=n(174),o=n(45),s=n(20);t.exports=function(t,e){for(var n=r(e),a=s.f,u=o.f,c=0;c<n.length;c++){var l=n[c];i(t,l)||a(t,l,u(e,l))}}},function(t,e,n){var i=n(13),r=n(31),o=n(132).indexOf,s=n(57);t.exports=function(t,e){var n,a=r(t),u=0,c=[];for(n in a)!i(s,n)&&i(a,n)&&c.push(n);for(;e.length>u;)i(a,n=e[u++])&&(~o(c,n)||c.push(n));return c}},function(t,e,n){var i=n(31),r=n(83),o=n(36),s=function(t){return function(e,n,s){var a,u=i(e),c=o(u),l=r(s,c);if(t&&n!=n){for(;c>l;)if((a=u[l++])!=a)return!0}else for(;c>l;l++)if((t||l in u)&&u[l]===n)return t||l||0;return!t&&-1}};t.exports={includes:s(!0),indexOf:s(!1)}},function(t,e,n){var i=n(3);t.exports=!i((function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype}))},function(t,e,n){"use strict";var i=n(24),r=n(83),o=n(36);t.exports=function(t){for(var e=i(this),n=o(e),s=arguments.length,a=r(s>1?arguments[1]:void 0,n),u=s>2?arguments[2]:void 0,c=void 0===u?n:r(u,n);c>a;)e[a++]=t;return e}},function(t,e,n){var i=n(17),r=n(20),o=n(8),s=n(87);t.exports=i?Object.defineProperties:function(t,e){o(t);for(var n,i=s(e),a=i.length,u=0;a>u;)r.f(t,n=i[u++],e[n]);return t}},function(t,e,n){var i=n(29);t.exports=i("document","documentElement")},function(t,e,n){"use strict";var i=n(5),r=n(49),o=n(50),s=n(7),a=n(138),u=n(58),c=n(85),l=n(42),h=n(33),f=n(25),d=n(6),p=n(60),v=n(139),g=o.PROPER,m=o.CONFIGURABLE,y=v.IteratorPrototype,b=v.BUGGY_SAFARI_ITERATORS,w=d("iterator"),x=function(){return this};t.exports=function(t,e,n,o,d,v,k){a(n,e,o);var E,S,O,T=function(t){if(t===d&&I)return I;if(!b&&t in C)return C[t];switch(t){case"keys":case"values":case"entries":return function(){return new n(this,t)}}return function(){return new n(this)}},_=e+" Iterator",N=!1,C=t.prototype,R=C[w]||C["@@iterator"]||d&&C[d],I=!b&&R||T(d),A="Array"==e&&C.entries||R;if(A&&(E=u(A.call(new t)))!==Object.prototype&&E.next&&(r||u(E)===y||(c?c(E,y):s(E[w])||f(E,w,x)),l(E,_,!0,!0),r&&(p[_]=x)),g&&"values"==d&&R&&"values"!==R.name&&(!r&&m?h(C,"name","values"):(N=!0,I=function(){return R.call(this)})),d)if(S={values:T("values"),keys:v?I:T("keys"),entries:T("entries")},k)for(O in S)(b||N||!(O in C))&&f(C,O,S[O]);else i({target:e,proto:!0,forced:b||N},S);return r&&!k||C[w]===I||f(C,w,I,{name:d}),p[e]=I,S}},function(t,e,n){"use strict";var i=n(139).IteratorPrototype,r=n(43),o=n(46),s=n(42),a=n(60),u=function(){return this};t.exports=function(t,e,n){var c=e+" Iterator";return t.prototype=r(i,{next:o(1,n)}),s(t,c,!1,!0),a[c]=u,t}},function(t,e,n){"use strict";var i,r,o,s=n(3),a=n(7),u=n(43),c=n(58),l=n(25),h=n(6),f=n(49),d=h("iterator"),p=!1;[].keys&&("next"in(o=[].keys())?(r=c(c(o)))!==Object.prototype&&(i=r):p=!0),null==i||s((function(){var t={};return i[d].call(t)!==t}))?i={}:f&&(i=u(i)),a(i[d])||l(i,d,(function(){return this})),t.exports={IteratorPrototype:i,BUGGY_SAFARI_ITERATORS:p}},function(t,e,n){var i=n(6),r=n(60),o=i("iterator"),s=Array.prototype;t.exports=function(t){return void 0!==t&&(r.Array===t||s[o]===t)}},function(t,e,n){var i=n(8),r=n(40);t.exports=function(t,e,n){var o,s;i(t);try{if(!(o=r(t,"return"))){if("throw"===e)throw n;return n}o=o.call(t)}catch(t){s=!0,o=t}if("throw"===e)throw n;if(s)throw o;return i(o),n}},function(t,e,n){var i=n(8),r=n(143),o=n(6)("species");t.exports=function(t,e){var n,s=i(t).constructor;return void 0===s||null==(n=i(s)[o])?e:r(n)}},function(t,e,n){var i=n(89),r=n(128);t.exports=function(t){if(i(t))return t;throw TypeError(r(t)+" is not a constructor")}},function(t,e,n){var i,r,o,s,a=n(4),u=n(7),c=n(3),l=n(61),h=n(136),f=n(80),d=n(145),p=n(112),v=a.setImmediate,g=a.clearImmediate,m=a.process,y=a.MessageChannel,b=a.Dispatch,w=0,x={};try{i=a.location}catch(t){}var k=function(t){if(x.hasOwnProperty(t)){var e=x[t];delete x[t],e()}},E=function(t){return function(){k(t)}},S=function(t){k(t.data)},O=function(t){a.postMessage(String(t),i.protocol+"//"+i.host)};v&&g||(v=function(t){for(var e=[],n=arguments.length,i=1;n>i;)e.push(arguments[i++]);return x[++w]=function(){(u(t)?t:Function(t)).apply(void 0,e)},r(w),w},g=function(t){delete x[t]},p?r=function(t){m.nextTick(E(t))}:b&&b.now?r=function(t){b.now(E(t))}:y&&!d?(s=(o=new y).port2,o.port1.onmessage=S,r=l(s.postMessage,s,1)):a.addEventListener&&u(a.postMessage)&&!a.importScripts&&i&&"file:"!==i.protocol&&!c(O)?(r=O,a.addEventListener("message",S,!1)):r="onreadystatechange"in f("script")?function(t){h.appendChild(f("script")).onreadystatechange=function(){h.removeChild(this),k(t)}}:function(t){setTimeout(E(t),0)}),t.exports={set:v,clear:g}},function(t,e,n){var i=n(77);t.exports=/(?:ipad|iphone|ipod).*applewebkit/i.test(i)},function(t,e,n){"use strict";var i=n(48),r=function(t){var e,n;this.promise=new t((function(t,i){if(void 0!==e||void 0!==n)throw TypeError("Bad Promise constructor");e=t,n=i})),this.resolve=i(e),this.reject=i(n)};t.exports.f=function(t){return new r(t)}},function(t,e){t.exports={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}},function(t,e,n){var i=n(80)("span").classList,r=i&&i.constructor&&i.constructor.prototype;t.exports=r===Object.prototype?void 0:r},function(t,e,n){var i=n(3),r=n(4).RegExp;t.exports=i((function(){var t=r(".","s");return!(t.dotAll&&t.exec("\n")&&"s"===t.flags)}))},function(t,e,n){var i=n(3),r=n(4).RegExp;t.exports=i((function(){var t=r("(?<a>b)","g");return"b"!==t.exec("b").groups.a||"bc"!=="b".replace(t,"$<a>c")}))},function(t,e,n){var i=n(3),r=n(6),o=n(49),s=r("iterator");t.exports=!i((function(){var t=new URL("b?a=1&b=2&c=3","http://a"),e=t.searchParams,n="";return t.pathname="c%20d",e.forEach((function(t,i){e.delete("b"),n+=i+t})),o&&!t.toJSON||!e.sort||"http://a/c%20d?a=1&c=3"!==t.href||"3"!==e.get("c")||"a=1"!==String(new URLSearchParams("?a=1"))||!e[s]||"a"!==new URL("https://a@b").username||"b"!==new URLSearchParams(new URLSearchParams("a=b")).get("a")||"xn--e1aybc"!==new URL("http://тест").host||"#%D0%B1"!==new URL("http://a#б").hash||"a1c3"!==n||"x"!==new URL("http://x",void 0).host}))},function(t,e,n){"use strict";var i=n(17),r=n(3),o=n(87),s=n(106),a=n(100),u=n(24),c=n(74),l=Object.assign,h=Object.defineProperty;t.exports=!l||r((function(){if(i&&1!==l({b:1},l(h({},"a",{enumerable:!0,get:function(){h(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var t={},e={},n=Symbol();return t[n]=7,"abcdefghijklmnopqrst".split("").forEach((function(t){e[t]=t})),7!=l({},t)[n]||"abcdefghijklmnopqrst"!=o(l({},e)).join("")}))?function(t,e){for(var n=u(t),r=arguments.length,l=1,h=s.f,f=a.f;r>l;)for(var d,p=c(arguments[l++]),v=h?o(p).concat(h(p)):o(p),g=v.length,m=0;g>m;)d=v[m++],i&&!f.call(p,d)||(n[d]=p[d]);return n}:l},function(t,e,n){"use strict";var i=n(3);t.exports=function(t,e){var n=[][t];return!!n&&i((function(){n.call(null,e||function(){throw 1},1)}))}},function(t,e,n){var i=n(5),r=n(3),o=n(31),s=n(45).f,a=n(17),u=r((function(){s(1)}));i({target:"Object",stat:!0,forced:!a||u,sham:!a},{getOwnPropertyDescriptor:function(t,e){return s(o(t),e)}})},function(t,e,n){var i=n(3);t.exports=!i((function(){return Object.isExtensible(Object.preventExtensions({}))}))},function(t,e,n){var i=n(96).NAMESPACE;function r(t){return""!==t}function o(t,e){return t.hasOwnProperty(e)||(t[e]=!0),t}function s(t){if(!t)return[];var e=function(t){return t?t.split(/[\t\n\f\r ]+/).filter(r):[]}(t);return Object.keys(e.reduce(o,{}))}function a(t,e){for(var n in t)e[n]=t[n]}function u(t,e){var n=t.prototype;if(!(n instanceof e)){function i(){}i.prototype=e.prototype,a(n,i=new i),t.prototype=n=i}n.constructor!=t&&("function"!=typeof t&&console.error("unknown Class:"+t),n.constructor=t)}var c={},l=c.ELEMENT_NODE=1,h=c.ATTRIBUTE_NODE=2,f=c.TEXT_NODE=3,d=c.CDATA_SECTION_NODE=4,p=c.ENTITY_REFERENCE_NODE=5,v=c.ENTITY_NODE=6,g=c.PROCESSING_INSTRUCTION_NODE=7,m=c.COMMENT_NODE=8,y=c.DOCUMENT_NODE=9,b=c.DOCUMENT_TYPE_NODE=10,w=c.DOCUMENT_FRAGMENT_NODE=11,x=c.NOTATION_NODE=12,k={},E={},S=(k.INDEX_SIZE_ERR=(E[1]="Index size error",1),k.DOMSTRING_SIZE_ERR=(E[2]="DOMString size error",2),k.HIERARCHY_REQUEST_ERR=(E[3]="Hierarchy request error",3)),O=(k.WRONG_DOCUMENT_ERR=(E[4]="Wrong document",4),k.INVALID_CHARACTER_ERR=(E[5]="Invalid character",5),k.NO_DATA_ALLOWED_ERR=(E[6]="No data allowed",6),k.NO_MODIFICATION_ALLOWED_ERR=(E[7]="No modification allowed",7),k.NOT_FOUND_ERR=(E[8]="Not found",8)),T=(k.NOT_SUPPORTED_ERR=(E[9]="Not supported",9),k.INUSE_ATTRIBUTE_ERR=(E[10]="Attribute in use",10));k.INVALID_STATE_ERR=(E[11]="Invalid state",11),k.SYNTAX_ERR=(E[12]="Syntax error",12),k.INVALID_MODIFICATION_ERR=(E[13]="Invalid modification",13),k.NAMESPACE_ERR=(E[14]="Invalid namespace",14),k.INVALID_ACCESS_ERR=(E[15]="Invalid access",15);function _(t,e){if(e instanceof Error)var n=e;else n=this,Error.call(this,E[t]),this.message=E[t],Error.captureStackTrace&&Error.captureStackTrace(this,_);return n.code=t,e&&(this.message=this.message+": "+e),n}function N(){}function C(t,e){this._node=t,this._refresh=e,R(this)}function R(t){var e=t._node._inc||t._node.ownerDocument._inc;if(t._inc!=e){var n=t._refresh(t._node);at(t,"length",n.length),a(n,t),t._inc=e}}function I(){}function A(t,e){for(var n=t.length;n--;)if(t[n]===e)return n}function j(t,e,n,r){if(r?e[A(e,r)]=n:e[e.length++]=n,t){n.ownerElement=t;var o=t.ownerDocument;o&&(r&&q(o,t,r),function(t,e,n){t&&t._inc++,n.namespaceURI===i.XMLNS&&(e._nsMap[n.prefix?n.localName:""]=n.value)}(o,t,n))}}function L(t,e,n){var i=A(e,n);if(!(i>=0))throw _(O,new Error(t.tagName+"@"+n));for(var r=e.length-1;i<r;)e[i]=e[++i];if(e.length=r,t){var o=t.ownerDocument;o&&(q(o,t,n),n.ownerElement=null)}}function P(){}function D(){}function M(t){return("<"==t?"&lt;":">"==t&&"&gt;")||"&"==t&&"&amp;"||'"'==t&&"&quot;"||"&#"+t.charCodeAt()+";"}function z(t,e){if(e(t))return!0;if(t=t.firstChild)do{if(z(t,e))return!0}while(t=t.nextSibling)}function B(){}function q(t,e,n,r){t&&t._inc++,n.namespaceURI===i.XMLNS&&delete e._nsMap[n.prefix?n.localName:""]}function U(t,e,n){if(t&&t._inc){t._inc++;var i=e.childNodes;if(n)i[i.length++]=n;else{for(var r=e.firstChild,o=0;r;)i[o++]=r,r=r.nextSibling;i.length=o}}}function F(t,e){var n=e.previousSibling,i=e.nextSibling;return n?n.nextSibling=i:t.firstChild=i,i?i.previousSibling=n:t.lastChild=n,U(t.ownerDocument,t),e}function W(t,e,n){var i=e.parentNode;if(i&&i.removeChild(e),e.nodeType===w){var r=e.firstChild;if(null==r)return e;var o=e.lastChild}else r=o=e;var s=n?n.previousSibling:t.lastChild;r.previousSibling=s,o.nextSibling=n,s?s.nextSibling=r:t.firstChild=r,null==n?t.lastChild=o:n.previousSibling=o;do{r.parentNode=t}while(r!==o&&(r=r.nextSibling));return U(t.ownerDocument||t,t),e.nodeType==w&&(e.firstChild=e.lastChild=null),e}function H(){this._nsMap={}}function V(){}function X(){}function G(){}function Y(){}function $(){}function K(){}function Z(){}function J(){}function Q(){}function tt(){}function et(){}function nt(){}function it(t,e){var n=[],i=9==this.nodeType&&this.documentElement||this,r=i.prefix,o=i.namespaceURI;if(o&&null==r&&null==(r=i.lookupPrefix(o)))var s=[{namespace:o,prefix:null}];return st(this,n,t,e,s),n.join("")}function rt(t,e,n){var r=t.prefix||"",o=t.namespaceURI;if(!o)return!1;if("xml"===r&&o===i.XML||o===i.XMLNS)return!1;for(var s=n.length;s--;){var a=n[s];if(a.prefix===r)return a.namespace!==o}return!0}function ot(t,e,n){t.push(" ",e,'="',n.replace(/[<&"]/g,M),'"')}function st(t,e,n,r,o){if(o||(o=[]),r){if(!(t=r(t)))return;if("string"==typeof t)return void e.push(t)}switch(t.nodeType){case l:var s=t.attributes,a=s.length,u=t.firstChild,c=t.tagName,v=c;if(!(n=i.isHTML(t.namespaceURI)||n)&&!t.prefix&&t.namespaceURI){for(var x,k=0;k<s.length;k++)if("xmlns"===s.item(k).name){x=s.item(k).value;break}if(!x)for(var E=o.length-1;E>=0;E--){if(""===(S=o[E]).prefix&&S.namespace===t.namespaceURI){x=S.namespace;break}}if(x!==t.namespaceURI)for(E=o.length-1;E>=0;E--){var S;if((S=o[E]).namespace===t.namespaceURI){S.prefix&&(v=S.prefix+":"+c);break}}}e.push("<",v);for(var O=0;O<a;O++){"xmlns"==(T=s.item(O)).prefix?o.push({prefix:T.localName,namespace:T.value}):"xmlns"==T.nodeName&&o.push({prefix:"",namespace:T.value})}for(O=0;O<a;O++){var T,_,N;if(rt(T=s.item(O),0,o))ot(e,(_=T.prefix||"")?"xmlns:"+_:"xmlns",N=T.namespaceURI),o.push({prefix:_,namespace:N});st(T,e,n,r,o)}if(c===v&&rt(t,0,o))ot(e,(_=t.prefix||"")?"xmlns:"+_:"xmlns",N=t.namespaceURI),o.push({prefix:_,namespace:N});if(u||n&&!/^(?:meta|link|img|br|hr|input)$/i.test(c)){if(e.push(">"),n&&/^script$/i.test(c))for(;u;)u.data?e.push(u.data):st(u,e,n,r,o.slice()),u=u.nextSibling;else for(;u;)st(u,e,n,r,o.slice()),u=u.nextSibling;e.push("</",v,">")}else e.push("/>");return;case y:case w:for(u=t.firstChild;u;)st(u,e,n,r,o.slice()),u=u.nextSibling;return;case h:return ot(e,t.name,t.value);case f:return e.push(t.data.replace(/[<&]/g,M).replace(/]]>/g,"]]&gt;"));case d:return e.push("<![CDATA[",t.data,"]]>");case m:return e.push("\x3c!--",t.data,"--\x3e");case b:var C=t.publicId,R=t.systemId;if(e.push("<!DOCTYPE ",t.name),C)e.push(" PUBLIC ",C),R&&"."!=R&&e.push(" ",R),e.push(">");else if(R&&"."!=R)e.push(" SYSTEM ",R,">");else{var I=t.internalSubset;I&&e.push(" [",I,"]"),e.push(">")}return;case g:return e.push("<?",t.target," ",t.data,"?>");case p:return e.push("&",t.nodeName,";");default:e.push("??",t.nodeName)}}function at(t,e,n){t[e]=n}_.prototype=Error.prototype,a(k,_),N.prototype={length:0,item:function(t){return this[t]||null},toString:function(t,e){for(var n=[],i=0;i<this.length;i++)st(this[i],n,t,e);return n.join("")}},C.prototype.item=function(t){return R(this),this[t]},u(C,N),I.prototype={length:0,item:N.prototype.item,getNamedItem:function(t){for(var e=this.length;e--;){var n=this[e];if(n.nodeName==t)return n}},setNamedItem:function(t){var e=t.ownerElement;if(e&&e!=this._ownerElement)throw new _(T);var n=this.getNamedItem(t.nodeName);return j(this._ownerElement,this,t,n),n},setNamedItemNS:function(t){var e,n=t.ownerElement;if(n&&n!=this._ownerElement)throw new _(T);return e=this.getNamedItemNS(t.namespaceURI,t.localName),j(this._ownerElement,this,t,e),e},removeNamedItem:function(t){var e=this.getNamedItem(t);return L(this._ownerElement,this,e),e},removeNamedItemNS:function(t,e){var n=this.getNamedItemNS(t,e);return L(this._ownerElement,this,n),n},getNamedItemNS:function(t,e){for(var n=this.length;n--;){var i=this[n];if(i.localName==e&&i.namespaceURI==t)return i}return null}},P.prototype={hasFeature:function(t,e){return!0},createDocument:function(t,e,n){var i=new B;if(i.implementation=this,i.childNodes=new N,i.doctype=n||null,n&&i.appendChild(n),e){var r=i.createElementNS(t,e);i.appendChild(r)}return i},createDocumentType:function(t,e,n){var i=new K;return i.name=t,i.nodeName=t,i.publicId=e||"",i.systemId=n||"",i}},D.prototype={firstChild:null,lastChild:null,previousSibling:null,nextSibling:null,attributes:null,parentNode:null,childNodes:null,ownerDocument:null,nodeValue:null,namespaceURI:null,prefix:null,localName:null,insertBefore:function(t,e){return W(this,t,e)},replaceChild:function(t,e){this.insertBefore(t,e),e&&this.removeChild(e)},removeChild:function(t){return F(this,t)},appendChild:function(t){return this.insertBefore(t,null)},hasChildNodes:function(){return null!=this.firstChild},cloneNode:function(t){return function t(e,n,i){var r=new n.constructor;for(var o in n){var s=n[o];"object"!=typeof s&&s!=r[o]&&(r[o]=s)}n.childNodes&&(r.childNodes=new N);switch(r.ownerDocument=e,r.nodeType){case l:var a=n.attributes,u=r.attributes=new I,c=a.length;u._ownerElement=r;for(var f=0;f<c;f++)r.setAttributeNode(t(e,a.item(f),!0));break;case h:i=!0}if(i)for(var d=n.firstChild;d;)r.appendChild(t(e,d,i)),d=d.nextSibling;return r}(this.ownerDocument||this,this,t)},normalize:function(){for(var t=this.firstChild;t;){var e=t.nextSibling;e&&e.nodeType==f&&t.nodeType==f?(this.removeChild(e),t.appendData(e.data)):(t.normalize(),t=e)}},isSupported:function(t,e){return this.ownerDocument.implementation.hasFeature(t,e)},hasAttributes:function(){return this.attributes.length>0},lookupPrefix:function(t){for(var e=this;e;){var n=e._nsMap;if(n)for(var i in n)if(n[i]==t)return i;e=e.nodeType==h?e.ownerDocument:e.parentNode}return null},lookupNamespaceURI:function(t){for(var e=this;e;){var n=e._nsMap;if(n&&t in n)return n[t];e=e.nodeType==h?e.ownerDocument:e.parentNode}return null},isDefaultNamespace:function(t){return null==this.lookupPrefix(t)}},a(c,D),a(c,D.prototype),B.prototype={nodeName:"#document",nodeType:y,doctype:null,documentElement:null,_inc:1,insertBefore:function(t,e){if(t.nodeType==w){for(var n=t.firstChild;n;){var i=n.nextSibling;this.insertBefore(n,e),n=i}return t}return null==this.documentElement&&t.nodeType==l&&(this.documentElement=t),W(this,t,e),t.ownerDocument=this,t},removeChild:function(t){return this.documentElement==t&&(this.documentElement=null),F(this,t)},importNode:function(t,e){return function t(e,n,i){var r;switch(n.nodeType){case l:(r=n.cloneNode(!1)).ownerDocument=e;case w:break;case h:i=!0}r||(r=n.cloneNode(!1));if(r.ownerDocument=e,r.parentNode=null,i)for(var o=n.firstChild;o;)r.appendChild(t(e,o,i)),o=o.nextSibling;return r}(this,t,e)},getElementById:function(t){var e=null;return z(this.documentElement,(function(n){if(n.nodeType==l&&n.getAttribute("id")==t)return e=n,!0})),e},getElementsByClassName:function(t){var e=s(t);return new C(this,(function(n){var i=[];return e.length>0&&z(n.documentElement,(function(r){if(r!==n&&r.nodeType===l){var o=r.getAttribute("class");if(o){var a=t===o;if(!a){var u=s(o);a=e.every((c=u,function(t){return c&&-1!==c.indexOf(t)}))}a&&i.push(r)}}var c})),i}))},createElement:function(t){var e=new H;return e.ownerDocument=this,e.nodeName=t,e.tagName=t,e.localName=t,e.childNodes=new N,(e.attributes=new I)._ownerElement=e,e},createDocumentFragment:function(){var t=new tt;return t.ownerDocument=this,t.childNodes=new N,t},createTextNode:function(t){var e=new G;return e.ownerDocument=this,e.appendData(t),e},createComment:function(t){var e=new Y;return e.ownerDocument=this,e.appendData(t),e},createCDATASection:function(t){var e=new $;return e.ownerDocument=this,e.appendData(t),e},createProcessingInstruction:function(t,e){var n=new et;return n.ownerDocument=this,n.tagName=n.target=t,n.nodeValue=n.data=e,n},createAttribute:function(t){var e=new V;return e.ownerDocument=this,e.name=t,e.nodeName=t,e.localName=t,e.specified=!0,e},createEntityReference:function(t){var e=new Q;return e.ownerDocument=this,e.nodeName=t,e},createElementNS:function(t,e){var n=new H,i=e.split(":"),r=n.attributes=new I;return n.childNodes=new N,n.ownerDocument=this,n.nodeName=e,n.tagName=e,n.namespaceURI=t,2==i.length?(n.prefix=i[0],n.localName=i[1]):n.localName=e,r._ownerElement=n,n},createAttributeNS:function(t,e){var n=new V,i=e.split(":");return n.ownerDocument=this,n.nodeName=e,n.name=e,n.namespaceURI=t,n.specified=!0,2==i.length?(n.prefix=i[0],n.localName=i[1]):n.localName=e,n}},u(B,D),H.prototype={nodeType:l,hasAttribute:function(t){return null!=this.getAttributeNode(t)},getAttribute:function(t){var e=this.getAttributeNode(t);return e&&e.value||""},getAttributeNode:function(t){return this.attributes.getNamedItem(t)},setAttribute:function(t,e){var n=this.ownerDocument.createAttribute(t);n.value=n.nodeValue=""+e,this.setAttributeNode(n)},removeAttribute:function(t){var e=this.getAttributeNode(t);e&&this.removeAttributeNode(e)},appendChild:function(t){return t.nodeType===w?this.insertBefore(t,null):function(t,e){var n=e.parentNode;if(n){var i=t.lastChild;n.removeChild(e);i=t.lastChild}return i=t.lastChild,e.parentNode=t,e.previousSibling=i,e.nextSibling=null,i?i.nextSibling=e:t.firstChild=e,t.lastChild=e,U(t.ownerDocument,t,e),e}(this,t)},setAttributeNode:function(t){return this.attributes.setNamedItem(t)},setAttributeNodeNS:function(t){return this.attributes.setNamedItemNS(t)},removeAttributeNode:function(t){return this.attributes.removeNamedItem(t.nodeName)},removeAttributeNS:function(t,e){var n=this.getAttributeNodeNS(t,e);n&&this.removeAttributeNode(n)},hasAttributeNS:function(t,e){return null!=this.getAttributeNodeNS(t,e)},getAttributeNS:function(t,e){var n=this.getAttributeNodeNS(t,e);return n&&n.value||""},setAttributeNS:function(t,e,n){var i=this.ownerDocument.createAttributeNS(t,e);i.value=i.nodeValue=""+n,this.setAttributeNode(i)},getAttributeNodeNS:function(t,e){return this.attributes.getNamedItemNS(t,e)},getElementsByTagName:function(t){return new C(this,(function(e){var n=[];return z(e,(function(i){i===e||i.nodeType!=l||"*"!==t&&i.tagName!=t||n.push(i)})),n}))},getElementsByTagNameNS:function(t,e){return new C(this,(function(n){var i=[];return z(n,(function(r){r===n||r.nodeType!==l||"*"!==t&&r.namespaceURI!==t||"*"!==e&&r.localName!=e||i.push(r)})),i}))}},B.prototype.getElementsByTagName=H.prototype.getElementsByTagName,B.prototype.getElementsByTagNameNS=H.prototype.getElementsByTagNameNS,u(H,D),V.prototype.nodeType=h,u(V,D),X.prototype={data:"",substringData:function(t,e){return this.data.substring(t,t+e)},appendData:function(t){t=this.data+t,this.nodeValue=this.data=t,this.length=t.length},insertData:function(t,e){this.replaceData(t,0,e)},appendChild:function(t){throw new Error(E[S])},deleteData:function(t,e){this.replaceData(t,e,"")},replaceData:function(t,e,n){n=this.data.substring(0,t)+n+this.data.substring(t+e),this.nodeValue=this.data=n,this.length=n.length}},u(X,D),G.prototype={nodeName:"#text",nodeType:f,splitText:function(t){var e=this.data,n=e.substring(t);e=e.substring(0,t),this.data=this.nodeValue=e,this.length=e.length;var i=this.ownerDocument.createTextNode(n);return this.parentNode&&this.parentNode.insertBefore(i,this.nextSibling),i}},u(G,X),Y.prototype={nodeName:"#comment",nodeType:m},u(Y,X),$.prototype={nodeName:"#cdata-section",nodeType:d},u($,X),K.prototype.nodeType=b,u(K,D),Z.prototype.nodeType=x,u(Z,D),J.prototype.nodeType=v,u(J,D),Q.prototype.nodeType=p,u(Q,D),tt.prototype.nodeName="#document-fragment",tt.prototype.nodeType=w,u(tt,D),et.prototype.nodeType=g,u(et,D),nt.prototype.serializeToString=function(t,e,n){return it.call(t,e,n)},D.prototype.toString=it;try{if(Object.defineProperty){Object.defineProperty(C.prototype,"length",{get:function(){return R(this),this.$$length}}),Object.defineProperty(D.prototype,"textContent",{get:function(){return function t(e){switch(e.nodeType){case l:case w:var n=[];for(e=e.firstChild;e;)7!==e.nodeType&&8!==e.nodeType&&n.push(t(e)),e=e.nextSibling;return n.join("");default:return e.nodeValue}}(this)},set:function(t){switch(this.nodeType){case l:case w:for(;this.firstChild;)this.removeChild(this.firstChild);(t||String(t))&&this.appendChild(this.ownerDocument.createTextNode(t));break;default:this.data=t,this.value=t,this.nodeValue=t}}}),at=function(t,e,n){t["$$"+e]=n}}}catch(t){}e.DocumentType=K,e.DOMException=_,e.DOMImplementation=P,e.Element=H,e.Node=D,e.NodeList=N,e.XMLSerializer=nt},function(t,e,n){"use strict";var i=n(91),r=n(8),o=n(52),s=n(21),a=n(32),u=n(40),c=n(116),l=n(92);i("match",(function(t,e,n){return[function(e){var n=a(this),i=null==e?void 0:u(e,t);return i?i.call(e,n):new RegExp(e)[t](s(n))},function(t){var i=r(this),a=s(t),u=n(e,i,a);if(u.done)return u.value;if(!i.global)return l(i,a);var h=i.unicode;i.lastIndex=0;for(var f,d=[],p=0;null!==(f=l(i,a));){var v=s(f[0]);d[p]=v,""===v&&(i.lastIndex=c(a,o(i.lastIndex),h)),p++}return 0===p?null:d}]}))},function(t,e,n){"use strict";var i=n(5),r=n(74),o=n(31),s=n(153),a=[].join,u=r!=Object,c=s("join",",");i({target:"Array",proto:!0,forced:u||!c},{join:function(t){return a.call(o(this),void 0===t?",":t)}})},function(t,e,n){var i=n(6);e.f=i},function(t,e,n){var i=n(220),r=n(13),o=n(159),s=n(20).f;t.exports=function(t){var e=i.Symbol||(i.Symbol={});r(e,t)||s(e,t,{value:o.f(t)})}},function(t,e,n){"use strict";var i=n(5),r=n(65).find,o=n(86),s=!0;"find"in[]&&Array(1).find((function(){s=!1})),i({target:"Array",proto:!0,forced:s},{find:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}}),o("find")},function(t,e){t.exports="\t\n\v\f\r                　\u2028\u2029\ufeff"},function(t,e,n){"use strict";var i=n(5),r=n(132).includes,o=n(86);i({target:"Array",proto:!0},{includes:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}}),o("includes")},function(t,e,n){"use strict";var i=n(5),r=n(225),o=n(32),s=n(21);i({target:"String",proto:!0,forced:!n(226)("includes")},{includes:function(t){return!!~s(o(this)).indexOf(s(r(t)),arguments.length>1?arguments[1]:void 0)}})},function(t,e,n){var i=n(231),r="object"==typeof self&&self&&self.Object===Object&&self,o=i||r||Function("return this")();t.exports=o},function(t,e,n){var i=n(165).Symbol;t.exports=i},function(t,e,n){var i=n(123),r=n(121);t.exports=function(t,e,n){var o=!0,s=!0;if("function"!=typeof t)throw new TypeError("Expected a function");return r(n)&&(o="leading"in n?!!n.leading:o,s="trailing"in n?!!n.trailing:s),i(t,e,{leading:o,maxWait:e,trailing:s})}},function(e,n){e.exports=t},function(t,e,n){"use strict";n.r(e),function(t){var i=n(126),r=n(99),o=n(2),s=n(71),a=n(0),u=n(1);n(122),n(55),n(124);function c(t,e){return new i.a(t,e)}c.VERSION=u.b,void 0!==t&&(t.EPUBJS_VERSION=u.b),c.Book=i.a,c.Rendition=r.a,c.Contents=s.a,c.CFI=o.a,c.utils=a,e.default=c}.call(this,n(73))},function(t,e,n){"use strict";var i=n(5),r=n(4),o=n(175),s=n(109),a=o.ArrayBuffer;i({global:!0,forced:r.ArrayBuffer!==a},{ArrayBuffer:a}),s("ArrayBuffer")},function(t,e,n){var i=n(9),r=n(101),o=n(40),s=n(172),a=n(6)("toPrimitive");t.exports=function(t,e){if(!i(t)||r(t))return t;var n,u=o(t,a);if(u){if(void 0===e&&(e="default"),n=u.call(t,e),!i(n)||r(n))return n;throw TypeError("Can't convert object to primitive value")}return void 0===e&&(e="number"),s(t,e)}},function(t,e,n){var i=n(7),r=n(9);t.exports=function(t,e){var n,o;if("string"===e&&i(n=t.toString)&&!r(o=n.call(t)))return o;if(i(n=t.valueOf)&&!r(o=n.call(t)))return o;if("string"!==e&&i(n=t.toString)&&!r(o=n.call(t)))return o;throw TypeError("Can't convert object to primitive value")}},function(t,e,n){var i=n(4),r=n(7),o=n(81),s=i.WeakMap;t.exports=r(s)&&/native code/.test(o(s))},function(t,e,n){var i=n(29),r=n(51),o=n(106),s=n(8);t.exports=i("Reflect","ownKeys")||function(t){var e=r.f(s(t)),n=o.f;return n?e.concat(n(t)):e}},function(t,e,n){"use strict";var i=n(4),r=n(17),o=n(176),s=n(50),a=n(33),u=n(108),c=n(3),l=n(84),h=n(41),f=n(52),d=n(177),p=n(178),v=n(58),g=n(85),m=n(51).f,y=n(20).f,b=n(134),w=n(42),x=n(30),k=s.PROPER,E=s.CONFIGURABLE,S=x.get,O=x.set,T=i.ArrayBuffer,_=T,N=i.DataView,C=N&&N.prototype,R=Object.prototype,I=i.RangeError,A=p.pack,j=p.unpack,L=function(t){return[255&t]},P=function(t){return[255&t,t>>8&255]},D=function(t){return[255&t,t>>8&255,t>>16&255,t>>24&255]},M=function(t){return t[3]<<24|t[2]<<16|t[1]<<8|t[0]},z=function(t){return A(t,23,4)},B=function(t){return A(t,52,8)},q=function(t,e){y(t.prototype,e,{get:function(){return S(this)[e]}})},U=function(t,e,n,i){var r=d(n),o=S(t);if(r+e>o.byteLength)throw I("Wrong index");var s=S(o.buffer).bytes,a=r+o.byteOffset,u=s.slice(a,a+e);return i?u:u.reverse()},F=function(t,e,n,i,r,o){var s=d(n),a=S(t);if(s+e>a.byteLength)throw I("Wrong index");for(var u=S(a.buffer).bytes,c=s+a.byteOffset,l=i(+r),h=0;h<e;h++)u[c+h]=l[o?h:e-h-1]};if(o){var W=k&&"ArrayBuffer"!==T.name;if(c((function(){T(1)}))&&c((function(){new T(-1)}))&&!c((function(){return new T,new T(1.5),new T(NaN),W&&!E})))W&&E&&a(T,"name","ArrayBuffer");else{for(var H,V=(_=function(t){return l(this,_),new T(d(t))}).prototype=T.prototype,X=m(T),G=0;X.length>G;)(H=X[G++])in _||a(_,H,T[H]);V.constructor=_}g&&v(C)!==R&&g(C,R);var Y=new N(new _(2)),$=C.setInt8;Y.setInt8(0,2147483648),Y.setInt8(1,2147483649),!Y.getInt8(0)&&Y.getInt8(1)||u(C,{setInt8:function(t,e){$.call(this,t,e<<24>>24)},setUint8:function(t,e){$.call(this,t,e<<24>>24)}},{unsafe:!0})}else _=function(t){l(this,_,"ArrayBuffer");var e=d(t);O(this,{bytes:b.call(new Array(e),0),byteLength:e}),r||(this.byteLength=e)},N=function(t,e,n){l(this,N,"DataView"),l(t,_,"DataView");var i=S(t).byteLength,o=h(e);if(o<0||o>i)throw I("Wrong offset");if(o+(n=void 0===n?i-o:f(n))>i)throw I("Wrong length");O(this,{buffer:t,byteLength:n,byteOffset:o}),r||(this.buffer=t,this.byteLength=n,this.byteOffset=o)},r&&(q(_,"byteLength"),q(N,"buffer"),q(N,"byteLength"),q(N,"byteOffset")),u(N.prototype,{getInt8:function(t){return U(this,1,t)[0]<<24>>24},getUint8:function(t){return U(this,1,t)[0]},getInt16:function(t){var e=U(this,2,t,arguments.length>1?arguments[1]:void 0);return(e[1]<<8|e[0])<<16>>16},getUint16:function(t){var e=U(this,2,t,arguments.length>1?arguments[1]:void 0);return e[1]<<8|e[0]},getInt32:function(t){return M(U(this,4,t,arguments.length>1?arguments[1]:void 0))},getUint32:function(t){return M(U(this,4,t,arguments.length>1?arguments[1]:void 0))>>>0},getFloat32:function(t){return j(U(this,4,t,arguments.length>1?arguments[1]:void 0),23)},getFloat64:function(t){return j(U(this,8,t,arguments.length>1?arguments[1]:void 0),52)},setInt8:function(t,e){F(this,1,t,L,e)},setUint8:function(t,e){F(this,1,t,L,e)},setInt16:function(t,e){F(this,2,t,P,e,arguments.length>2?arguments[2]:void 0)},setUint16:function(t,e){F(this,2,t,P,e,arguments.length>2?arguments[2]:void 0)},setInt32:function(t,e){F(this,4,t,D,e,arguments.length>2?arguments[2]:void 0)},setUint32:function(t,e){F(this,4,t,D,e,arguments.length>2?arguments[2]:void 0)},setFloat32:function(t,e){F(this,4,t,z,e,arguments.length>2?arguments[2]:void 0)},setFloat64:function(t,e){F(this,8,t,B,e,arguments.length>2?arguments[2]:void 0)}});w(_,"ArrayBuffer"),w(N,"DataView"),t.exports={ArrayBuffer:_,DataView:N}},function(t,e){t.exports="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof DataView},function(t,e,n){var i=n(41),r=n(52);t.exports=function(t){if(void 0===t)return 0;var e=i(t),n=r(e);if(e!==n)throw RangeError("Wrong length or index");return n}},function(t,e){var n=Math.abs,i=Math.pow,r=Math.floor,o=Math.log,s=Math.LN2;t.exports={pack:function(t,e,a){var u,c,l,h=new Array(a),f=8*a-e-1,d=(1<<f)-1,p=d>>1,v=23===e?i(2,-24)-i(2,-77):0,g=t<0||0===t&&1/t<0?1:0,m=0;for((t=n(t))!=t||t===1/0?(c=t!=t?1:0,u=d):(u=r(o(t)/s),t*(l=i(2,-u))<1&&(u--,l*=2),(t+=u+p>=1?v/l:v*i(2,1-p))*l>=2&&(u++,l/=2),u+p>=d?(c=0,u=d):u+p>=1?(c=(t*l-1)*i(2,e),u+=p):(c=t*i(2,p-1)*i(2,e),u=0));e>=8;h[m++]=255&c,c/=256,e-=8);for(u=u<<e|c,f+=e;f>0;h[m++]=255&u,u/=256,f-=8);return h[--m]|=128*g,h},unpack:function(t,e){var n,r=t.length,o=8*r-e-1,s=(1<<o)-1,a=s>>1,u=o-7,c=r-1,l=t[c--],h=127&l;for(l>>=7;u>0;h=256*h+t[c],c--,u-=8);for(n=h&(1<<-u)-1,h>>=-u,u+=e;u>0;n=256*n+t[c],c--,u-=8);if(0===h)h=1-a;else{if(h===s)return n?NaN:l?-1/0:1/0;n+=i(2,e),h-=a}return(l?-1:1)*n*i(2,h-e)}}},function(t,e,n){var i=n(7);t.exports=function(t){if("object"==typeof t||i(t))return t;throw TypeError("Can't set "+String(t)+" as a prototype")}},function(t,e,n){"use strict";var i=n(110),r=n(59);t.exports=i?{}.toString:function(){return"[object "+r(this)+"]"}},function(t,e,n){var i=n(4);t.exports=i.Promise},function(t,e,n){var i=n(8),r=n(140),o=n(36),s=n(61),a=n(111),u=n(88),c=n(141),l=function(t,e){this.stopped=t,this.result=e};t.exports=function(t,e,n){var h,f,d,p,v,g,m,y=n&&n.that,b=!(!n||!n.AS_ENTRIES),w=!(!n||!n.IS_ITERATOR),x=!(!n||!n.INTERRUPTED),k=s(e,y,1+b+x),E=function(t){return h&&c(h,"normal",t),new l(!0,t)},S=function(t){return b?(i(t),x?k(t[0],t[1],E):k(t[0],t[1])):x?k(t,E):k(t)};if(w)h=t;else{if(!(f=u(t)))throw TypeError(String(t)+" is not iterable");if(r(f)){for(d=0,p=o(t);p>d;d++)if((v=S(t[d]))&&v instanceof l)return v;return new l(!1)}h=a(t,f)}for(g=h.next;!(m=g.call(h)).done;){try{v=S(m.value)}catch(t){c(h,"throw",t)}if("object"==typeof v&&v&&v instanceof l)return v}return new l(!1)}},function(t,e,n){var i=n(6)("iterator"),r=!1;try{var o=0,s={next:function(){return{done:!!o++}},return:function(){r=!0}};s[i]=function(){return this},Array.from(s,(function(){throw 2}))}catch(t){}t.exports=function(t,e){if(!e&&!r)return!1;var n=!1;try{var o={};o[i]=function(){return{next:function(){return{done:n=!0}}}},t(o)}catch(t){}return n}},function(t,e,n){var i,r,o,s,a,u,c,l,h=n(4),f=n(45).f,d=n(144).set,p=n(145),v=n(185),g=n(186),m=n(112),y=h.MutationObserver||h.WebKitMutationObserver,b=h.document,w=h.process,x=h.Promise,k=f(h,"queueMicrotask"),E=k&&k.value;E||(i=function(){var t,e;for(m&&(t=w.domain)&&t.exit();r;){e=r.fn,r=r.next;try{e()}catch(t){throw r?s():o=void 0,t}}o=void 0,t&&t.enter()},p||m||g||!y||!b?!v&&x&&x.resolve?((c=x.resolve(void 0)).constructor=x,l=c.then,s=function(){l.call(c,i)}):s=m?function(){w.nextTick(i)}:function(){d.call(h,i)}:(a=!0,u=b.createTextNode(""),new y(i).observe(u,{characterData:!0}),s=function(){u.data=a=!a})),t.exports=E||function(t){var e={fn:t,next:void 0};o&&(o.next=e),r||(r=e,s()),o=e}},function(t,e,n){var i=n(77),r=n(4);t.exports=/ipad|iphone|ipod/i.test(i)&&void 0!==r.Pebble},function(t,e,n){var i=n(77);t.exports=/web0s(?!.*chrome)/i.test(i)},function(t,e,n){var i=n(8),r=n(9),o=n(146);t.exports=function(t,e){if(i(t),r(e)&&e.constructor===t)return e;var n=o.f(t);return(0,n.resolve)(e),n.promise}},function(t,e,n){var i=n(4);t.exports=function(t,e){var n=i.console;n&&n.error&&(1===arguments.length?n.error(t):n.error(t,e))}},function(t,e){t.exports=function(t){try{return{error:!1,value:t()}}catch(t){return{error:!0,value:t}}}},function(t,e){t.exports="object"==typeof window},function(t,e,n){var i=n(24),r=Math.floor,o="".replace,s=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,a=/\$([$&'`]|\d{1,2})/g;t.exports=function(t,e,n,u,c,l){var h=n+t.length,f=u.length,d=a;return void 0!==c&&(c=i(c),d=s),o.call(l,d,(function(i,o){var s;switch(o.charAt(0)){case"$":return"$";case"&":return t;case"`":return e.slice(0,n);case"'":return e.slice(h);case"<":s=c[o.slice(1,-1)];break;default:var a=+o;if(0===a)return i;if(a>f){var l=r(a/10);return 0===l?i:l<=f?void 0===u[l-1]?o.charAt(1):u[l-1]+o.charAt(1):i}s=u[a-1]}return void 0===s?"":s}))}},function(t,e,n){var i=n(93),r=n(89),o=n(9),s=n(6)("species");t.exports=function(t){var e;return i(t)&&(e=t.constructor,(r(e)&&(e===Array||i(e.prototype))||o(e)&&null===(e=e[s]))&&(e=void 0)),void 0===e?Array:e}},function(t,e,n){"use strict";var i=n(194),r=n(202),o=n(203),s=n(204);(t.exports=function(t,e){var n,o,a,u,c;return arguments.length<2||"string"!=typeof t?(u=e,e=t,t=null):u=arguments[2],null==t?(n=a=!0,o=!1):(n=s.call(t,"c"),o=s.call(t,"e"),a=s.call(t,"w")),c={value:e,configurable:n,enumerable:o,writable:a},u?i(r(u),c):c}).gs=function(t,e,n){var a,u,c,l;return"string"!=typeof t?(c=n,n=e,e=t,t=null):c=arguments[3],null==e?e=void 0:o(e)?null==n?n=void 0:o(n)||(c=n,n=void 0):(c=e,e=n=void 0),null==t?(a=!0,u=!1):(a=s.call(t,"c"),u=s.call(t,"e")),l={get:e,set:n,configurable:a,enumerable:u},c?i(r(c),l):l}},function(t,e,n){"use strict";t.exports=n(195)()?Object.assign:n(196)},function(t,e,n){"use strict";t.exports=function(){var t,e=Object.assign;return"function"==typeof e&&(e(t={foo:"raz"},{bar:"dwa"},{trzy:"trzy"}),t.foo+t.bar+t.trzy==="razdwatrzy")}},function(t,e,n){"use strict";var i=n(197),r=n(201),o=Math.max;t.exports=function(t,e){var n,s,a,u=o(arguments.length,2);for(t=Object(r(t)),a=function(i){try{t[i]=e[i]}catch(t){n||(n=t)}},s=1;s<u;++s)i(e=arguments[s]).forEach(a);if(void 0!==n)throw n;return t}},function(t,e,n){"use strict";t.exports=n(198)()?Object.keys:n(199)},function(t,e,n){"use strict";t.exports=function(){try{return Object.keys("primitive"),!0}catch(t){return!1}}},function(t,e,n){"use strict";var i=n(118),r=Object.keys;t.exports=function(t){return r(i(t)?Object(t):t)}},function(t,e,n){"use strict";t.exports=function(){}},function(t,e,n){"use strict";var i=n(118);t.exports=function(t){if(!i(t))throw new TypeError("Cannot use null or undefined");return t}},function(t,e,n){"use strict";var i=n(118),r=Array.prototype.forEach,o=Object.create,s=function(t,e){var n;for(n in t)e[n]=t[n]};t.exports=function(t){var e=o(null);return r.call(arguments,(function(t){i(t)&&s(Object(t),e)})),e}},function(t,e,n){"use strict";t.exports=function(t){return"function"==typeof t}},function(t,e,n){"use strict";t.exports=n(205)()?String.prototype.contains:n(206)},function(t,e,n){"use strict";var i="razdwatrzy";t.exports=function(){return"function"==typeof i.contains&&(!0===i.contains("dwa")&&!1===i.contains("foo"))}},function(t,e,n){"use strict";var i=String.prototype.indexOf;t.exports=function(t){return i.call(this,t,arguments[1])>-1}},function(t,e,n){"use strict";t.exports=function(t){if("function"!=typeof t)throw new TypeError(t+" is not a function");return t}},function(t,e,n){"use strict";var i=n(61),r=n(24),o=n(209),s=n(140),a=n(89),u=n(36),c=n(94),l=n(111),h=n(88);t.exports=function(t){var e=r(t),n=a(this),f=arguments.length,d=f>1?arguments[1]:void 0,p=void 0!==d;p&&(d=i(d,f>2?arguments[2]:void 0,2));var v,g,m,y,b,w,x=h(e),k=0;if(!x||this==Array&&s(x))for(v=u(e),g=n?new this(v):Array(v);v>k;k++)w=p?d(e[k],k):e[k],c(g,k,w);else for(b=(y=l(e,x)).next,g=n?new this:[];!(m=b.call(y)).done;k++)w=p?o(y,d,[m.value,k],!0):m.value,c(g,k,w);return g.length=k,g}},function(t,e,n){var i=n(8),r=n(141);t.exports=function(t,e,n,o){try{return o?e(i(n)[0],n[1]):e(n)}catch(e){r(t,"throw",e)}}},function(t,e,n){"use strict";var i=/[^\0-\u007E]/,r=/[.\u3002\uFF0E\uFF61]/g,o="Overflow: input needs wider integers to process",s=Math.floor,a=String.fromCharCode,u=function(t){return t+22+75*(t<26)},c=function(t,e,n){var i=0;for(t=n?s(t/700):t>>1,t+=s(t/e);t>455;i+=36)t=s(t/35);return s(i+36*t/(t+38))},l=function(t){var e,n,i=[],r=(t=function(t){for(var e=[],n=0,i=t.length;n<i;){var r=t.charCodeAt(n++);if(r>=55296&&r<=56319&&n<i){var o=t.charCodeAt(n++);56320==(64512&o)?e.push(((1023&r)<<10)+(1023&o)+65536):(e.push(r),n--)}else e.push(r)}return e}(t)).length,l=128,h=0,f=72;for(e=0;e<t.length;e++)(n=t[e])<128&&i.push(a(n));var d=i.length,p=d;for(d&&i.push("-");p<r;){var v=2147483647;for(e=0;e<t.length;e++)(n=t[e])>=l&&n<v&&(v=n);var g=p+1;if(v-l>s((2147483647-h)/g))throw RangeError(o);for(h+=(v-l)*g,l=v,e=0;e<t.length;e++){if((n=t[e])<l&&++h>2147483647)throw RangeError(o);if(n==l){for(var m=h,y=36;;y+=36){var b=y<=f?1:y>=f+26?26:y-f;if(m<b)break;var w=m-b,x=36-b;i.push(a(u(b+w%x))),m=s(w/x)}i.push(a(u(m))),f=c(h,g,p==d),h=0,++p}}++h,++l}return i.join("")};t.exports=function(t){var e,n,o=[],s=t.toLowerCase().replace(r,".").split(".");for(e=0;e<s.length;e++)n=s[e],o.push(i.test(n)?"xn--"+l(n):n);return o.join(".")}},function(t,e,n){"use strict";var i=n(65).forEach,r=n(153)("forEach");t.exports=r?[].forEach:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0)}},function(t,e,n){var i=n(5),r=n(3),o=n(119).f;i({target:"Object",stat:!0,forced:r((function(){return!Object.getOwnPropertyNames(1)}))},{getOwnPropertyNames:o})},function(t,e,n){var i=n(5),r=n(155),o=n(3),s=n(9),a=n(214).onFreeze,u=Object.freeze;i({target:"Object",stat:!0,forced:o((function(){u(1)})),sham:!r},{freeze:function(t){return u&&s(t)?u(a(t)):t}})},function(t,e,n){var i=n(5),r=n(57),o=n(9),s=n(13),a=n(20).f,u=n(51),c=n(119),l=n(79),h=n(155),f=!1,d=l("meta"),p=0,v=Object.isExtensible||function(){return!0},g=function(t){a(t,d,{value:{objectID:"O"+p++,weakData:{}}})},m=t.exports={enable:function(){m.enable=function(){},f=!0;var t=u.f,e=[].splice,n={};n[d]=1,t(n).length&&(u.f=function(n){for(var i=t(n),r=0,o=i.length;r<o;r++)if(i[r]===d){e.call(i,r,1);break}return i},i({target:"Object",stat:!0,forced:!0},{getOwnPropertyNames:c.f}))},fastKey:function(t,e){if(!o(t))return"symbol"==typeof t?t:("string"==typeof t?"S":"P")+t;if(!s(t,d)){if(!v(t))return"F";if(!e)return"E";g(t)}return t[d].objectID},getWeakData:function(t,e){if(!s(t,d)){if(!v(t))return!0;if(!e)return!1;g(t)}return t[d].weakData},onFreeze:function(t){return h&&f&&v(t)&&!s(t,d)&&g(t),t}};r[d]=!0},function(t,e,n){var i=n(96),r=n(156),o=n(216),s=n(217),a=r.DOMImplementation,u=i.NAMESPACE,c=s.ParseError,l=s.XMLReader;function h(t){this.options=t||{locator:{}}}function f(){this.cdata=!1}function d(t,e){e.lineNumber=t.lineNumber,e.columnNumber=t.columnNumber}function p(t){if(t)return"\n@"+(t.systemId||"")+"#[line:"+t.lineNumber+",col:"+t.columnNumber+"]"}function v(t,e,n){return"string"==typeof t?t.substr(e,n):t.length>=e+n||e?new java.lang.String(t,e,n)+"":t}function g(t,e){t.currentElement?t.currentElement.appendChild(e):t.doc.appendChild(e)}h.prototype.parseFromString=function(t,e){var n=this.options,i=new l,r=n.domBuilder||new f,s=n.errorHandler,a=n.locator,c=n.xmlns||{},h=/\/x?html?$/.test(e),d=h?o.HTML_ENTITIES:o.XML_ENTITIES;return a&&r.setDocumentLocator(a),i.errorHandler=function(t,e,n){if(!t){if(e instanceof f)return e;t=e}var i={},r=t instanceof Function;function o(e){var o=t[e];!o&&r&&(o=2==t.length?function(n){t(e,n)}:t),i[e]=o&&function(t){o("[xmldom "+e+"]\t"+t+p(n))}||function(){}}return n=n||{},o("warning"),o("error"),o("fatalError"),i}(s,r,a),i.domBuilder=n.domBuilder||r,h&&(c[""]=u.HTML),c.xml=c.xml||u.XML,t&&"string"==typeof t?i.parse(t,c,d):i.errorHandler.error("invalid doc source"),r.doc},f.prototype={startDocument:function(){this.doc=(new a).createDocument(null,null,null),this.locator&&(this.doc.documentURI=this.locator.systemId)},startElement:function(t,e,n,i){var r=this.doc,o=r.createElementNS(t,n||e),s=i.length;g(this,o),this.currentElement=o,this.locator&&d(this.locator,o);for(var a=0;a<s;a++){t=i.getURI(a);var u=i.getValue(a),c=(n=i.getQName(a),r.createAttributeNS(t,n));this.locator&&d(i.getLocator(a),c),c.value=c.nodeValue=u,o.setAttributeNode(c)}},endElement:function(t,e,n){var i=this.currentElement;i.tagName;this.currentElement=i.parentNode},startPrefixMapping:function(t,e){},endPrefixMapping:function(t){},processingInstruction:function(t,e){var n=this.doc.createProcessingInstruction(t,e);this.locator&&d(this.locator,n),g(this,n)},ignorableWhitespace:function(t,e,n){},characters:function(t,e,n){if(t=v.apply(this,arguments)){if(this.cdata)var i=this.doc.createCDATASection(t);else i=this.doc.createTextNode(t);this.currentElement?this.currentElement.appendChild(i):/^\s*$/.test(t)&&this.doc.appendChild(i),this.locator&&d(this.locator,i)}},skippedEntity:function(t){},endDocument:function(){this.doc.normalize()},setDocumentLocator:function(t){(this.locator=t)&&(t.lineNumber=0)},comment:function(t,e,n){t=v.apply(this,arguments);var i=this.doc.createComment(t);this.locator&&d(this.locator,i),g(this,i)},startCDATA:function(){this.cdata=!0},endCDATA:function(){this.cdata=!1},startDTD:function(t,e,n){var i=this.doc.implementation;if(i&&i.createDocumentType){var r=i.createDocumentType(t,e,n);this.locator&&d(this.locator,r),g(this,r),this.doc.doctype=r}},warning:function(t){console.warn("[xmldom warning]\t"+t,p(this.locator))},error:function(t){console.error("[xmldom error]\t"+t,p(this.locator))},fatalError:function(t){throw new c(t,this.locator)}},"endDTD,startEntity,endEntity,attributeDecl,elementDecl,externalEntityDecl,internalEntityDecl,resolveEntity,getExternalSubset,notationDecl,unparsedEntityDecl".replace(/\w+/g,(function(t){f.prototype[t]=function(){return null}})),e.__DOMHandler=f,e.DOMParser=h,e.DOMImplementation=r.DOMImplementation,e.XMLSerializer=r.XMLSerializer},function(t,e,n){var i=n(96).freeze;e.XML_ENTITIES=i({amp:"&",apos:"'",gt:">",lt:"<",quot:'"'}),e.HTML_ENTITIES=i({lt:"<",gt:">",amp:"&",quot:'"',apos:"'",Agrave:"À",Aacute:"Á",Acirc:"Â",Atilde:"Ã",Auml:"Ä",Aring:"Å",AElig:"Æ",Ccedil:"Ç",Egrave:"È",Eacute:"É",Ecirc:"Ê",Euml:"Ë",Igrave:"Ì",Iacute:"Í",Icirc:"Î",Iuml:"Ï",ETH:"Ð",Ntilde:"Ñ",Ograve:"Ò",Oacute:"Ó",Ocirc:"Ô",Otilde:"Õ",Ouml:"Ö",Oslash:"Ø",Ugrave:"Ù",Uacute:"Ú",Ucirc:"Û",Uuml:"Ü",Yacute:"Ý",THORN:"Þ",szlig:"ß",agrave:"à",aacute:"á",acirc:"â",atilde:"ã",auml:"ä",aring:"å",aelig:"æ",ccedil:"ç",egrave:"è",eacute:"é",ecirc:"ê",euml:"ë",igrave:"ì",iacute:"í",icirc:"î",iuml:"ï",eth:"ð",ntilde:"ñ",ograve:"ò",oacute:"ó",ocirc:"ô",otilde:"õ",ouml:"ö",oslash:"ø",ugrave:"ù",uacute:"ú",ucirc:"û",uuml:"ü",yacute:"ý",thorn:"þ",yuml:"ÿ",nbsp:" ",iexcl:"¡",cent:"¢",pound:"£",curren:"¤",yen:"¥",brvbar:"¦",sect:"§",uml:"¨",copy:"©",ordf:"ª",laquo:"«",not:"¬",shy:"­­",reg:"®",macr:"¯",deg:"°",plusmn:"±",sup2:"²",sup3:"³",acute:"´",micro:"µ",para:"¶",middot:"·",cedil:"¸",sup1:"¹",ordm:"º",raquo:"»",frac14:"¼",frac12:"½",frac34:"¾",iquest:"¿",times:"×",divide:"÷",forall:"∀",part:"∂",exist:"∃",empty:"∅",nabla:"∇",isin:"∈",notin:"∉",ni:"∋",prod:"∏",sum:"∑",minus:"−",lowast:"∗",radic:"√",prop:"∝",infin:"∞",ang:"∠",and:"∧",or:"∨",cap:"∩",cup:"∪",int:"∫",there4:"∴",sim:"∼",cong:"≅",asymp:"≈",ne:"≠",equiv:"≡",le:"≤",ge:"≥",sub:"⊂",sup:"⊃",nsub:"⊄",sube:"⊆",supe:"⊇",oplus:"⊕",otimes:"⊗",perp:"⊥",sdot:"⋅",Alpha:"Α",Beta:"Β",Gamma:"Γ",Delta:"Δ",Epsilon:"Ε",Zeta:"Ζ",Eta:"Η",Theta:"Θ",Iota:"Ι",Kappa:"Κ",Lambda:"Λ",Mu:"Μ",Nu:"Ν",Xi:"Ξ",Omicron:"Ο",Pi:"Π",Rho:"Ρ",Sigma:"Σ",Tau:"Τ",Upsilon:"Υ",Phi:"Φ",Chi:"Χ",Psi:"Ψ",Omega:"Ω",alpha:"α",beta:"β",gamma:"γ",delta:"δ",epsilon:"ε",zeta:"ζ",eta:"η",theta:"θ",iota:"ι",kappa:"κ",lambda:"λ",mu:"μ",nu:"ν",xi:"ξ",omicron:"ο",pi:"π",rho:"ρ",sigmaf:"ς",sigma:"σ",tau:"τ",upsilon:"υ",phi:"φ",chi:"χ",psi:"ψ",omega:"ω",thetasym:"ϑ",upsih:"ϒ",piv:"ϖ",OElig:"Œ",oelig:"œ",Scaron:"Š",scaron:"š",Yuml:"Ÿ",fnof:"ƒ",circ:"ˆ",tilde:"˜",ensp:" ",emsp:" ",thinsp:" ",zwnj:"‌",zwj:"‍",lrm:"‎",rlm:"‏",ndash:"–",mdash:"—",lsquo:"‘",rsquo:"’",sbquo:"‚",ldquo:"“",rdquo:"”",bdquo:"„",dagger:"†",Dagger:"‡",bull:"•",hellip:"…",permil:"‰",prime:"′",Prime:"″",lsaquo:"‹",rsaquo:"›",oline:"‾",euro:"€",trade:"™",larr:"←",uarr:"↑",rarr:"→",darr:"↓",harr:"↔",crarr:"↵",lceil:"⌈",rceil:"⌉",lfloor:"⌊",rfloor:"⌋",loz:"◊",spades:"♠",clubs:"♣",hearts:"♥",diams:"♦"}),e.entityMap=e.HTML_ENTITIES},function(t,e,n){var i=n(96).NAMESPACE,r=/[A-Z_a-z\xC0-\xD6\xD8-\xF6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/,o=new RegExp("[\\-\\.0-9"+r.source.slice(1,-1)+"\\u00B7\\u0300-\\u036F\\u203F-\\u2040]"),s=new RegExp("^"+r.source+o.source+"*(?::"+r.source+o.source+"*)?$");function a(t,e){this.message=t,this.locator=e,Error.captureStackTrace&&Error.captureStackTrace(this,a)}function u(){}function c(t,e){return e.lineNumber=t.lineNumber,e.columnNumber=t.columnNumber,e}function l(t,e,n,r,o,s){function a(t,e,i){n.attributeNames.hasOwnProperty(t)&&s.fatalError("Attribute "+t+" redefined"),n.addValue(t,e,i)}for(var u,c=++e,l=0;;){var h=t.charAt(c);switch(h){case"=":if(1===l)u=t.slice(e,c),l=3;else{if(2!==l)throw new Error("attribute equal must after attrName");l=3}break;case"'":case'"':if(3===l||1===l){if(1===l&&(s.warning('attribute value must after "="'),u=t.slice(e,c)),e=c+1,!((c=t.indexOf(h,e))>0))throw new Error("attribute value no end '"+h+"' match");a(u,f=t.slice(e,c).replace(/&#?\w+;/g,o),e-1),l=5}else{if(4!=l)throw new Error('attribute value must after "="');a(u,f=t.slice(e,c).replace(/&#?\w+;/g,o),e),s.warning('attribute "'+u+'" missed start quot('+h+")!!"),e=c+1,l=5}break;case"/":switch(l){case 0:n.setTagName(t.slice(e,c));case 5:case 6:case 7:l=7,n.closed=!0;case 4:case 1:case 2:break;default:throw new Error("attribute invalid close char('/')")}break;case"":return s.error("unexpected end of input"),0==l&&n.setTagName(t.slice(e,c)),c;case">":switch(l){case 0:n.setTagName(t.slice(e,c));case 5:case 6:case 7:break;case 4:case 1:"/"===(f=t.slice(e,c)).slice(-1)&&(n.closed=!0,f=f.slice(0,-1));case 2:2===l&&(f=u),4==l?(s.warning('attribute "'+f+'" missed quot(")!'),a(u,f.replace(/&#?\w+;/g,o),e)):(i.isHTML(r[""])&&f.match(/^(?:disabled|checked|selected)$/i)||s.warning('attribute "'+f+'" missed value!! "'+f+'" instead!!'),a(f,f,e));break;case 3:throw new Error("attribute value missed!!")}return c;case"":h=" ";default:if(h<=" ")switch(l){case 0:n.setTagName(t.slice(e,c)),l=6;break;case 1:u=t.slice(e,c),l=2;break;case 4:var f=t.slice(e,c).replace(/&#?\w+;/g,o);s.warning('attribute "'+f+'" missed quot(")!!'),a(u,f,e);case 5:l=6}else switch(l){case 2:n.tagName;i.isHTML(r[""])&&u.match(/^(?:disabled|checked|selected)$/i)||s.warning('attribute "'+u+'" missed value!! "'+u+'" instead2!!'),a(u,u,e),e=c,l=1;break;case 5:s.warning('attribute space is required"'+u+'"!!');case 6:l=1,e=c;break;case 3:l=4,e=c;break;case 7:throw new Error("elements closed character '/' and '>' must be connected to")}}c++}}function h(t,e,n){for(var r=t.tagName,o=null,s=t.length;s--;){var a=t[s],u=a.qName,c=a.value;if((d=u.indexOf(":"))>0)var l=a.prefix=u.slice(0,d),h=u.slice(d+1),f="xmlns"===l&&h;else h=u,l=null,f="xmlns"===u&&"";a.localName=h,!1!==f&&(null==o&&(o={},p(n,n={})),n[f]=o[f]=c,a.uri=i.XMLNS,e.startPrefixMapping(f,c))}for(s=t.length;s--;){(l=(a=t[s]).prefix)&&("xml"===l&&(a.uri=i.XML),"xmlns"!==l&&(a.uri=n[l||""]))}var d;(d=r.indexOf(":"))>0?(l=t.prefix=r.slice(0,d),h=t.localName=r.slice(d+1)):(l=null,h=t.localName=r);var v=t.uri=n[l||""];if(e.startElement(v,h,r,t),!t.closed)return t.currentNSMap=n,t.localNSMap=o,!0;if(e.endElement(v,h,r),o)for(l in o)e.endPrefixMapping(l)}function f(t,e,n,i,r){if(/^(?:script|textarea)$/i.test(n)){var o=t.indexOf("</"+n+">",e),s=t.substring(e+1,o);if(/[&<]/.test(s))return/^script$/i.test(n)?(r.characters(s,0,s.length),o):(s=s.replace(/&#?\w+;/g,i),r.characters(s,0,s.length),o)}return e+1}function d(t,e,n,i){var r=i[n];return null==r&&((r=t.lastIndexOf("</"+n+">"))<e&&(r=t.lastIndexOf("</"+n)),i[n]=r),r<e}function p(t,e){for(var n in t)e[n]=t[n]}function v(t,e,n,i){switch(t.charAt(e+2)){case"-":return"-"===t.charAt(e+3)?(r=t.indexOf("--\x3e",e+4))>e?(n.comment(t,e+4,r-e-4),r+3):(i.error("Unclosed comment"),-1):-1;default:if("CDATA["==t.substr(e+3,6)){var r=t.indexOf("]]>",e+9);return n.startCDATA(),n.characters(t,e+9,r-e-9),n.endCDATA(),r+3}var o=function(t,e){var n,i=[],r=/'[^']+'|"[^"]+"|[^\s<>\/=]+=?|(\/?\s*>|<)/g;r.lastIndex=e,r.exec(t);for(;n=r.exec(t);)if(i.push(n),n[1])return i}(t,e),s=o.length;if(s>1&&/!doctype/i.test(o[0][0])){var a=o[1][0],u=!1,c=!1;s>3&&(/^public$/i.test(o[2][0])?(u=o[3][0],c=s>4&&o[4][0]):/^system$/i.test(o[2][0])&&(c=o[3][0]));var l=o[s-1];return n.startDTD(a,u,c),n.endDTD(),l.index+l[0].length}}return-1}function g(t,e,n){var i=t.indexOf("?>",e);if(i){var r=t.substring(e,i).match(/^<\?(\S*)\s*([\s\S]*?)\s*$/);if(r){r[0].length;return n.processingInstruction(r[1],r[2]),i+2}return-1}return-1}function m(){this.attributeNames={}}a.prototype=new Error,a.prototype.name=a.name,u.prototype={parse:function(t,e,n){var r=this.domBuilder;r.startDocument(),p(e,e={}),function(t,e,n,r,o){function s(t){var e=t.slice(1,-1);return e in n?n[e]:"#"===e.charAt(0)?function(t){if(t>65535){var e=55296+((t-=65536)>>10),n=56320+(1023&t);return String.fromCharCode(e,n)}return String.fromCharCode(t)}(parseInt(e.substr(1).replace("x","0x"))):(o.error("entity not found:"+t),t)}function u(e){if(e>S){var n=t.substring(S,e).replace(/&#?\w+;/g,s);x&&p(S),r.characters(n,0,e-S),S=e}}function p(e,n){for(;e>=b&&(n=w.exec(t));)y=n.index,b=y+n[0].length,x.lineNumber++;x.columnNumber=e-y+1}var y=0,b=0,w=/.*(?:\r\n?|\n)|.*$/g,x=r.locator,k=[{currentNSMap:e}],E={},S=0;for(;;){try{var O=t.indexOf("<",S);if(O<0){if(!t.substr(S).match(/^\s*$/)){var T=r.doc,_=T.createTextNode(t.substr(S));T.appendChild(_),r.currentElement=_}return}switch(O>S&&u(O),t.charAt(O+1)){case"/":var N=t.indexOf(">",O+3),C=t.substring(O+2,N).replace(/[ \t\n\r]+$/g,""),R=k.pop();N<0?(C=t.substring(O+2).replace(/[\s<].*/,""),o.error("end tag name: "+C+" is not complete:"+R.tagName),N=O+1+C.length):C.match(/\s</)&&(C=C.replace(/[\s<].*/,""),o.error("end tag name: "+C+" maybe not complete"),N=O+1+C.length);var I=R.localNSMap,A=R.tagName==C;if(A||R.tagName&&R.tagName.toLowerCase()==C.toLowerCase()){if(r.endElement(R.uri,R.localName,C),I)for(var j in I)r.endPrefixMapping(j);A||o.fatalError("end tag name: "+C+" is not match the current start tagName:"+R.tagName)}else k.push(R);N++;break;case"?":x&&p(O),N=g(t,O,r);break;case"!":x&&p(O),N=v(t,O,r,o);break;default:x&&p(O);var L=new m,P=k[k.length-1].currentNSMap,D=(N=l(t,O,L,P,s,o),L.length);if(!L.closed&&d(t,N,L.tagName,E)&&(L.closed=!0,n.nbsp||o.warning("unclosed xml attribute")),x&&D){for(var M=c(x,{}),z=0;z<D;z++){var B=L[z];p(B.offset),B.locator=c(x,{})}r.locator=M,h(L,r,P)&&k.push(L),r.locator=x}else h(L,r,P)&&k.push(L);i.isHTML(L.uri)&&!L.closed?N=f(t,N,L.tagName,s,r):N++}}catch(t){if(t instanceof a)throw t;o.error("element parse error: "+t),N=-1}N>S?S=N:u(Math.max(O,S)+1)}}(t,e,n,r,this.errorHandler),r.endDocument()}},m.prototype={setTagName:function(t){if(!s.test(t))throw new Error("invalid tagName:"+t);this.tagName=t},addValue:function(t,e,n){if(!s.test(t))throw new Error("invalid attribute:"+t);this.attributeNames[t]=this.length,this[this.length++]={qName:t,value:e,offset:n}},length:0,getLocalName:function(t){return this[t].localName},getLocator:function(t){return this[t].locator},getQName:function(t){return this[t].qName},getURI:function(t){return this[t].uri},getValue:function(t){return this[t].value}},e.XMLReader=u,e.ParseError=a},function(t,e,n){"use strict";var i=n(91),r=n(8),o=n(32),s=n(219),a=n(21),u=n(40),c=n(92);i("search",(function(t,e,n){return[function(e){var n=o(this),i=null==e?void 0:u(e,t);return i?i.call(e,n):new RegExp(e)[t](a(n))},function(t){var i=r(this),o=a(t),u=n(e,i,o);if(u.done)return u.value;var l=i.lastIndex;s(l,0)||(i.lastIndex=0);var h=c(i,o);return s(i.lastIndex,l)||(i.lastIndex=l),null===h?-1:h.index}]}))},function(t,e){t.exports=Object.is||function(t,e){return t===e?0!==t||1/t==1/e:t!=t&&e!=e}},function(t,e,n){var i=n(4);t.exports=i},function(t,e,n){var i=n(17),r=n(4),o=n(107),s=n(222),a=n(33),u=n(20).f,c=n(51).f,l=n(120),h=n(21),f=n(114),d=n(115),p=n(25),v=n(3),g=n(13),m=n(30).enforce,y=n(109),b=n(6),w=n(149),x=n(150),k=b("match"),E=r.RegExp,S=E.prototype,O=/^\?<[^\s\d!#%&*+<=>@^][^\s!#%&*+<=>@^]*>/,T=/a/g,_=/a/g,N=new E(T)!==T,C=d.UNSUPPORTED_Y,R=i&&(!N||C||w||x||v((function(){return _[k]=!1,E(T)!=T||E(_)==_||"/a/i"!=E(T,"i")})));if(o("RegExp",R)){for(var I=function(t,e){var n,i,r,o,u,c,d=this instanceof I,p=l(t),v=void 0===e,y=[],b=t;if(!d&&p&&v&&t.constructor===I)return t;if((p||t instanceof I)&&(t=t.source,v&&(e="flags"in b?b.flags:f.call(b))),t=void 0===t?"":h(t),e=void 0===e?"":h(e),b=t,w&&"dotAll"in T&&(i=!!e&&e.indexOf("s")>-1)&&(e=e.replace(/s/g,"")),n=e,C&&"sticky"in T&&(r=!!e&&e.indexOf("y")>-1)&&(e=e.replace(/y/g,"")),x&&(t=(o=function(t){for(var e,n=t.length,i=0,r="",o=[],s={},a=!1,u=!1,c=0,l="";i<=n;i++){if("\\"===(e=t.charAt(i)))e+=t.charAt(++i);else if("]"===e)a=!1;else if(!a)switch(!0){case"["===e:a=!0;break;case"("===e:O.test(t.slice(i+1))&&(i+=2,u=!0),r+=e,c++;continue;case">"===e&&u:if(""===l||g(s,l))throw new SyntaxError("Invalid capture group name");s[l]=!0,o.push([l,c]),u=!1,l="";continue}u?l+=e:r+=e}return[r,o]}(t))[0],y=o[1]),u=s(E(t,e),d?this:S,I),(i||r||y.length)&&(c=m(u),i&&(c.dotAll=!0,c.raw=I(function(t){for(var e,n=t.length,i=0,r="",o=!1;i<=n;i++)"\\"!==(e=t.charAt(i))?o||"."!==e?("["===e?o=!0:"]"===e&&(o=!1),r+=e):r+="[\\s\\S]":r+=e+t.charAt(++i);return r}(t),n)),r&&(c.sticky=!0),y.length&&(c.groups=y)),t!==b)try{a(u,"source",""===b?"(?:)":b)}catch(t){}return u},A=function(t){t in I||u(I,t,{configurable:!0,get:function(){return E[t]},set:function(e){E[t]=e}})},j=c(E),L=0;j.length>L;)A(j[L++]);S.constructor=I,I.prototype=S,p(r,"RegExp",I)}y("RegExp")},function(t,e,n){var i=n(7),r=n(9),o=n(85);t.exports=function(t,e,n){var s,a;return o&&i(s=e.constructor)&&s!==n&&r(a=s.prototype)&&a!==n.prototype&&o(t,a),t}},function(t,e,n){var i=n(32),r=n(21),o="["+n(162)+"]",s=RegExp("^"+o+o+"*"),a=RegExp(o+o+"*$"),u=function(t){return function(e){var n=r(i(e));return 1&t&&(n=n.replace(s,"")),2&t&&(n=n.replace(a,"")),n}};t.exports={start:u(1),end:u(2),trim:u(3)}},function(t,e,n){var i=n(50).PROPER,r=n(3),o=n(162);t.exports=function(t){return r((function(){return!!o[t]()||"​᠎"!=="​᠎"[t]()||i&&o[t].name!==t}))}},function(t,e,n){var i=n(120);t.exports=function(t){if(i(t))throw TypeError("The method doesn't accept regular expressions");return t}},function(t,e,n){var i=n(6)("match");t.exports=function(t){var e=/./;try{"/./"[t](e)}catch(n){try{return e[i]=!1,"/./"[t](e)}catch(t){}}return!1}},function(t,e,n){var i=n(5),r=n(152);i({target:"Object",stat:!0,forced:Object.assign!==r},{assign:r})},function(t,e,n){"use strict";function i(t){return document.createElementNS("http://www.w3.org/2000/svg",t)}Object.defineProperty(e,"__esModule",{value:!0}),e.createElement=i,e.default={createElement:i}},function(t,e,n){"use strict";function i(t,e){function n(n){for(var i=e.length-1;i>=0;i--){var s=e[i],a=n.clientX,u=n.clientY;if(n.touches&&n.touches.length&&(a=n.touches[0].clientX,u=n.touches[0].clientY),o(s,t,a,u)){s.dispatchEvent(r(n));break}}}if("iframe"===t.nodeName||"IFRAME"===t.nodeName)try{this.target=t.contentDocument}catch(e){this.target=t}else this.target=t;for(var i=["mouseup","mousedown","click","touchstart"],s=0;s<i.length;s++){var a=i[s];this.target.addEventListener(a,(function(t){return n(t)}),!1)}}function r(t){var e=Object.assign({},t,{bubbles:!1});try{return new MouseEvent(t.type,e)}catch(i){var n=document.createEvent("MouseEvents");return n.initMouseEvent(t.type,!1,e.cancelable,e.view,e.detail,e.screenX,e.screenY,e.clientX,e.clientY,e.ctrlKey,e.altKey,e.shiftKey,e.metaKey,e.button,e.relatedTarget),n}}function o(t,e,n,i){var r=e.getBoundingClientRect();function o(t,e,n){var i=t.top-r.top,o=t.left-r.left,s=i+t.height,a=o+t.width;return i<=n&&o<=e&&s>n&&a>e}if(!o(t.getBoundingClientRect(),n,i))return!1;for(var s=t.getClientRects(),a=0,u=s.length;a<u;a++)if(o(s[a],n,i))return!0;return!1}Object.defineProperty(e,"__esModule",{value:!0}),e.proxyMouse=i,e.clone=r,e.default={proxyMouse:i}},function(t,e,n){var i=n(165);t.exports=function(){return i.Date.now()}},function(t,e,n){(function(e){var n="object"==typeof e&&e&&e.Object===Object&&e;t.exports=n}).call(this,n(73))},function(t,e,n){var i=n(233),r=n(121),o=n(235),s=/^[-+]0x[0-9a-f]+$/i,a=/^0b[01]+$/i,u=/^0o[0-7]+$/i,c=parseInt;t.exports=function(t){if("number"==typeof t)return t;if(o(t))return NaN;if(r(t)){var e="function"==typeof t.valueOf?t.valueOf():t;t=r(e)?e+"":e}if("string"!=typeof t)return 0===t?t:+t;t=i(t);var n=a.test(t);return n||u.test(t)?c(t.slice(2),n?2:8):s.test(t)?NaN:+t}},function(t,e,n){var i=n(234),r=/^\s+/;t.exports=function(t){return t?t.slice(0,i(t)+1).replace(r,""):t}},function(t,e){var n=/\s/;t.exports=function(t){for(var e=t.length;e--&&n.test(t.charAt(e)););return e}},function(t,e,n){var i=n(236),r=n(239);t.exports=function(t){return"symbol"==typeof t||r(t)&&"[object Symbol]"==i(t)}},function(t,e,n){var i=n(166),r=n(237),o=n(238),s=i?i.toStringTag:void 0;t.exports=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":s&&s in Object(t)?r(t):o(t)}},function(t,e,n){var i=n(166),r=Object.prototype,o=r.hasOwnProperty,s=r.toString,a=i?i.toStringTag:void 0;t.exports=function(t){var e=o.call(t,a),n=t[a];try{t[a]=void 0;var i=!0}catch(t){}var r=s.call(t);return i&&(e?t[a]=n:delete t[a]),r}},function(t,e){var n=Object.prototype.toString;t.exports=function(t){return n.call(t)}},function(t,e){t.exports=function(t){return null!=t&&"object"==typeof t}},function(t,e,n){var i=n(5),r=n(134),o=n(86);i({target:"Array",proto:!0},{fill:r}),o("fill")},function(t,e,n){var i=n(5),r=n(3),o=n(24),s=n(58),a=n(133);i({target:"Object",stat:!0,forced:r((function(){s(1)})),sham:!a},{getPrototypeOf:function(t){return s(o(t))}})},function(t,e,n){var i=n(5),r=n(29),o=n(143),s=n(8),a=n(9),u=n(43),c=n(243),l=n(3),h=r("Reflect","construct"),f=l((function(){function t(){}return!(h((function(){}),[],t)instanceof t)})),d=!l((function(){h((function(){}))})),p=f||d;i({target:"Reflect",stat:!0,forced:p,sham:p},{construct:function(t,e){o(t),s(e);var n=arguments.length<3?t:o(arguments[2]);if(d&&!f)return h(t,e,n);if(t==n){switch(e.length){case 0:return new t;case 1:return new t(e[0]);case 2:return new t(e[0],e[1]);case 3:return new t(e[0],e[1],e[2]);case 4:return new t(e[0],e[1],e[2],e[3])}var i=[null];return i.push.apply(i,e),new(c.apply(t,i))}var r=n.prototype,l=u(a(r)?r:Object.prototype),p=Function.apply.call(t,l,e);return a(p)?p:l}})},function(t,e,n){"use strict";var i=n(48),r=n(9),o=[].slice,s={},a=function(t,e,n){if(!(e in s)){for(var i=[],r=0;r<e;r++)i[r]="a["+r+"]";s[e]=Function("C,a","return new C("+i.join(",")+")")}return s[e](t,n)};t.exports=Function.bind||function(t){var e=i(this),n=o.call(arguments,1),s=function(){var i=n.concat(o.call(arguments));return this instanceof s?a(e,i.length,i):e.apply(t,i)};return r(e.prototype)&&(s.prototype=e.prototype),s}},function(t,e,n){var i=n(5),r=n(9),o=n(8),s=n(245),a=n(45),u=n(58);i({target:"Reflect",stat:!0},{get:function t(e,n){var i,c,l=arguments.length<3?e:arguments[2];return o(e)===l?e[n]:(i=a.f(e,n))?s(i)?i.value:void 0===i.get?void 0:i.get.call(l):r(c=u(e))?t(c,n,l):void 0}})},function(t,e,n){var i=n(13);t.exports=function(t){return void 0!==t&&(i(t,"value")||i(t,"writable"))}}]).default}));