<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<title>EPUB.js Basic Example</title>

	<script src="../dist/epub.js"></script>

	<link rel="stylesheet" type="text/css" href="examples.css">

</head>
<body>
	<select id="toc"></select>
	<div id="viewer" class="scrolled"></div>

	<script>
		var $viewer = document.getElementById("viewer");
		var $next = document.getElementById("next");
		var $prev = document.getElementById("prev");
		var currentSectionIndex = 9;
		// Load the opf
		var book = ePub("https://s3.amazonaws.com/moby-dick/OPS/package.opf");
		var rendition = book.renderTo("viewer", { flow: "scrolled-doc", width: 600, height: 400});
		var displayed = rendition.display(currentSectionIndex);

		book.loaded.navigation.then(function(toc){
			var $select = document.getElementById("toc"),
					docfrag = document.createDocumentFragment();

			toc.forEach(function(chapter) {
				var option = document.createElement("option");
				option.textContent = chapter.label;
				option.ref = chapter.href;

				docfrag.appendChild(option);
			});

			$select.appendChild(docfrag);

			$select.onchange = function(){
					var index = $select.selectedIndex,
							url = $select.options[index].ref;
					rendition.display(url);
					return false;
			};


		});
	</script>

</body>
</html>
