// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		11A307EE2A06E45800E56674 /* ReactNativeBlobUtil.mm in Sources */ = {isa = PBXBuildFile; fileRef = 11A307ED2A06E45800E56674 /* ReactNativeBlobUtil.mm */; };
		11A307F82A06E47700E56674 /* ReactNativeBlobUtilNetwork.mm in Sources */ = {isa = PBXBuildFile; fileRef = 11A307EF2A06E47300E56674 /* ReactNativeBlobUtilNetwork.mm */; };
		11A307F92A06E47700E56674 /* ReactNativeBlobUtilReqBuilder.mm in Sources */ = {isa = PBXBuildFile; fileRef = 11A307F02A06E47400E56674 /* ReactNativeBlobUtilReqBuilder.mm */; };
		11A307FA2A06E47700E56674 /* ReactNativeBlobUtilFS.mm in Sources */ = {isa = PBXBuildFile; fileRef = 11A307F12A06E47400E56674 /* ReactNativeBlobUtilFS.mm */; };
		11A307FB2A06E47700E56674 /* ReactNativeBlobUtilFileTransformer.mm in Sources */ = {isa = PBXBuildFile; fileRef = 11A307F32A06E47400E56674 /* ReactNativeBlobUtilFileTransformer.mm */; };
		11A307FC2A06E47700E56674 /* ReactNativeBlobUtilConst.mm in Sources */ = {isa = PBXBuildFile; fileRef = 11A307F42A06E47500E56674 /* ReactNativeBlobUtilConst.mm */; };
		11A307FD2A06E47700E56674 /* ReactNativeBlobUtilProgress.mm in Sources */ = {isa = PBXBuildFile; fileRef = 11A307F52A06E47500E56674 /* ReactNativeBlobUtilProgress.mm */; };
		11A307FE2A06E47700E56674 /* ReactNativeBlobUtilRequest.mm in Sources */ = {isa = PBXBuildFile; fileRef = 11A307F62A06E47500E56674 /* ReactNativeBlobUtilRequest.mm */; };
		11A308002A06E92D00E56674 /* React.podspec in Frameworks */ = {isa = PBXBuildFile; fileRef = 11A307FF2A06E92D00E56674 /* React.podspec */; };
		A166D1AA1CE0647A00273590 /* ReactNativeBlobUtil.h in Sources */ = {isa = PBXBuildFile; fileRef = A15C30111CD25C330074CB35 /* ReactNativeBlobUtil.h */; };
/* End PBXBuildFile section */

/* Begin PBXCopyFilesBuildPhase section */
		A15C300C1CD25C330074CB35 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		11A307ED2A06E45800E56674 /* ReactNativeBlobUtil.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; name = ReactNativeBlobUtil.mm; path = ReactNativeBlobUtil/ReactNativeBlobUtil.mm; sourceTree = "<group>"; };
		11A307EF2A06E47300E56674 /* ReactNativeBlobUtilNetwork.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = ReactNativeBlobUtilNetwork.mm; sourceTree = "<group>"; };
		11A307F02A06E47400E56674 /* ReactNativeBlobUtilReqBuilder.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = ReactNativeBlobUtilReqBuilder.mm; sourceTree = "<group>"; };
		11A307F12A06E47400E56674 /* ReactNativeBlobUtilFS.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = ReactNativeBlobUtilFS.mm; sourceTree = "<group>"; };
		11A307F22A06E47400E56674 /* ReactNativeBlobUtil */ = {isa = PBXFileReference; lastKnownFileType = folder; path = ReactNativeBlobUtil; sourceTree = "<group>"; };
		11A307F32A06E47400E56674 /* ReactNativeBlobUtilFileTransformer.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = ReactNativeBlobUtilFileTransformer.mm; sourceTree = "<group>"; };
		11A307F42A06E47500E56674 /* ReactNativeBlobUtilConst.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = ReactNativeBlobUtilConst.mm; sourceTree = "<group>"; };
		11A307F52A06E47500E56674 /* ReactNativeBlobUtilProgress.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = ReactNativeBlobUtilProgress.mm; sourceTree = "<group>"; };
		11A307F62A06E47500E56674 /* ReactNativeBlobUtilRequest.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = ReactNativeBlobUtilRequest.mm; sourceTree = "<group>"; };
		11A307FF2A06E92D00E56674 /* React.podspec */ = {isa = PBXFileReference; lastKnownFileType = text; name = React.podspec; path = "../node_modules/react-native/React.podspec"; sourceTree = "<group>"; };
		1BDA30C5220621C99F823CB9 /* Pods-ReactNativeBlobUtil.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ReactNativeBlobUtil.release.xcconfig"; path = "Target Support Files/Pods-ReactNativeBlobUtil/Pods-ReactNativeBlobUtil.release.xcconfig"; sourceTree = "<group>"; };
		636B49C8101263AC402FD024 /* Pods_ReactNativeBlobUtil.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_ReactNativeBlobUtil.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		8C4801A4200CF71700FED7ED /* ReactNativeBlobUtilRequest.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ReactNativeBlobUtilRequest.h; sourceTree = "<group>"; };
		9FD8D3C126F1709A00009F35 /* ReactNativeBlobUtilFileTransformer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ReactNativeBlobUtilFileTransformer.h; sourceTree = "<group>"; };
		A158F4281D052E57006FFD38 /* ReactNativeBlobUtilFS.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ReactNativeBlobUtilFS.h; sourceTree = "<group>"; };
		A158F4291D0534A9006FFD38 /* ReactNativeBlobUtilConst.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ReactNativeBlobUtilConst.h; sourceTree = "<group>"; };
		A158F42E1D0539CE006FFD38 /* ReactNativeBlobUtilNetwork.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ReactNativeBlobUtilNetwork.h; sourceTree = "<group>"; };
		A15C300E1CD25C330074CB35 /* libReactNativeBlobUtil.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libReactNativeBlobUtil.a; sourceTree = BUILT_PRODUCTS_DIR; };
		A15C30111CD25C330074CB35 /* ReactNativeBlobUtil.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = ReactNativeBlobUtil.h; path = ReactNativeBlobUtil/ReactNativeBlobUtil.h; sourceTree = "<group>"; };
		A19B48231D98100800E6868A /* ReactNativeBlobUtilProgress.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ReactNativeBlobUtilProgress.h; sourceTree = "<group>"; };
		A1AAE2971D300E3E0051D11C /* ReactNativeBlobUtilReqBuilder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ReactNativeBlobUtilReqBuilder.h; sourceTree = "<group>"; };
		CBE41E482BB345A0004922E9 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; lastKnownFileType = text.xml; path = PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		E83F89B1EFF498D9833537A4 /* Pods-ReactNativeBlobUtil.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ReactNativeBlobUtil.debug.xcconfig"; path = "Target Support Files/Pods-ReactNativeBlobUtil/Pods-ReactNativeBlobUtil.debug.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		A15C300B1CD25C330074CB35 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				11A308002A06E92D00E56674 /* React.podspec in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		193768812AF9A9C46358234E /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				11A307FF2A06E92D00E56674 /* React.podspec */,
				636B49C8101263AC402FD024 /* Pods_ReactNativeBlobUtil.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		8BD9ABDFAF76406291A798F2 /* Libraries */ = {
			isa = PBXGroup;
			children = (
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		A15C30051CD25C330074CB35 = {
			isa = PBXGroup;
			children = (
				CBE41E482BB345A0004922E9 /* PrivacyInfo.xcprivacy */,
				11A307F22A06E47400E56674 /* ReactNativeBlobUtil */,
				11A307F42A06E47500E56674 /* ReactNativeBlobUtilConst.mm */,
				11A307F32A06E47400E56674 /* ReactNativeBlobUtilFileTransformer.mm */,
				11A307F12A06E47400E56674 /* ReactNativeBlobUtilFS.mm */,
				11A307EF2A06E47300E56674 /* ReactNativeBlobUtilNetwork.mm */,
				11A307F52A06E47500E56674 /* ReactNativeBlobUtilProgress.mm */,
				11A307F02A06E47400E56674 /* ReactNativeBlobUtilReqBuilder.mm */,
				11A307F62A06E47500E56674 /* ReactNativeBlobUtilRequest.mm */,
				11A307ED2A06E45800E56674 /* ReactNativeBlobUtil.mm */,
				9FD8D3C126F1709A00009F35 /* ReactNativeBlobUtilFileTransformer.h */,
				A19B48231D98100800E6868A /* ReactNativeBlobUtilProgress.h */,
				A1AAE2971D300E3E0051D11C /* ReactNativeBlobUtilReqBuilder.h */,
				A158F42E1D0539CE006FFD38 /* ReactNativeBlobUtilNetwork.h */,
				8C4801A4200CF71700FED7ED /* ReactNativeBlobUtilRequest.h */,
				A158F4291D0534A9006FFD38 /* ReactNativeBlobUtilConst.h */,
				A158F4281D052E57006FFD38 /* ReactNativeBlobUtilFS.h */,
				A15C30111CD25C330074CB35 /* ReactNativeBlobUtil.h */,
				A15C300F1CD25C330074CB35 /* Products */,
				8BD9ABDFAF76406291A798F2 /* Libraries */,
				BE4E5761992F01F75082D147 /* Pods */,
				193768812AF9A9C46358234E /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		A15C300F1CD25C330074CB35 /* Products */ = {
			isa = PBXGroup;
			children = (
				A15C300E1CD25C330074CB35 /* libReactNativeBlobUtil.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		BE4E5761992F01F75082D147 /* Pods */ = {
			isa = PBXGroup;
			children = (
				E83F89B1EFF498D9833537A4 /* Pods-ReactNativeBlobUtil.debug.xcconfig */,
				1BDA30C5220621C99F823CB9 /* Pods-ReactNativeBlobUtil.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		A15C300D1CD25C330074CB35 /* ReactNativeBlobUtil */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A15C30171CD25C330074CB35 /* Build configuration list for PBXNativeTarget "ReactNativeBlobUtil" */;
			buildPhases = (
				D6BB6591D040E628F7A16E89 /* [CP] Check Pods Manifest.lock */,
				A15C300A1CD25C330074CB35 /* Sources */,
				A15C300B1CD25C330074CB35 /* Frameworks */,
				A15C300C1CD25C330074CB35 /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = ReactNativeBlobUtil;
			productName = ReactNativeBlobUtil;
			productReference = A15C300E1CD25C330074CB35 /* libReactNativeBlobUtil.a */;
			productType = "com.apple.product-type.library.static";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		A15C30061CD25C330074CB35 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1420;
				ORGANIZATIONNAME = wkh237.github.io;
				TargetAttributes = {
					A15C300D1CD25C330074CB35 = {
						CreatedOnToolsVersion = 7.3;
					};
				};
			};
			buildConfigurationList = A15C30091CD25C330074CB35 /* Build configuration list for PBXProject "ReactNativeBlobUtil" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = A15C30051CD25C330074CB35;
			productRefGroup = A15C300F1CD25C330074CB35 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				A15C300D1CD25C330074CB35 /* ReactNativeBlobUtil */,
			);
		};
/* End PBXProject section */

/* Begin PBXShellScriptBuildPhase section */
		D6BB6591D040E628F7A16E89 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-ReactNativeBlobUtil-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		A15C300A1CD25C330074CB35 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				11A307F92A06E47700E56674 /* ReactNativeBlobUtilReqBuilder.mm in Sources */,
				A166D1AA1CE0647A00273590 /* ReactNativeBlobUtil.h in Sources */,
				11A307F82A06E47700E56674 /* ReactNativeBlobUtilNetwork.mm in Sources */,
				11A307FB2A06E47700E56674 /* ReactNativeBlobUtilFileTransformer.mm in Sources */,
				11A307FE2A06E47700E56674 /* ReactNativeBlobUtilRequest.mm in Sources */,
				11A307FC2A06E47700E56674 /* ReactNativeBlobUtilConst.mm in Sources */,
				11A307EE2A06E45800E56674 /* ReactNativeBlobUtil.mm in Sources */,
				11A307FD2A06E47700E56674 /* ReactNativeBlobUtilProgress.mm in Sources */,
				11A307FA2A06E47700E56674 /* ReactNativeBlobUtilFS.mm in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		A15C30151CD25C330074CB35 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		A15C30161CD25C330074CB35 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		A15C30181CD25C330074CB35 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = E83F89B1EFF498D9833537A4 /* Pods-ReactNativeBlobUtil.debug.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				GCC_INPUT_FILETYPE = automatic;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"$(SRCROOT)/Libraries/**",
					"$(SRCROOT)/../node_modules/react-native/React/**",
					"$(SRCROOT)/../../React/**",
					"$(SRCROOT)/../../react-native/React/**",
					"$(SRCROOT)/../../node_modules/react-native/React/**",
					"$(SRCROOT)/../../node_modules/react-native-blob-util/ios/ReactNativeBlobUtil",
					"$(SRCROOT)/../../ReactNativeBlobUtilTest/node_modules/react-native/**",
				);
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
			};
			name = Debug;
		};
		A15C30191CD25C330074CB35 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1BDA30C5220621C99F823CB9 /* Pods-ReactNativeBlobUtil.release.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				GCC_INPUT_FILETYPE = automatic;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"$(SRCROOT)/Libraries/**",
					"$(SRCROOT)/../node_modules/react-native/React/**",
					"$(SRCROOT)/../../React/**",
					"$(SRCROOT)/../../react-native/React/**",
					"$(SRCROOT)/../../node_modules/react-native/React/**",
					"$(SRCROOT)/../../node_modules/react-native-blob-util/ios/ReactNativeBlobUtil",
					"$(SRCROOT)/../../ReactNativeBlobUtilTest/node_modules/react-native/**",
				);
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		A15C30091CD25C330074CB35 /* Build configuration list for PBXProject "ReactNativeBlobUtil" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A15C30151CD25C330074CB35 /* Debug */,
				A15C30161CD25C330074CB35 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A15C30171CD25C330074CB35 /* Build configuration list for PBXNativeTarget "ReactNativeBlobUtil" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A15C30181CD25C330074CB35 /* Debug */,
				A15C30191CD25C330074CB35 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = A15C30061CD25C330074CB35 /* Project object */;
}
