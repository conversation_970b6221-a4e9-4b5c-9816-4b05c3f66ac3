package com.ReactNativeBlobUtil;


public class ReactNativeBlobUtilConst {
    public static final String EVENT_UPLOAD_PROGRESS = "ReactNativeBlobUtilProgress-upload";
    public static final String EVENT_PROGRESS = "ReactNativeBlobUtilProgress";
    public static final String EVENT_HTTP_STATE = "ReactNativeBlobUtilState";
    public static final String EVENT_MESSAGE = "ReactNativeBlobUtilMessage";
    public static final String EVENT_FILESYSTEM = "ReactNativeBlobUtilFilesystem";
    public static final String FILE_PREFIX = "ReactNativeBlobUtil-file://";
    public static final String CONTENT_PREFIX = "ReactNativeBlobUtil-content://";
    public static final String FILE_PREFIX_BUNDLE_ASSET = "bundle-assets://";
    public static final String FILE_PREFIX_CONTENT = "content://";
    public static final String DATA_ENCODE_URI = "uri";
    public static final String RNFB_RESPONSE_BASE64 = "base64";
    public static final String RNFB_RESPONSE_UTF8 = "utf8";
    public static final String RNFB_RESPONSE_PATH = "path";
    public static final Integer GET_CONTENT_INTENT = 99900;

}
