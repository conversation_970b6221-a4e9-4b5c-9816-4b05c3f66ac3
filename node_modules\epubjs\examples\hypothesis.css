body {
  margin: 0;
  background: #fafafa;
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  color: #333;
}

svg {
  display: block;
}

.close-x {
  stroke: #cccddd;
  fill: transparent;
  stroke-linecap: round;
  stroke-width: 5;
}

.close-x:hover {
  stroke: #fff;
}

#opener {
  position: absolute;
  top: 0;
  left: 0;
  padding: 10px;
  stroke: #E2E2E2;
  fill: #E2E2E2;

}

#opener:hover {
  stroke: #777;
  fill: #777;
}

#navigation {
  width: 400px;
  position: fixed;
  overflow: auto;
  top: 0;
  left: -425px;

  background: #ECECEC;
  min-height: 100%;
  height: 100%;
  height: 100vh;

  overflow: scroll;
  -webkit-overflow-scrolling: touch;
  padding: 9px;
  padding-top: 10px;

  transition: left .2s ease-out;
  box-shadow: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
}

#navigation.open {
  left: 0;
}

#navigation.fixed {
  position: fixed;
}

#navigation h1 {
  width: 200px;
  font-size: 16px;
  font-weight: normal;
  color: #777;
  margin-bottom: 10px;
}

#navigation h2 {
  font-size: 14px;
  font-weight: normal;
  color: #B0B0B0;
  margin-bottom: 20px;
}

#navigation ul {
  padding-left: 28px;
  margin-left: 0;
}

#navigation ul li {
  list-style: decimal;
  margin-bottom: 10px;
  color: #585858;
  font-size: 12px;
  padding-left: 0;
  margin-left: 0;
}

#navigation ul li a {
  color: #585858;
  text-decoration: none;
}

#navigation ul li a:hover {
  color: #585858;
  text-decoration: underline;
}

#navigation ul li a.active {
  color: #000;
  font-weight: 400;
}

#navigation #author {
  text-align: center;
}

#cover {
  display: inline;
}

#main {
  margin-top: 60px;
}

#pagination {
  text-align: center;
  margin: 20px;
  /*padding: 0 50px;*/
}

.arrow:hover {
  color: #777;
}

.arrow:active {
  color: #000;
}

.arrow .material-icons {
  font-size: 64px;
}

#prev {
  float: left;
}

#next {
  float: right;
}

#toc {
  display: block;
  margin: 10px auto;
}

#hypothesis-custom {
  overflow: hidden;
  /*position: absolute;*/
  right: 0;
  /*top: 0;*/
  height: 100%;
  width: 200px;
  /*z-index: -2;*/
}

#hypothesis-custom iframe {
  position: absolute;
  width: 100%;
  height: 100%;
}

#navigation #cover {
  display: block;
  margin: 24px auto;
}

#closer {
  position: absolute;
  padding: 12px;
  left: 0;
  top: 0;
  color: #333;
  cursor: pointer;
}

#closer .material-icons {
  color: #333;
}

#opener {
  position: absolute;
  left: 0;
  top: 0;
  cursor: pointer;
}

#hiddenTitle {
  display: none;
}

#title {
  width: 900px;
  min-height: 18px;
  margin: 10px auto;
  text-align: center;
  font-size: 16px;
  color: #E2E2E2;
  font-weight: 400;
}

#title:hover {
  color: #777;
}

#prev {
  left: 0;
}

#next {
  right: 0;
}

#toc {
  display: block;
  margin: 10px auto;
}

@media (min-width: 1000px) {
  #viewer.spreads:after {
    position: absolute;
    width: 1px;
    border-right: 1px #000 solid;
    height: 90%;
    z-index: 1;
    left: 50%;
    margin-left: -1px;
    top: 5%;
    opacity: .15;
    box-shadow: -2px 0 15px rgba(0, 0, 0, 1);
    content:  "";
  }

  #prev {
    left: 40px;
  }

  #next {
    right: 40px;
  }
}

#viewer.spreads {
  width: 84vw;
  height: 80vh;
  box-shadow: 0 0 4px #ccc;
  border-radius: 5px;
  padding: 0;
  position: relative;
  margin: 10vh auto;
  background: white;
  top: 0;
}

.arrow {
  position: fixed;
  top: 50%;
  margin-top: -32px;
  font-size: 64px;
  color: #E2E2E2;
  font-family: arial, sans-serif;
  font-weight: bold;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  text-decoration: none;
}

.navlink {
  margin: 14px;
  display: block;
  text-align: center;
  text-decoration: none;
  color: #ccc;
}

.arrow:hover, .navlink:hover {
  color: #777;
}

.arrow:active, .navlink:hover {
  color: #000;
}

#book-wrapper {
  width: 480px;
  height: 640px;
  overflow: hidden;
  border: 1px solid #ccc;
  margin: 28px auto;
  background: #fff;
  border-radius: 0 5px 5px 0;
  position: absolute;
}

#book-viewer {
  width: 480px;
  height: 660px;
  margin: -30px auto;
  -moz-box-shadow:      inset 10px 0 20px rgba(0,0,0,.1);
  -webkit-box-shadow:   inset 10px 0 20px rgba(0,0,0,.1);
  box-shadow:           inset 10px 0 20px rgba(0,0,0,.1);
}

#book-viewer iframe {
  padding: 40px 40px;
}

#controls {
  position: absolute;
  bottom: 16px;
  left: 50%;
  width: 400px;
  margin-left: -200px;
  text-align: center;
  display: none;
}

#controls > input[type=range] {
    width: 400px;
}
