<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>EPUB.js Continuous Example</title>

  <script src="../dist/epub.js"></script>
  <!-- <script src="https://code.jquery.com/jquery-2.1.4.min.js"></script> -->
  <script src="//hypothes.is/embed.js"></script>

  <style type="text/css">
    body {
      margin: 0 auto;
      background: #fafafa;
      font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
      color: #333;
      height: 100%;
      width: 100%;
      max-width: 1200px;
    }

    .epub-container {
      min-width: 320px;
      margin: 0 auto;
    }

      /*.epub-container > div {
        padding: 0 20% 0 20%;
      }*/

    .epub-container .epub-view > iframe {
        background: white;
        box-shadow: 0 0 4px #ccc;
        margin: 10px;
        padding: 20px;
    }


    #prev {
      left: 40px;
    }

    #next {
      right: 40px;
    }

    .arrow {
      position: fixed;
      top: 50%;
      margin-top: -32px;
      font-size: 64px;
      color: #E2E2E2;
      font-family: arial, sans-serif;
      font-weight: bold;
      cursor: pointer;
      -webkit-user-select: none;
      -moz-user-select: none;
      user-select: none;
    }

    .arrow:hover {
      color: #777;
    }

    .arrow:active {
      color: #000;
    }

    #toc {
      display: block;
      margin: 10px auto;
    }

  </style>
</head>
<body>
  <div id="viewer"></div>

  <script>
    // var currentSectionIndex = 8;
    // Load the opf
    var book = ePub("https://s3.amazonaws.com/epubjs/books/alice/OPS/package.opf");
    var rendition = book.renderTo(document.body, { method: "continuous", flow: "scrolled-continuous", width: "60%" });
    var displayed = rendition.display();


    displayed.then(function(renderer){
      // -- do stuff
    });

    // Navigation loaded
    book.loaded.navigation.then(function(toc){
      // console.log(toc);
    });

    book.rendition.hooks.content.register(function(view) {
      return view.addScript("//hypothes.is/embed.js").then(function() {
        view.window.hypothesisConfig = function () {

      		return {
      			constructor: this.Annotator.Guest,
              	app: 'https://hypothes.is/app.html'
      		};
      	};

        // view.window.hypothesisInstall();
      });
    });

    book.rendition.hooks.render.register(function(view) {
    	var getVisibleAnnotations = function ($) {
    		var height = document.documentElement.clientHeight;
    		return $('.annotator-hl').map(function() {
    			var $this = $(this),
              pos = view.position().top,
              top = pos + this.getBoundingClientRect().top;
    			if (top >= 0 && top <= height) {
    				return $this.data('annotation');
    			}
    		}).get();
    	};
      book.rendition.on("scroll", function() {
        var annotator = view.window.annotator;
        var annotations;
        if(annotator && annotator.constructor.$) {
          annotations = getVisibleAnnotations(annotator.constructor.$);
          annotator.showAnnotations(annotations);
        }
      });
    });

  </script>

</body>
</html>
