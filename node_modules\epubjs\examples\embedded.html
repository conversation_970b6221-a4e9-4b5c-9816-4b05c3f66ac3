<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title></title>

  <script src="../dist/epub.js"></script>

  <style>

    body {
      margin: 0;
    }

  </style>
</head>
<body>

  <script>
    var params = URLSearchParams && new URLSearchParams(document.location.search.substring(1));
    var url = params && params.get("url") && decodeURIComponent(params.get("url"));
    var currentCfi = (params && params.get("cfi")) ? params.get("cfi") : undefined;
    var currentSectionIndex = (params && params.get("loc")) ? parseInt(params.get("loc")) : undefined;

    // Load the opf
    var book = ePub(url || "https://s3.amazonaws.com/moby-dick/");
    var rendition = book.renderTo(document.body, {
      manager: "continuous",
      snap: true
    });

    rendition.display(currentCfi || currentSectionIndex);


    rendition.on("rendered", function(section){
      // console.log("rendered", section);
      var nextSection = section.next();
      var prevSection = section.prev();

      var current = book.navigation && book.navigation.get(section.href);

      if (current) {
        document.title = current.label;
      }
    });

    rendition.on("relocated", function(location){
      // console.log("locationChanged", location)
      console.log("locationChanged start", location.start.cfi)
      // console.log("locationChanged end", location.end.cfi)
    });

    window.addEventListener("unload", function () {
      console.log("unloading");
      this.book.destroy();
    });

  </script>
</body>
</html>
