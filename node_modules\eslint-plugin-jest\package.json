{"name": "eslint-plugin-jest", "version": "29.0.1", "description": "ESLint rules for Jest", "keywords": ["eslint", "eslintplugin", "eslint-plugin"], "homepage": "https://github.com/jest-community/eslint-plugin-jest#readme", "bugs": {"url": "https://github.com/jest-community/eslint-plugin-jest/issues"}, "repository": {"type": "git", "url": "git+https://github.com/jest-community/eslint-plugin-jest.git"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "jkimbo.com"}, "main": "lib/index.js", "types": "index.d.ts", "files": ["docs/", "lib/", "index.d.ts"], "scripts": {"build": "babel --extensions .js,.ts src --out-dir lib --copy-files && rimraf --glob lib/__tests__ 'lib/**/__tests__'", "_postinstall": "is-ci || husky", "lint": "eslint . --ignore-pattern '!.eslintrc.js' --ext js,ts", "prepack": "rimraf lib && yarn build", "prepublishOnly": "pinst --disable", "prettier:check": "prettier --check 'docs/**/*.md' README.md '.github/**' package.json tsconfig.json src/globals.json .yarnrc.yml", "prettier:write": "prettier --write 'docs/**/*.md' README.md '.github/**' package.json tsconfig.json src/globals.json .yarnrc.yml", "postpublish": "pinst --enable", "regenerate-docs": "yarn prepack && eslint-doc-generator", "test": "jest", "typecheck": "tsc -p ."}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "lint-staged": {"*.{js,ts}": "eslint --fix", "*.{md,json,yml}": "prettier --write"}, "prettier": {"arrowParens": "avoid", "endOfLine": "auto", "proseWrap": "always", "singleQuote": true}, "release": {"branches": ["main", {"name": "next", "prerelease": true}], "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/git", "@semantic-release/github"]}, "dependencies": {"@typescript-eslint/utils": "^8.0.0"}, "devDependencies": {"@babel/cli": "^7.4.4", "@babel/core": "^7.4.4", "@babel/preset-env": "^7.4.4", "@babel/preset-typescript": "^7.3.3", "@commitlint/cli": "^17.0.3", "@commitlint/config-conventional": "^17.0.3", "@eslint-community/eslint-plugin-eslint-comments": "^4.3.0", "@schemastore/package": "^0.0.10", "@semantic-release/changelog": "^6.0.0", "@semantic-release/git": "^10.0.0", "@tsconfig/node20": "^20.0.0", "@types/eslint": "^8.4.6", "@types/jest": "^29.0.0", "@types/node": "^20.0.0", "@types/semver": "^7.5.8", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "@typescript-eslint/utils": "^8.0.0", "babel-jest": "^29.0.0", "babel-plugin-replace-ts-export-assignment": "^0.0.2", "dedent": "^1.5.0", "eslint": "^8.57.0", "eslint-config-prettier": "^10.0.0", "eslint-doc-generator": "^2.0.0", "eslint-plugin-eslint-plugin": "^6.0.0", "eslint-plugin-import": "^2.25.1", "eslint-plugin-n": "^17.0.0", "eslint-plugin-prettier": "^5.0.0", "eslint-remote-tester": "^3.0.0", "eslint-remote-tester-repositories": "~1.0.0", "husky": "^9.0.1", "is-ci": "^4.0.0", "jest": "^29.0.0", "jest-runner-eslint": "^2.0.0", "lint-staged": "^13.0.3", "markdown-link-check": "~3.12.0", "pinst": "^3.0.0", "prettier": "^3.0.0", "rimraf": "^5.0.0", "semantic-release": "^24.0.0", "semver": "^7.3.5", "ts-node": "^10.2.1", "typescript": "^5.0.4"}, "peerDependencies": {"@typescript-eslint/eslint-plugin": "^8.0.0", "eslint": "^8.57.0 || ^9.0.0", "jest": "*"}, "peerDependenciesMeta": {"@typescript-eslint/eslint-plugin": {"optional": true}, "jest": {"optional": true}}, "packageManager": "yarn@3.8.7", "engines": {"node": "^20.12.0 || ^22.0.0 || >=24.0.0"}, "publishConfig": {"provenance": true}}