<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>EPUB.js Scrolled Example</title>

  <script src="../dist/epub.js"></script>

  <link rel="stylesheet" type="text/css" href="examples.css">

</head>
<body>
  <a id="prev" href="#prev" class="navlink">...</a>
  <div id="viewer" class="scrolled"></div>
  <a id="next" href="#next" class="navlink">...</a>

  <script>
    // Load the opf
    var params = URLSearchParams && new URLSearchParams(document.location.search.substring(1));
    var url = params && params.get("url") && decodeURIComponent(params.get("url"));
    var currentSectionIndex = (params && params.get("loc")) ? params.get("loc") : undefined;

    var book = ePub(url || "https://s3.amazonaws.com/epubjs/books/alice/OPS/package.opf");
    var rendition = book.renderTo("viewer", {
      flow: "scrolled-doc",
      width: "100%",
      fullsize: true
    });

    rendition.display(currentSectionIndex);


    var next = document.getElementById("next");
    next.addEventListener("click", function(e){
      rendition.next();
      e.preventDefault();
    }, false);

    var prev = document.getElementById("prev");
    prev.addEventListener("click", function(e){
      rendition.prev();
      e.preventDefault();
    }, false);

    rendition.on("relocated", function(location){
      console.log(location);
    });

    rendition.on("rendered", function(section){
      var nextSection = section.next();
      var prevSection = section.prev();

      if(nextSection) {
        nextNav = book.navigation.get(nextSection.href);

        if(nextNav) {
          nextLabel = nextNav.label;
        } else {
          nextLabel = "next";
        }

        next.textContent = nextLabel + " »";
      } else {
        next.textContent = "";
      }

      if(prevSection) {
        prevNav = book.navigation.get(prevSection.href);

        if(prevNav) {
          prevLabel = prevNav.label;
        } else {
          prevLabel = "previous";
        }

        prev.textContent = "« " + prevLabel;
      } else {
        prev.textContent = "";
      }

    });

  </script>

</body>
</html>
