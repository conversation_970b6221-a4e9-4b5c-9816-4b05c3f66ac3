<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## ePub

Creates a new Book

**Parameters**

-   `url` **([string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String) \| [ArrayBuffer](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/ArrayBuffer))** URL, Path or ArrayBuffer
-   `options` **[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)** to pass to the book

**Examples**

```javascript
ePub("/path/to/book.epub", {})
```

Returns **[Book](#book)** a new Book object

## Book

An Epub representation with methods for the loading, parsing and manipulation
of its contents.

**Parameters**

-   `url` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)?** 
-   `options` **[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)?** 
    -   `options.requestMethod` **method?** a request function to use instead of the default
    -   `options.requestCredentials` **[boolean](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** send the xhr request withCredentials (optional, default `undefined`)
    -   `options.requestHeaders` **[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)** send the xhr request headers (optional, default `undefined`)
    -   `options.encoding` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** optional to pass 'binary' or base64' for archived Epubs (optional, default `binary`)
    -   `options.replacements` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** use base64, blobUrl, or none for replacing assets in archived Epubs (optional, default `none`)
    -   `options.canonical` **method?** optional function to determine canonical urls for a path
    -   `options.openAs` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)?** optional string to determine the input type

**Examples**

```javascript
new Book("/path/to/book.epub", {})
```

```javascript
new Book({ replacements: "blobUrl" })
```

Returns **[Book](#book)** 

### open

Open a epub or url

**Parameters**

-   `input` **([string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String) \| [ArrayBuffer](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/ArrayBuffer))** Url, Path or ArrayBuffer
-   `what` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** force opening as a certain type (optional, default `"binary","base64","epub","opf","json","directory"`)

**Examples**

```javascript
book.open("/path/to/book.epub")
```

Returns **[Promise](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise)** of when the book has been loaded

### load

Load a resource from the Book

**Parameters**

-   `path` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** path to the resource to load

Returns **[Promise](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise)** returns a promise with the requested resource

### resolve

Resolve a path to it's absolute position in the Book

**Parameters**

-   `path` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 
-   `absolute` **[boolean](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean)?** force resolving the full URL

Returns **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** the resolved path string

### canonical

Get a canonical link to a path

**Parameters**

-   `path` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 

Returns **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** the canonical path string

### section

Gets a Section of the Book from the Spine
Alias for `book.spine.get`

**Parameters**

-   `target` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 

Returns **[Section](#section)** 

### renderTo

Sugar to render a book to an element

**Parameters**

-   `element` **([element](https://developer.mozilla.org/en-US/docs/Web/API/Element) \| [string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String))** element or string to add a rendition to
-   `options` **[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)?** 

Returns **[Rendition](#rendition)** 

### setRequestCredentials

Set if request should use withCredentials

**Parameters**

-   `credentials` **[boolean](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** 

### setRequestHeaders

Set headers request should use

**Parameters**

-   `headers` **[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)** 

### coverUrl

Get the cover url if there is a cover

Returns **[Promise](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise)&lt;[?string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)&gt;** Promise resolves with maybe a url string

### getRange

Find a DOM Range for a given CFI Range

**Parameters**

-   `cfiRange` **[EpubCFI](#epubcfi)** a epub cfi range

Returns **[Range](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Input)** 

### key

Generates the Book Key using the identifier in the manifest or other string provided

**Parameters**

-   `identifier` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)?** to use instead of metadata identifier

Returns **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** key

### destroy

Destroy the Book and all associated objects

### opened

### spine

Type: [Spine](#spine)

### locations

Type: [Locations](#locations)

### navigation

Type: [Navigation](#navigation)

### pagelist

Type: [PageList](#pagelist)

## Url

creates a Url object for parsing and manipulation of a url string

**Parameters**

-   `urlString` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** a url string (relative or absolute)
-   `baseString` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)?** optional base for the url,
    default to window.location.href

### path

Returns **[Path](#path)** 

### resolve

Resolves a relative path to a absolute url

**Parameters**

-   `what` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 

Returns **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** url

### relative

Resolve a path relative to the url

**Parameters**

-   `what` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 

Returns **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** path

### toString

Returns **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 

## Path

Creates a Path object for parsing and manipulation of a path strings

Uses a polyfill for Nodejs path: <https://nodejs.org/api/path.html>

**Parameters**

-   `pathString` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** a url string (relative or absolute)

### parse

Parse the path: <https://nodejs.org/api/path.html#path_path_parse_path>

**Parameters**

-   `what` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 

Returns **[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)** 

### isAbsolute

**Parameters**

-   `what` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 

Returns **[boolean](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** 

### isDirectory

Check if path ends with a directory

**Parameters**

-   `what` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 

Returns **[boolean](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** 

### resolve

Resolve a path against the directory of the Path

<https://nodejs.org/api/path.html#path_path_resolve_paths>

**Parameters**

-   `what` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 

Returns **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** resolved

### relative

Resolve a path relative to the directory of the Path

<https://nodejs.org/api/path.html#path_path_relative_from_to>

**Parameters**

-   `what` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 

Returns **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** relative

### toString

Return the path string

Returns **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** path

## Spine

A collection of Spine Items

### unpack

Unpack items from a opf into spine items

**Parameters**

-   `_package` **[Packaging](#packaging)** 
-   `resolver` **method** URL resolver
-   `canonical` **method** Resolve canonical url

### get

Get an item from the spine

**Parameters**

-   `target` **([string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String) \| [number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number))?** 

**Examples**

```javascript
spine.get();
```

```javascript
spine.get(1);
```

```javascript
spine.get("chap1.html");
```

```javascript
spine.get("#id1234");
```

Returns **[Section](#section)** section

### each

Loop over the Sections in the Spine

Returns **method** forEach

### first

Find the first Section in the Spine

Returns **[Section](#section)** first section

### last

Find the last Section in the Spine

Returns **[Section](#section)** last section

## Section

Represents a Section of the Book

In most books this is equivalent to a Chapter

**Parameters**

-   `item` **[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)** The spine item representing the section
-   `hooks` **[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)** hooks for serialize and content

### load

Load the section from its url

**Parameters**

-   `_request` **method?** a request method to use for loading

Returns **[document](https://developer.mozilla.org/en-US/docs/Web/JavaScript)** a promise with the xml document

### render

Render the contents of a section

**Parameters**

-   `_request` **method?** a request method to use for loading

Returns **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** output a serialized XML Document

### find

Find a string in a section

**Parameters**

-   `_query` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** The query string to find

Returns **[Array](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array)&lt;[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)>** A list of matches, with form {cfi, excerpt}

### reconcileLayoutSettings

Reconciles the current chapters layout properties with
the global layout properties.

**Parameters**

-   `globalLayout` **[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)** The global layout settings object, chapter properties string

Returns **[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)** layoutProperties Object with layout properties

### cfiFromRange

Get a CFI from a Range in the Section

**Parameters**

-   `_range` **[range](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Input)** 

Returns **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** cfi an EpubCFI string

### cfiFromElement

Get a CFI from an Element in the Section

**Parameters**

-   `el` **[element](https://developer.mozilla.org/en-US/docs/Web/API/Element)** 

Returns **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** cfi an EpubCFI string

### unload

Unload the section document

## Locations

Find Locations for a Book

**Parameters**

-   `spine` **[Spine](#spine)** 
-   `request` **[request](https://developer.mozilla.org/en-US/Add-ons/SDK/High-Level_APIs/request)** 
-   `pause` **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)**  (optional, default `100`)

### generate

Load all of sections in the book to generate locations

**Parameters**

-   `chars` **int** how many chars to split on

Returns **[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)** locations

### locationFromCfi

Get a location from an EpubCFI

**Parameters**

-   `cfi` **[EpubCFI](#epubcfi)** 

Returns **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** 

### percentageFromCfi

Get a percentage position in locations from an EpubCFI

**Parameters**

-   `cfi` **[EpubCFI](#epubcfi)** 

Returns **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** 

### percentageFromLocation

Get a percentage position from a location index

**Parameters**

-   `loc`  
-   `location` **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** 

Returns **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** 

### cfiFromLocation

Get an EpubCFI from location index

**Parameters**

-   `loc` **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** 

Returns **[EpubCFI](#epubcfi)** cfi

### cfiFromPercentage

Get an EpubCFI from location percentage

**Parameters**

-   `percentage` **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** 

Returns **[EpubCFI](#epubcfi)** cfi

### load

Load locations from JSON

**Parameters**

-   `locations` **[json](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON)** 

### save

Save locations to JSON

Returns **[json](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON)** 

### currentLocation

Get the current location

### currentLocation

Set the current location

**Parameters**

-   `curr`  

### length

Locations length

## Container

Handles Parsing and Accessing an Epub Container

**Parameters**

-   `containerDocument` **[document](https://developer.mozilla.org/en-US/docs/Web/JavaScript)?** xml document

### parse

Parse the Container XML

**Parameters**

-   `containerDocument` **[document](https://developer.mozilla.org/en-US/docs/Web/JavaScript)** 

## Packaging

Open Packaging Format Parser

**Parameters**

-   `packageDocument` **[document](https://developer.mozilla.org/en-US/docs/Web/JavaScript)** OPF XML

### parse

Parse OPF XML

**Parameters**

-   `packageDocument` **[document](https://developer.mozilla.org/en-US/docs/Web/JavaScript)** OPF XML

Returns **[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)** parsed package parts

### load

Load JSON Manifest

**Parameters**

-   `json`  
-   `packageDocument` **[document](https://developer.mozilla.org/en-US/docs/Web/JavaScript)** OPF XML

Returns **[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)** parsed package parts

## Navigation

Navigation Parser

**Parameters**

-   `xml` **[document](https://developer.mozilla.org/en-US/docs/Web/JavaScript)** navigation html / xhtml / ncx

### parse

Parse out the navigation items

**Parameters**

-   `xml` **[document](https://developer.mozilla.org/en-US/docs/Web/JavaScript)** navigation html / xhtml / ncx

### get

Get an item from the navigation

**Parameters**

-   `target` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 

Returns **[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)** navItem

### landmark

Get a landmark by type
List of types: <https://idpf.github.io/epub-vocabs/structure/>

**Parameters**

-   `type` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 

Returns **[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)** landmarkItem

### load

Load Spine Items

**Parameters**

-   `json` **[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)** the items to be loaded

Returns **[Array](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array)** navItems

### forEach

forEach pass through

**Parameters**

-   `fn` **[Function](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function)** function to run on each item

Returns **method** forEach loop

## Resources

Handle Package Resources

**Parameters**

-   `manifest` **Manifest** 
-   `options` **[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)?** 
    -   `options.replacements` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)**  (optional, default `"base64"`)
    -   `options.archive` **[Archive](#archive)?** 
    -   `options.resolver` **method?** 

### createUrl

Create a url to a resource

**Parameters**

-   `url` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 

Returns **[Promise](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise)&lt;[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)>** Promise resolves with url string

### replacements

Create blob urls for all the assets

Returns **[Promise](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise)** returns replacement urls

### relativeTo

Resolve all resources URLs relative to an absolute URL

**Parameters**

-   `absolute` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** to be resolved to
-   `resolver` **resolver?** 

Returns **[Array](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array)&lt;[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)>** array with relative Urls

### get

Get a URL for a resource

**Parameters**

-   `path` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 

Returns **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** url

### substitute

Substitute urls in content, with replacements,
relative to a url if provided

**Parameters**

-   `content` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 
-   `url` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)?** url to resolve to

Returns **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** content with urls substituted

## PageList

Page List Parser

**Parameters**

-   `xml` **[document](https://developer.mozilla.org/en-US/docs/Web/JavaScript)?** 

### parse

Parse PageList Xml

**Parameters**

-   `xml` **[document](https://developer.mozilla.org/en-US/docs/Web/JavaScript)** 

### pageFromCfi

Get a PageList result from a EpubCFI

**Parameters**

-   `cfi` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** EpubCFI String

Returns **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** page

### cfiFromPage

Get an EpubCFI from a Page List Item

**Parameters**

-   `pg` **([string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String) \| [number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number))** 

Returns **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** cfi

### pageFromPercentage

Get a Page from Book percentage

**Parameters**

-   `percent` **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** 

Returns **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** page

### percentageFromPage

Returns a value between 0 - 1 corresponding to the location of a page

**Parameters**

-   `pg` **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** the page

Returns **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** percentage

### percentageFromCfi

Returns a value between 0 - 1 corresponding to the location of a cfi

**Parameters**

-   `cfi` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** EpubCFI String

Returns **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** percentage

### destroy

Destroy

## Archive

Handles Unzipping a requesting files from an Epub Archive

### open

Open an archive

**Parameters**

-   `input` **binary** 
-   `isBase64` **[boolean](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean)?** tells JSZip if the input data is base64 encoded

Returns **[Promise](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise)** zipfile

### openUrl

Load and Open an archive

**Parameters**

-   `zipUrl` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 
-   `isBase64` **[boolean](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean)?** tells JSZip if the input data is base64 encoded

Returns **[Promise](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise)** zipfile

### request

Request a url from the archive

**Parameters**

-   `url` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** a url to request from the archive
-   `type` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)?** specify the type of the returned result

Returns **[Promise](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise)&lt;([Blob](https://developer.mozilla.org/en-US/docs/Web/API/Blob) \| [string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String) \| [JSON](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON) \| [Document](https://developer.mozilla.org/en-US/docs/Web/JavaScript) | XMLDocument)>** 

### getBlob

Get a Blob from Archive by Url

**Parameters**

-   `url` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 
-   `mimeType` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)?** 

Returns **[Blob](https://developer.mozilla.org/en-US/docs/Web/API/Blob)** 

### getText

Get Text from Archive by Url

**Parameters**

-   `url` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 
-   `encoding` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)?** 

Returns **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 

### getBase64

Get a base64 encoded result from Archive by Url

**Parameters**

-   `url` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 
-   `mimeType` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)?** 

Returns **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** base64 encoded

### createUrl

Create a Url from an unarchived item

**Parameters**

-   `url` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 
-   `options`  

Returns **[Promise](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise)** url promise with Url string

### revokeUrl

Revoke Temp Url for a archive item

**Parameters**

-   `url` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** url of the item in the archive

## Rendition

Displays an Epub as a series of Views for each Section.
Requires Manager and View class to handle specifics of rendering
the section content.

**Parameters**

-   `book` **[Book](#book)** 
-   `options` **[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)?** 
    -   `options.width` **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)?** 
    -   `options.height` **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)?** 
    -   `options.ignoreClass` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)?** class for the cfi parser to ignore
    -   `options.manager` **([string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String) \| [function](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function) \| [object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object))**  (optional, default `'default'`)
    -   `options.view` **([string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String) \| [function](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function))**  (optional, default `'iframe'`)
    -   `options.layout` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)?** layout to force
    -   `options.spread` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)?** force spread value
    -   `options.minSpreadWidth` **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)?** overridden by spread: none (never) / both (always)
    -   `options.stylesheet` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)?** url of stylesheet to be injected
    -   `options.resizeOnOrientationChange` **[boolean](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean)?** false to disable orientation events
    -   `options.script` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)?** url of script to be injected

### setManager

Set the manager function

**Parameters**

-   `manager` **[function](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function)** 

### requireManager

Require the manager from passed string, or as a class function

**Parameters**

-   `manager` **([string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String) \| [object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object))** [description]

Returns **method** 

### requireView

Require the view from passed string, or as a class function

**Parameters**

-   `view` **([string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String) \| [object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object))** 

Returns **view** 

### start

Start the rendering

Returns **[Promise](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise)** rendering has started

### attachTo

Call to attach the container to an element in the dom
Container must be attached before rendering can begin

**Parameters**

-   `element` **[element](https://developer.mozilla.org/en-US/docs/Web/API/Element)** to attach to

Returns **[Promise](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise)** 

### display

Display a point in the book
The request will be added to the rendering Queue,
so it will wait until book is opened, rendering started
and all other rendering tasks have finished to be called.

**Parameters**

-   `target` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** Url or EpubCFI

Returns **[Promise](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise)** 

### moveTo

Move the Rendition to a specific offset
Usually you would be better off calling display()

**Parameters**

-   `offset` **[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)** 

### resize

Trigger a resize of the views

**Parameters**

-   `width` **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)?** 
-   `height` **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)?** 

### clear

Clear all rendered views

### next

Go to the next "page" in the rendition

Returns **[Promise](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise)** 

### prev

Go to the previous "page" in the rendition

Returns **[Promise](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise)** 

### flow

Adjust the flow of the rendition to paginated or scrolled
(scrolled-continuous vs scrolled-doc are handled by different view managers)

**Parameters**

-   `flow` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 

### layout

Adjust the layout of the rendition to reflowable or pre-paginated

**Parameters**

-   `settings` **[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)** 

### spread

Adjust if the rendition uses spreads

**Parameters**

-   `spread` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** none | auto (TODO: implement landscape, portrait, both)
-   `min` **int** min width to use spreads at

### direction

Adjust the direction of the rendition

**Parameters**

-   `dir` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 

### reportLocation

Report the current location

### currentLocation

Get the Current Location object

Returns **(displayedLocation | [promise](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise))** location (may be a promise)

### destroy

Remove and Clean Up the Rendition

### getRange

Get a Range from a Visible CFI

**Parameters**

-   `cfi` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** EpubCfi String
-   `ignoreClass` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 

Returns **[range](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Input)** 

### getContents

Get the Contents object of each rendered view

Returns **[Array](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array)&lt;[Contents](#contents)>** 

### views

Get the views member from the manager

Returns **Views** 

### hooks

Adds Hook methods to the Rendition prototype

Type: [object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)

### themes

Type: [Themes](#themes)

### annotations

Type: [Annotations](#annotations)

### location

A Rendered Location Range

**Properties**

-   `start` **[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)** 
    -   `start.index` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 
    -   `start.href` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 
    -   `start.displayed` **[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)** 
        -   `start.displayed.page` **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** 
        -   `start.displayed.total` **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** 
    -   `start.cfi` **[EpubCFI](#epubcfi)** 
    -   `start.location` **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** 
    -   `start.percentage` **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** 
-   `end` **[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)** 
    -   `end.index` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 
    -   `end.href` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 
    -   `end.displayed` **[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)** 
        -   `end.displayed.page` **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** 
        -   `end.displayed.total` **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** 
    -   `end.cfi` **[EpubCFI](#epubcfi)** 
    -   `end.location` **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** 
    -   `end.percentage` **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** 
-   `atStart` **[boolean](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** 
-   `atEnd` **[boolean](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** 

### started

## Hook

Hooks allow for injecting functions that must all complete in order before finishing
They will execute in parallel but all must finish before continuing
Functions may return a promise if they are async.

**Parameters**

-   `context` **any** scope of this

**Examples**

```javascript
this.content = new EPUBJS.Hook(this);
```

### register

Adds a function to be run before a hook completes

**Examples**

```javascript
this.content.register(function(){...});
```

### trigger

Triggers a hook to run all functions

**Examples**

```javascript
this.content.trigger(args).then(function(){...});
```

## Queue

Queue for handling tasks one at a time

**Parameters**

-   `context` **scope** what this will resolve to in the tasks

### enqueue

Add an item to the queue

Returns **[Promise](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise)** 

### dequeue

Run one item

Returns **[Promise](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise)** 

### run

Run all tasks sequentially, at convince

Returns **[Promise](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise)** 

### flush

Flush all, as quickly as possible

Returns **[Promise](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise)** 

### clear

Clear all items in wait

### length

Get the number of tasks in the queue

Returns **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** tasks

### pause

Pause a running queue

### stop

End the queue

## Layout

Figures out the CSS values to apply for a layout

**Parameters**

-   `settings` **[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)** 
    -   `settings.layout` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)**  (optional, default `'reflowable'`)
    -   `settings.spread` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)?** 
    -   `settings.minSpreadWidth` **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)**  (optional, default `800`)
    -   `settings.evenSpreads` **[boolean](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean)**  (optional, default `false`)

### flow

Switch the flow between paginated and scrolled

**Parameters**

-   `flow` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** paginated | scrolled

Returns **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** simplified flow

### spread

Switch between using spreads or not, and set the
width at which they switch to single.

**Parameters**

-   `spread` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** "none" | "always" | "auto"
-   `min` **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** integer in pixels

Returns **[boolean](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** spread true | false

### calculate

Calculate the dimensions of the pagination

**Parameters**

-   `_width` **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** width of the rendering
-   `_height` **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** height of the rendering
-   `_gap` **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** width of the gap between columns

### format

Apply Css to a Document

**Parameters**

-   `contents` **[Contents](#contents)** 

Returns **[Promise](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise)** 

### count

Count number of pages

**Parameters**

-   `totalLength` **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** 
-   `pageLength` **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** 

Returns **{spreads: [Number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number), pages: [Number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)}** 

## Themes

Themes to apply to displayed content

**Parameters**

-   `rendition` **[Rendition](#rendition)** 

### register

Add themes to be used by a rendition

**Examples**

```javascript
themes.register("light", "http://example.com/light.css")
```

```javascript
themes.register("light", { "body": { "color": "purple"}})
```

```javascript
themes.register({ "light" : {...}, "dark" : {...}})
```

### default

Add a default theme to be used by a rendition

**Parameters**

-   `theme` **([object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object) \| [string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String))** 

**Examples**

```javascript
themes.register("http://example.com/default.css")
```

```javascript
themes.register({ "body": { "color": "purple"}})
```

### registerThemes

Register themes object

**Parameters**

-   `themes` **[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)** 

### registerUrl

Register a url

**Parameters**

-   `name` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 
-   `input` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 

### registerRules

Register rule

**Parameters**

-   `name` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 
-   `rules` **[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)** 

### select

Select a theme

**Parameters**

-   `name` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 

### update

Update a theme

**Parameters**

-   `name` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 

### inject

Inject all themes into contents

**Parameters**

-   `contents` **[Contents](#contents)** 

### add

Add Theme to contents

**Parameters**

-   `name` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 
-   `contents` **[Contents](#contents)** 

### override

Add override

**Parameters**

-   `name` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 
-   `value` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 
-   `priority` **[boolean](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** 

### overrides

Add all overrides

**Parameters**

-   `contents`  
-   `content` **Content** 

### fontSize

Adjust the font size of a rendition

**Parameters**

-   `size` **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** 

### font

Adjust the font-family of a rendition

**Parameters**

-   `f` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 

## Annotations

Handles managing adding & removing Annotations

**Parameters**

-   `rendition` **[Rendition](#rendition)** 

### add

Add an annotation to store

**Parameters**

-   `type` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** Type of annotation to add: "highlight", "underline", "mark"
-   `cfiRange` **[EpubCFI](#epubcfi)** EpubCFI range to attach annotation to
-   `data` **[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)** Data to assign to annotation
-   `cb` **[function](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function)?** Callback after annotation is added
-   `className` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** CSS class to assign to annotation
-   `styles` **[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)** CSS styles to assign to annotation

Returns **[Annotation](#annotation)** annotation

### remove

Remove an annotation from store

**Parameters**

-   `cfiRange` **[EpubCFI](#epubcfi)** EpubCFI range the annotation is attached to
-   `type` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** Type of annotation to add: "highlight", "underline", "mark"

### highlight

Add a highlight to the store

**Parameters**

-   `cfiRange` **[EpubCFI](#epubcfi)** EpubCFI range to attach annotation to
-   `data` **[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)** Data to assign to annotation
-   `cb` **[function](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function)** Callback after annotation is added
-   `className` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** CSS class to assign to annotation
-   `styles` **[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)** CSS styles to assign to annotation

### underline

Add a underline to the store

**Parameters**

-   `cfiRange` **[EpubCFI](#epubcfi)** EpubCFI range to attach annotation to
-   `data` **[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)** Data to assign to annotation
-   `cb` **[function](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function)** Callback after annotation is added
-   `className` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** CSS class to assign to annotation
-   `styles` **[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)** CSS styles to assign to annotation

### mark

Add a mark to the store

**Parameters**

-   `cfiRange` **[EpubCFI](#epubcfi)** EpubCFI range to attach annotation to
-   `data` **[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)** Data to assign to annotation
-   `cb` **[function](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function)** Callback after annotation is added

### each

iterate over annotations in the store

### show

[Not Implemented] Show annotations

### hide

[Not Implemented] Hide annotations

## Annotation

Annotation object

**Parameters**

-   `$0` **[Object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)** 
    -   `$0.type`  
    -   `$0.cfiRange`  
    -   `$0.data`  
    -   `$0.sectionIndex`  
    -   `$0.cb`  
    -   `$0.className`  
    -   `$0.styles`  
-   `options` **[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)** 
    -   `options.type` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** Type of annotation to add: "highlight", "underline", "mark"
    -   `options.cfiRange` **[EpubCFI](#epubcfi)** EpubCFI range to attach annotation to
    -   `options.data` **[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)** Data to assign to annotation
    -   `options.sectionIndex` **int** Index in the Spine of the Section annotation belongs to
    -   `options.cb` **[function](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function)?** Callback after annotation is added
-   `className` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** CSS class to assign to annotation
-   `styles` **[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)** CSS styles to assign to annotation

Returns **[Annotation](#annotation)** annotation

### update

Update stored data

**Parameters**

-   `data` **[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)** 

### attach

Add to a view

**Parameters**

-   `view` **View** 

### detach

Remove from a view

**Parameters**

-   `view` **View** 

### text

[Not Implemented] Get text of an annotation

## EpubCFI

Parsing and creation of EpubCFIs: <http://www.idpf.org/epub/linking/cfi/epub-cfi.html>

Implements:

-   Character Offset: epubcfi(/6/4[chap01ref]!/4[body01]/10[para05]/2/1:3)
-   Simple Ranges : epubcfi(/6/4[chap01ref]!/4[body01]/10[para05],/2/1:1,/3:4)

Does Not Implement:

-   Temporal Offset (~)
-   Spatial Offset (@)
-   Temporal-Spatial Offset (~ + @)
-   Text Location Assertion (\[)

**Parameters**

-   `cfiFrom` **([string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String) \| [Range](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Input) \| [Node](https://developer.mozilla.org/en-US/docs/Web/API/Node/nextSibling))?** 
-   `base` **([string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String) \| [object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object))?** 
-   `ignoreClass` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)?** class to ignore when parsing DOM

### parse

Parse a cfi string to a CFI object representation

**Parameters**

-   `cfiStr` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 

Returns **[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)** cfi

### toString

Convert CFI to a epubcfi(...) string

Returns **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** epubcfi

### compare

Compare which of two CFIs is earlier in the text

**Parameters**

-   `cfiOne`  
-   `cfiTwo`  

Returns **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** First is earlier = -1, Second is earlier = 1, They are equal = 0

### fromRange

Create a CFI object from a Range

**Parameters**

-   `range` **[Range](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Input)** 
-   `base` **([string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String) \| [object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object))** 
-   `ignoreClass` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)?** 

Returns **[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)** cfi

### fromNode

Create a CFI object from a Node

**Parameters**

-   `anchor` **[Node](https://developer.mozilla.org/en-US/docs/Web/API/Node/nextSibling)** 
-   `base` **([string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String) \| [object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object))** 
-   `ignoreClass` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)?** 

Returns **[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)** cfi

### toRange

Creates a DOM range representing a CFI

**Parameters**

-   `_doc` **[document](https://developer.mozilla.org/en-US/docs/Web/JavaScript)** document referenced in the base
-   `ignoreClass` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)?** 

Returns **[Range](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Input)** 

### isCfiString

Check if a string is wrapped with "epubcfi()"

**Parameters**

-   `str` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 

Returns **[boolean](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** 

### collapse

Collapse a CFI Range to a single CFI Position

**Parameters**

-   `toStart` **[boolean](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean)**  (optional, default `false`)

## Contents

Handles DOM manipulation, queries and events for View contents

**Parameters**

-   `doc` **[document](https://developer.mozilla.org/en-US/docs/Web/JavaScript)** Document
-   `content` **[element](https://developer.mozilla.org/en-US/docs/Web/API/Element)** Parent Element (typically Body)
-   `cfiBase` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** Section component of CFIs
-   `sectionIndex` **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** Index in Spine of Conntent's Section

### width

Get or Set width

**Parameters**

-   `w` **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)?** 

Returns **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** width

### height

Get or Set height

**Parameters**

-   `h` **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)?** 

Returns **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** height

### contentWidth

Get or Set width of the contents

**Parameters**

-   `w` **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)?** 

Returns **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** width

### contentHeight

Get or Set height of the contents

**Parameters**

-   `h` **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)?** 

Returns **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** height

### textWidth

Get the width of the text using Range

Returns **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** width

### textHeight

Get the height of the text using Range

Returns **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** height

### scrollWidth

Get documentElement scrollWidth

Returns **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** width

### scrollHeight

Get documentElement scrollHeight

Returns **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** height

### overflow

Set overflow css style of the contents

**Parameters**

-   `overflow` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)?** 

### overflowX

Set overflowX css style of the documentElement

**Parameters**

-   `overflow` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)?** 

### overflowY

Set overflowY css style of the documentElement

**Parameters**

-   `overflow` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)?** 

### css

Set Css styles on the contents element (typically Body)

**Parameters**

-   `property` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 
-   `value` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 
-   `priority` **[boolean](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean)?** set as "important"

### viewport

Get or Set the viewport element

**Parameters**

-   `options` **[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)?** 
    -   `options.width` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)?** 
    -   `options.height` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)?** 
    -   `options.scale` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)?** 
    -   `options.minimum` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)?** 
    -   `options.maximum` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)?** 
    -   `options.scalable` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)?** 

### root

Get the documentElement

Returns **[element](https://developer.mozilla.org/en-US/docs/Web/API/Element)** documentElement

### locationOf

Get the location offset of a EpubCFI or an #id

**Parameters**

-   `target` **([string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String) \| [EpubCFI](#epubcfi))** 
-   `ignoreClass` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)?** for the cfi

### addStylesheet

Append a stylesheet link to the document head

**Parameters**

-   `src` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** url

### addStylesheetRules

Append stylesheet rules to a generate stylesheet
Array: <https://developer.mozilla.org/en-US/docs/Web/API/CSSStyleSheet/insertRule>
Object: <https://github.com/desirable-objects/json-to-css>

**Parameters**

-   `rules` **([array](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array) \| [object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object))** 

### addScript

Append a script tag to the document head

**Parameters**

-   `src` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** url

Returns **[Promise](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise)** loaded

### addClass

Add a class to the contents container

**Parameters**

-   `className` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 

### removeClass

Remove a class from the contents container

**Parameters**

-   `className`  
-   `removeClass` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 

### range

Get a Dom Range from EpubCFI

**Parameters**

-   `_cfi` **[EpubCFI](#epubcfi)** 
-   `ignoreClass` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)?** 

Returns **[Range](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Input)** range

### cfiFromRange

Get an EpubCFI from a Dom Range

**Parameters**

-   `range` **[Range](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Input)** 
-   `ignoreClass` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)?** 

Returns **[EpubCFI](#epubcfi)** cfi

### cfiFromNode

Get an EpubCFI from a Dom node

**Parameters**

-   `node` **[node](https://developer.mozilla.org/en-US/docs/Web/API/Node/nextSibling)** 
-   `ignoreClass` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)?** 

Returns **[EpubCFI](#epubcfi)** cfi

### size

Size the contents to a given width and height

**Parameters**

-   `width` **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)?** 
-   `height` **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)?** 

### columns

Apply columns to the contents for pagination

**Parameters**

-   `width` **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** 
-   `height` **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** 
-   `columnWidth` **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** 
-   `gap` **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** 

### scaler

Scale contents from center

**Parameters**

-   `scale` **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** 
-   `offsetX` **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** 
-   `offsetY` **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** 

### fit

Fit contents into a fixed width and height

**Parameters**

-   `width` **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** 
-   `height` **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** 

### direction

Set the direction of the text

**Parameters**

-   `dir` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** "rtl" | "ltr" (optional, default `"ltr"`)

### writingMode

Set the writingMode of the text

**Parameters**

-   `mode` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** "horizontal-tb" | "vertical-rl" | "vertical-lr" (optional, default `"horizontal-tb"`)

### listenedEvents

Get DOM events that are listened for and passed along

## Mapping

Map text locations to CFI ranges

**Parameters**

-   `layout` **[Layout](#layout)** Layout to apply
-   `direction` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** Text direction (optional, default `"ltr"`)
-   `axis` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** vertical or horizontal axis (optional, default `"horizontal"`)
-   `dev` **[boolean](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean)?** toggle developer highlighting

### section

Find CFI pairs for entire section at once

**Parameters**

-   `view`  

### page

Find CFI pairs for a page

**Parameters**

-   `contents` **[Contents](#contents)** Contents from view
-   `cfiBase` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** string of the base for a cfi
-   `start` **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** position to start at
-   `end` **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** position to end at

### axis

Set the axis for mapping

**Parameters**

-   `axis` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** horizontal | vertical

Returns **[boolean](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** is it horizontal?

## Core

Core Utilities and Helpers

### requestAnimationFrame

Vendor prefixed requestAnimationFrame

Returns **[function](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function)** requestAnimationFrame

### uuid

Generates a UUID
based on: <http://stackoverflow.com/questions/105034/how-to-create-a-guid-uuid-in-javascript>

Returns **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** uuid

### documentHeight

Gets the height of a document

Returns **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** height

### isElement

Checks if a node is an element

**Parameters**

-   `obj` **[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)** 

Returns **[boolean](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** 

### isNumber

**Parameters**

-   `n` **any** 

Returns **[boolean](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** 

### isFloat

**Parameters**

-   `n` **any** 

Returns **[boolean](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** 

### prefixed

Get a prefixed css property

**Parameters**

-   `unprefixed` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 

Returns **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 

### defaults

Apply defaults to an object

**Parameters**

-   `obj` **[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)** 

Returns **[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)** 

### extend

Extend properties of an object

**Parameters**

-   `target` **[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)** 

Returns **[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)** 

### insert

Fast quicksort insert for sorted array -- based on:
 <http://stackoverflow.com/questions/1344500/efficient-way-to-insert-a-number-into-a-sorted-array-of-numbers>

**Parameters**

-   `item` **any** 
-   `array` **[array](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array)** 
-   `compareFunction` **[function](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function)?** 

Returns **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** location (in array)

### locationOf

Finds where something would fit into a sorted array

**Parameters**

-   `item` **any** 
-   `array` **[array](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array)** 
-   `compareFunction` **[function](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function)?** 
-   `_start` **[function](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function)?** 
-   `_end` **[function](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function)?** 

Returns **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** location (in array)

### indexOfSorted

Finds index of something in a sorted array
Returns -1 if not found

**Parameters**

-   `item` **any** 
-   `array` **[array](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array)** 
-   `compareFunction` **[function](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function)?** 
-   `_start` **[function](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function)?** 
-   `_end` **[function](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function)?** 

Returns **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** index (in array) or -1

### bounds

Find the bounds of an element
taking padding and margin into account

**Parameters**

-   `el` **[element](https://developer.mozilla.org/en-US/docs/Web/API/Element)** 

Returns **{width: [Number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number), height: [Number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)}** 

### borders

Find the bounds of an element
taking padding, margin and borders into account

**Parameters**

-   `el` **[element](https://developer.mozilla.org/en-US/docs/Web/API/Element)** 

Returns **{width: [Number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number), height: [Number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)}** 

### nodeBounds

Find the bounds of any node
allows for getting bounds of text nodes by wrapping them in a range

**Parameters**

-   `node` **[node](https://developer.mozilla.org/en-US/docs/Web/API/Node/nextSibling)** 

Returns **BoundingClientRect** 

### windowBounds

Find the equivalent of getBoundingClientRect of a browser window

Returns **{width: [Number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number), height: [Number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number), top: [Number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number), left: [Number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number), right: [Number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number), bottom: [Number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)}** 

### indexOfNode

Gets the index of a node in its parent

**Parameters**

-   `node` **[Node](https://developer.mozilla.org/en-US/docs/Web/API/Node/nextSibling)** 
-   `typeId` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 

Returns **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** index

### indexOfTextNode

Gets the index of a text node in its parent

**Parameters**

-   `textNode` **[node](https://developer.mozilla.org/en-US/docs/Web/API/Node/nextSibling)** 

Returns **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** index

### indexOfElementNode

Gets the index of an element node in its parent

**Parameters**

-   `elementNode` **[element](https://developer.mozilla.org/en-US/docs/Web/API/Element)** 

Returns **[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)** index

### isXml

Check if extension is xml

**Parameters**

-   `ext` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 

Returns **[boolean](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** 

### createBlob

Create a new blob

**Parameters**

-   `content` **any** 
-   `mime` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 

Returns **[Blob](https://developer.mozilla.org/en-US/docs/Web/API/Blob)** 

### createBlobUrl

Create a new blob url

**Parameters**

-   `content` **any** 
-   `mime` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 

Returns **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** url

### revokeBlobUrl

Remove a blob url

**Parameters**

-   `url` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 

### createBase64Url

Create a new base64 encoded url

**Parameters**

-   `content` **any** 
-   `mime` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 

Returns **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** url

### type

Get type of an object

**Parameters**

-   `obj` **[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)** 

Returns **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** type

### parse

Parse xml (or html) markup

**Parameters**

-   `markup` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 
-   `mime` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 
-   `forceXMLDom` **[boolean](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** force using xmlDom to parse instead of native parser

Returns **[document](https://developer.mozilla.org/en-US/docs/Web/JavaScript)** document

### qs

querySelector polyfill

**Parameters**

-   `el` **[element](https://developer.mozilla.org/en-US/docs/Web/API/Element)** 
-   `sel` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** selector string

Returns **[element](https://developer.mozilla.org/en-US/docs/Web/API/Element)** element

### qsa

querySelectorAll polyfill

**Parameters**

-   `el` **[element](https://developer.mozilla.org/en-US/docs/Web/API/Element)** 
-   `sel` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** selector string

Returns **[Array](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array)&lt;[element](https://developer.mozilla.org/en-US/docs/Web/API/Element)>** elements

### qsp

querySelector by property

**Parameters**

-   `el` **[element](https://developer.mozilla.org/en-US/docs/Web/API/Element)** 
-   `sel` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** selector string
-   `props` **[Array](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array)&lt;[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)>** 

Returns **[Array](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array)&lt;[element](https://developer.mozilla.org/en-US/docs/Web/API/Element)>** elements

### sprint

Sprint through all text nodes in a document

**Parameters**

-   `root` **[element](https://developer.mozilla.org/en-US/docs/Web/API/Element)** element to start with
-   `func` **[function](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function)** function to run on each element

### treeWalker

Create a treeWalker

**Parameters**

-   `root` **[element](https://developer.mozilla.org/en-US/docs/Web/API/Element)** element to start with
-   `func` **[function](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function)** function to run on each element
-   `filter` **([function](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function) \| [object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object))** function or object to filter with

### walk

**Parameters**

-   `node` **[node](https://developer.mozilla.org/en-US/docs/Web/API/Node/nextSibling)** 
-   `callback`  
-   `return` **callback** false for continue,true for break inside callback

### blob2base64

Convert a blob to a base64 encoded string

**Parameters**

-   `blob` **Blog** 

Returns **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 

### defer

Creates a new pending promise and provides methods to resolve or reject it.
From: <https://developer.mozilla.org/en-US/docs/Mozilla/JavaScript_code_modules/Promise.jsm/Deferred#backwards_forwards_compatible>

### querySelectorByType

querySelector with filter by epub type

**Parameters**

-   `html` **[element](https://developer.mozilla.org/en-US/docs/Web/API/Element)** 
-   `element` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** element type to find
-   `type` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** epub type to find

Returns **[Array](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array)&lt;[element](https://developer.mozilla.org/en-US/docs/Web/API/Element)>** elements

### findChildren

Find direct descendents of an element

**Parameters**

-   `el` **[element](https://developer.mozilla.org/en-US/docs/Web/API/Element)** 

Returns **[Array](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array)&lt;[element](https://developer.mozilla.org/en-US/docs/Web/API/Element)>** children

### parents

Find all parents (ancestors) of an element

**Parameters**

-   `node` **[element](https://developer.mozilla.org/en-US/docs/Web/API/Element)** 

Returns **[Array](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array)&lt;[element](https://developer.mozilla.org/en-US/docs/Web/API/Element)>** parents

### filterChildren

Find all direct descendents of a specific type

**Parameters**

-   `el` **[element](https://developer.mozilla.org/en-US/docs/Web/API/Element)** 
-   `nodeName` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 
-   `single` **[boolean](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean)?** 

Returns **[Array](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array)&lt;[element](https://developer.mozilla.org/en-US/docs/Web/API/Element)>** children

### getParentByTagName

Filter all parents (ancestors) with tag name

**Parameters**

-   `node` **[element](https://developer.mozilla.org/en-US/docs/Web/API/Element)** 
-   `tagname` **[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)** 

Returns **[Array](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array)&lt;[element](https://developer.mozilla.org/en-US/docs/Web/API/Element)>** parents

### RangeObject

Lightweight Polyfill for DOM Range
