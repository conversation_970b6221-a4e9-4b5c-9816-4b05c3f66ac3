/**
 * Document Parser Service
 * Central service for parsing and managing documents across all supported formats
 */

import RNFS from 'react-native-fs';
import {
  DocumentFormat,
  DocumentParser,
  DocumentParseResult,
  DocumentParseOptions,
  DocumentValidationResult,
  DocumentError,
  DocumentProcessingError,
  DocumentMetadata,
} from '../../types/document';

// Import specific parsers
import { EPUBParser } from './parsers/EPUBParser';
import { PDFParser } from './parsers/PDFParser';
import { TextParser } from './parsers/TextParser';
import { RTFParser } from './parsers/RTFParser';
import { MarkdownParser } from './parsers/MarkdownParser';

export class DocumentParserService {
  private parsers: Map<DocumentFormat, DocumentParser> = new Map();
  private mimeTypeMap: Map<string, DocumentFormat> = new Map();

  constructor() {
    this.initializeParsers();
    this.initializeMimeTypeMap();
  }

  private initializeParsers(): void {
    // Register all document parsers
    const epubParser = new EPUBParser();
    const pdfParser = new PDFParser();
    const textParser = new TextParser();
    const rtfParser = new RTFParser();
    const markdownParser = new MarkdownParser();

    // Register parsers for their supported formats
    epubParser.supportedFormats.forEach(format => {
      this.parsers.set(format, epubParser);
    });

    pdfParser.supportedFormats.forEach(format => {
      this.parsers.set(format, pdfParser);
    });

    textParser.supportedFormats.forEach(format => {
      this.parsers.set(format, textParser);
    });

    rtfParser.supportedFormats.forEach(format => {
      this.parsers.set(format, rtfParser);
    });

    markdownParser.supportedFormats.forEach(format => {
      this.parsers.set(format, markdownParser);
    });
  }

  private initializeMimeTypeMap(): void {
    // Map MIME types to document formats
    this.mimeTypeMap.set('application/epub+zip', DocumentFormat.EPUB);
    this.mimeTypeMap.set('application/pdf', DocumentFormat.PDF);
    this.mimeTypeMap.set('text/plain', DocumentFormat.TXT);
    this.mimeTypeMap.set('application/rtf', DocumentFormat.RTF);
    this.mimeTypeMap.set('text/rtf', DocumentFormat.RTF);
    this.mimeTypeMap.set('application/vnd.openxmlformats-officedocument.wordprocessingml.document', DocumentFormat.DOCX);
    this.mimeTypeMap.set('application/msword', DocumentFormat.DOC);
    this.mimeTypeMap.set('text/csv', DocumentFormat.CSV);
    this.mimeTypeMap.set('text/markdown', DocumentFormat.MD);
    this.mimeTypeMap.set('text/html', DocumentFormat.HTML);
  }

  /**
   * Detect document format from file path and content
   */
  async detectFormat(filePath: string): Promise<DocumentFormat | null> {
    try {
      // First, try to detect by file extension
      const extension = this.getFileExtension(filePath).toLowerCase();
      const formatByExtension = this.getFormatByExtension(extension);

      if (formatByExtension) {
        return formatByExtension;
      }

      // If extension detection fails, try MIME type detection
      const stats = await RNFS.stat(filePath);
      if (stats.isFile()) {
        // Read first few bytes to detect file signature
        const header = await RNFS.read(filePath, 16, 0, 'base64');
        const format = this.detectFormatBySignature(header);
        if (format) {
          return format;
        }
      }

      return null;
    } catch (error) {
      console.error('Error detecting document format:', error);
      return null;
    }
  }

  private getFileExtension(filePath: string): string {
    const lastDotIndex = filePath.lastIndexOf('.');
    return lastDotIndex !== -1 ? filePath.substring(lastDotIndex + 1) : '';
  }

  private getFormatByExtension(extension: string): DocumentFormat | null {
    const extensionMap: Record<string, DocumentFormat> = {
      epub: DocumentFormat.EPUB,
      pdf: DocumentFormat.PDF,
      txt: DocumentFormat.TXT,
      rtf: DocumentFormat.RTF,
      docx: DocumentFormat.DOCX,
      doc: DocumentFormat.DOC,
      csv: DocumentFormat.CSV,
      md: DocumentFormat.MD,
      markdown: DocumentFormat.MD,
      html: DocumentFormat.HTML,
      htm: DocumentFormat.HTML,
    };

    return extensionMap[extension] || null;
  }

  private detectFormatBySignature(headerBase64: string): DocumentFormat | null {
    // Convert base64 to hex for signature detection
    const buffer = Buffer.from(headerBase64, 'base64');
    const hex = buffer.toString('hex').toUpperCase();

    // File signatures (magic numbers)
    const signatures: Record<string, DocumentFormat> = {
      '504B0304': DocumentFormat.EPUB, // ZIP signature (EPUB/DOCX are ZIP-based, EPUB takes precedence)
      '25504446': DocumentFormat.PDF,  // %PDF
      'D0CF11E0': DocumentFormat.DOC,  // MS Office legacy
    };

    for (const [signature, format] of Object.entries(signatures)) {
      if (hex.startsWith(signature)) {
        return format;
      }
    }

    return null;
  }

  /**
   * Validate document file
   */
  async validateDocument(filePath: string): Promise<DocumentValidationResult> {
    const result: DocumentValidationResult = {
      isValid: false,
      fileSize: 0,
      errors: [],
      warnings: [],
    };

    try {
      // Check if file exists
      const exists = await RNFS.exists(filePath);
      if (!exists) {
        result.errors.push('File does not exist');
        return result;
      }

      // Get file stats
      const stats = await RNFS.stat(filePath);
      result.fileSize = stats.size;

      // Check file size limits (100MB max)
      const maxFileSize = 100 * 1024 * 1024; // 100MB
      if (stats.size > maxFileSize) {
        result.errors.push(`File size (${Math.round(stats.size / 1024 / 1024)}MB) exceeds maximum limit (100MB)`);
        return result;
      }

      // Detect format
      const format = await this.detectFormat(filePath);
      if (!format) {
        result.errors.push('Unsupported document format');
        return result;
      }

      result.format = format;

      // Get parser for format
      const parser = this.parsers.get(format);
      if (!parser) {
        result.errors.push(`No parser available for format: ${format}`);
        return result;
      }

      // Validate with specific parser
      const isValid = await parser.validateDocument(filePath);
      result.isValid = isValid;

      if (!isValid) {
        result.errors.push(`Document validation failed for format: ${format}`);
      }

      return result;
    } catch (error) {
      result.errors.push(`Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return result;
    }
  }

  /**
   * Parse document with specified options
   */
  async parseDocument(
    filePath: string,
    options: DocumentParseOptions = {
      extractText: true,
      extractMetadata: true,
      extractTableOfContents: true,
      generateThumbnail: false,
    },
  ): Promise<DocumentParseResult> {
    try {
      // Validate document first
      const validation = await this.validateDocument(filePath);
      if (!validation.isValid) {
        return {
          success: false,
          error: `Document validation failed: ${validation.errors.join(', ')}`,
        };
      }

      const format = validation.format!;
      const parser = this.parsers.get(format);

      if (!parser) {
        throw new DocumentProcessingError(
          DocumentError.UNSUPPORTED_FORMAT,
          `No parser available for format: ${format}`,
          filePath,
        );
      }

      // Parse document
      const result = await parser.parse(filePath, options);
      return result;
    } catch (error) {
      console.error('Error parsing document:', error);
      
      if (error instanceof DocumentProcessingError) {
        return {
          success: false,
          error: error.message,
        };
      }

      return {
        success: false,
        error: `Parsing failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }

  /**
   * Extract metadata only (faster than full parsing)
   */
  async extractMetadata(filePath: string): Promise<DocumentMetadata | null> {
    try {
      const format = await this.detectFormat(filePath);
      if (!format) {
        return null;
      }

      const parser = this.parsers.get(format);
      if (!parser) {
        return null;
      }

      return await parser.extractMetadata(filePath);
    } catch (error) {
      console.error('Error extracting metadata:', error);
      return null;
    }
  }

  /**
   * Get supported formats
   */
  getSupportedFormats(): DocumentFormat[] {
    return Array.from(this.parsers.keys());
  }

  /**
   * Check if format is supported
   */
  isFormatSupported(format: DocumentFormat): boolean {
    return this.parsers.has(format);
  }

  /**
   * Get parser for specific format
   */
  getParser(format: DocumentFormat): DocumentParser | undefined {
    return this.parsers.get(format);
  }
}
