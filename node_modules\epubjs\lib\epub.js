"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;

var _book = _interopRequireDefault(require("./book"));

var _rendition = _interopRequireDefault(require("./rendition"));

var _epubcfi = _interopRequireDefault(require("./epubcfi"));

var _contents = _interopRequireDefault(require("./contents"));

var utils = _interopRequireWildcard(require("./utils/core"));

var _constants = require("./utils/constants");

var _iframe = _interopRequireDefault(require("./managers/views/iframe"));

var _default2 = _interopRequireDefault(require("./managers/default"));

var _continuous = _interopRequireDefault(require("./managers/continuous"));

function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }

function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

/**
 * Creates a new Book
 * @param {string|ArrayBuffer} url URL, Path or ArrayBuffer
 * @param {object} options to pass to the book
 * @returns {Book} a new Book object
 * @example ePub("/path/to/book.epub", {})
 */
function ePub(url, options) {
  return new _book.default(url, options);
}

ePub.VERSION = _constants.EPUBJS_VERSION;

if (typeof global !== "undefined") {
  global.EPUBJS_VERSION = _constants.EPUBJS_VERSION;
}

ePub.Book = _book.default;
ePub.Rendition = _rendition.default;
ePub.Contents = _contents.default;
ePub.CFI = _epubcfi.default;
ePub.utils = utils;
var _default = ePub;
exports.default = _default;