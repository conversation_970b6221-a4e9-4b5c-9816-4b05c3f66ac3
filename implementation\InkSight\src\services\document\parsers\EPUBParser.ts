/**
 * EPUB Parser Implementation
 * Handles EPUB2 and EPUB3 format parsing using epub.js
 */

import ePub from 'epubjs';
import RNFS from 'react-native-fs';
import {
  DocumentFormat,
  DocumentParser,
  DocumentParseResult,
  DocumentParseOptions,
  DocumentMetadata,
  DocumentContent,
  DocumentChapter,
  DocumentTableOfContents,
  ParsedDocument,
  DocumentError,
  DocumentProcessingError,
} from '../../../types/document';

export class EPUBParser implements DocumentParser {
  supportedFormats: DocumentFormat[] = [DocumentFormat.EPUB];

  canParse(filePath: string, mimeType?: string): boolean {
    const extension = filePath.toLowerCase().split('.').pop();
    return extension === 'epub' || mimeType === 'application/epub+zip';
  }

  async validateDocument(filePath: string): Promise<boolean> {
    try {
      // Check if file exists and is readable
      const exists = await RNFS.exists(filePath);
      if (!exists) {
        return false;
      }

      // Try to open the EPUB file
      const book = ePub(filePath);
      await book.ready;
      
      // Basic validation - check if it has required components
      const hasMetadata = book.packaging?.metadata !== undefined;
      const hasManifest = book.packaging?.manifest !== undefined;
      const hasSpine = book.packaging?.spine !== undefined;

      return hasMetadata && hasManifest && hasSpine;
    } catch (error) {
      console.error('EPUB validation error:', error);
      return false;
    }
  }

  async parse(filePath: string, options: DocumentParseOptions = {
    extractText: true,
    extractMetadata: true,
    extractTableOfContents: true,
    generateThumbnail: false,
  }): Promise<DocumentParseResult> {
    try {
      const book = ePub(filePath);
      await book.ready;

      const metadata = await this.extractMetadataFromBook(book, filePath);
      let content: DocumentContent = { text: '' };
      let tableOfContents: DocumentTableOfContents | undefined;

      // Extract table of contents if requested
      if (options.extractTableOfContents) {
        tableOfContents = await this.extractTableOfContents(book);
      }

      // Extract content if requested
      if (options.extractText) {
        content = await this.extractContent(book, options);
      }

      const parsedDocument: ParsedDocument = {
        metadata,
        content,
        tableOfContents,
      };

      return {
        success: true,
        document: parsedDocument,
      };
    } catch (error) {
      console.error('EPUB parsing error:', error);
      return {
        success: false,
        error: `Failed to parse EPUB: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }

  async extractMetadata(filePath: string): Promise<DocumentMetadata> {
    try {
      const book = ePub(filePath);
      await book.ready;
      return await this.extractMetadataFromBook(book, filePath);
    } catch (error) {
      throw new DocumentProcessingError(
        DocumentError.PARSING_ERROR,
        `Failed to extract EPUB metadata: ${error instanceof Error ? error.message : 'Unknown error'}`,
        filePath,
        error instanceof Error ? error : undefined,
      );
    }
  }

  private async extractMetadataFromBook(book: unknown, filePath: string): Promise<DocumentMetadata> {
    const stats = await RNFS.stat(filePath);
    const metadata = (book as { packaging?: { metadata?: Record<string, unknown> } }).packaging?.metadata || {};

    // Generate unique ID based on file path and modification time
    const id = this.generateDocumentId(filePath, stats.mtime);

    // Extract cover image if available
    let coverImageUri: string | undefined;
    try {
      const coverUrl = await (book as { coverUrl: () => Promise<string> }).coverUrl();
      if (coverUrl) {
        coverImageUri = coverUrl;
      }
    } catch (error) {
      // Cover extraction failed, continue without cover
      console.warn('Failed to extract EPUB cover:', error);
    }

    return {
      id,
      title: metadata.title || this.getFileNameWithoutExtension(filePath),
      author: this.extractAuthor(metadata),
      publisher: metadata.publisher,
      publishedDate: metadata.pubdate,
      description: metadata.description,
      language: metadata.language || 'en',
      isbn: metadata.identifier,
      fileSize: stats.size,
      format: DocumentFormat.EPUB,
      mimeType: 'application/epub+zip',
      createdAt: new Date(stats.ctime),
      modifiedAt: new Date(stats.mtime),
      readingProgress: 0,
      totalReadingTime: 0,
      coverImageUri,
    };
  }

  private extractAuthor(metadata: Record<string, unknown>): string | undefined {
    const creator = metadata.creator;
    if (creator) {
      if (typeof creator === 'string') {
        return creator;
      }
      if (Array.isArray(creator)) {
        return creator.join(', ');
      }
      if (typeof creator === 'object' && creator !== null && 'name' in creator) {
        return String((creator as { name: unknown }).name);
      }
    }
    return undefined;
  }

  private async extractTableOfContents(book: unknown): Promise<DocumentTableOfContents> {
    try {
      const navigation = await book.loaded.navigation;
      const chapters: DocumentChapter[] = [];

      const processNavItem = (item: unknown, level: number = 0): void => {
        const chapter: DocumentChapter = {
          id: item.id || `chapter-${chapters.length}`,
          title: item.label || `Chapter ${chapters.length + 1}`,
          href: item.href,
          level,
        };

        chapters.push(chapter);

        // Process sub-chapters recursively
        if (item && typeof item === 'object' && 'subitems' in item && Array.isArray(item.subitems)) {
          item.subitems.forEach((subitem: unknown) => {
            processNavItem(subitem, level + 1);
          });
        }
      };

      if (navigation && typeof navigation === 'object' && 'toc' in navigation && Array.isArray(navigation.toc)) {
        navigation.toc.forEach((item: unknown) => {
          processNavItem(item);
        });
      }

      return {
        chapters,
        totalChapters: chapters.length,
      };
    } catch (error) {
      console.warn('Failed to extract EPUB table of contents:', error);
      return {
        chapters: [],
        totalChapters: 0,
      };
    }
  }

  private async extractContent(book: unknown, options: DocumentParseOptions): Promise<DocumentContent> {
    try {
      const spine = (book as { spine: { length: number; get: (index: number) => unknown } }).spine;
      let fullText = '';
      const chapters: DocumentChapter[] = [];

      // Extract text from each spine item
      for (let i = 0; i < spine.length; i++) {
        const spineItem = spine.get(i);
        
        try {
          const doc = await spineItem.load(book.load.bind(book));
          const textContent = this.extractTextFromDocument(doc);
          
          if (textContent.trim()) {
            const wordCount = this.countWords(textContent);

            const chapter: DocumentChapter = {
              id: spineItem.idref || `spine-${i}`,
              title: spineItem.href,
              href: spineItem.href,
              level: 0,
              wordCount,
            };

            chapters.push(chapter);
            fullText += `${textContent}\n\n`;

            // Check if we've reached the maximum text length
            if (options.maxTextLength && fullText.length > options.maxTextLength) {
              fullText = fullText.substring(0, options.maxTextLength);
              break;
            }
          }
        } catch (error) {
          console.warn(`Failed to extract content from spine item ${i}:`, error);
          continue;
        }
      }

      return {
        text: fullText.trim(),
        chapters,
      };
    } catch (error) {
      console.error('Failed to extract EPUB content:', error);
      return {
        text: '',
        chapters: [],
      };
    }
  }

  private extractTextFromDocument(doc: Document): string {
    // Remove script and style elements
    const scripts = doc.querySelectorAll('script, style');
    scripts.forEach(element => element.remove());

    // Get text content
    const textContent = doc.body?.textContent || doc.textContent || '';
    
    // Clean up whitespace
    return textContent.replace(/\s+/g, ' ').trim();
  }

  private countWords(text: string): number {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  private generateDocumentId(filePath: string, modificationTime: string | number): string {
    const fileName = this.getFileNameWithoutExtension(filePath);
    const timestamp = new Date(modificationTime).getTime();
    return `epub-${fileName}-${timestamp}`;
  }

  private getFileNameWithoutExtension(filePath: string): string {
    const fileName = filePath.split('/').pop() || filePath;
    const lastDotIndex = fileName.lastIndexOf('.');
    return lastDotIndex !== -1 ? fileName.substring(0, lastDotIndex) : fileName;
  }
}
