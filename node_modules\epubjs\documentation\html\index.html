<!doctype html>
<html>
<head>
  <meta charset='utf-8' />
  <title>epubjs 0.3.73 | Documentation</title>
  <meta name='viewport' content='width=device-width,initial-scale=1'>
  <link href='assets/bass.css' type='text/css' rel='stylesheet' />
  <link href='assets/style.css' type='text/css' rel='stylesheet' />
  <link href='assets/github.css' type='text/css' rel='stylesheet' />
  <link href='assets/split.css' type='text/css' rel='stylesheet' />
</head>
<body class='documentation m0'>
    <div class='flex'>
      <div id='split-left' class='overflow-auto fs0 height-viewport-100'>
        <div class='py1 px2'>
          <h3 class='mb0 no-anchor'>epubjs</h3>
          <div class='mb1'><code>0.3.73</code></div>
          <input
            placeholder='Filter'
            id='filter-input'
            class='col12 block input'
            type='text' />
          <div id='toc'>
            <ul class='list-reset h5 py1-ul'>
              
                
                <li><a
                  href='#epub'
                  class="">
                  ePub
                  
                </a>
                
                </li>
              
                
                <li><a
                  href='#book'
                  class=" toggle-sibling">
                  Book
                  <span class='icon'>▸</span>
                </a>
                
                <div class='toggle-target display-none'>
                  
                  <ul class='list-reset py1-ul pl1'>
                    <li class='h5'><span>Static members</span></li>
                    
                      <li><a
                        href='#bookopened'
                        class='regular pre-open'>
                        .opened
                      </a></li>
                    
                      <li><a
                        href='#bookspine'
                        class='regular pre-open'>
                        .spine
                      </a></li>
                    
                      <li><a
                        href='#booklocations'
                        class='regular pre-open'>
                        .locations
                      </a></li>
                    
                      <li><a
                        href='#booknavigation'
                        class='regular pre-open'>
                        .navigation
                      </a></li>
                    
                      <li><a
                        href='#bookpagelist'
                        class='regular pre-open'>
                        .pagelist
                      </a></li>
                    
                    </ul>
                  
                  
                    <ul class='list-reset py1-ul pl1'>
                      <li class='h5'><span>Instance members</span></li>
                      
                      <li><a
                        href='#bookopen'
                        class='regular pre-open'>
                        #open
                      </a></li>
                      
                      <li><a
                        href='#bookload'
                        class='regular pre-open'>
                        #load
                      </a></li>
                      
                      <li><a
                        href='#bookresolve'
                        class='regular pre-open'>
                        #resolve
                      </a></li>
                      
                      <li><a
                        href='#bookcanonical'
                        class='regular pre-open'>
                        #canonical
                      </a></li>
                      
                      <li><a
                        href='#booksection'
                        class='regular pre-open'>
                        #section
                      </a></li>
                      
                      <li><a
                        href='#bookrenderto'
                        class='regular pre-open'>
                        #renderTo
                      </a></li>
                      
                      <li><a
                        href='#booksetrequestcredentials'
                        class='regular pre-open'>
                        #setRequestCredentials
                      </a></li>
                      
                      <li><a
                        href='#booksetrequestheaders'
                        class='regular pre-open'>
                        #setRequestHeaders
                      </a></li>
                      
                      <li><a
                        href='#bookcoverurl'
                        class='regular pre-open'>
                        #coverUrl
                      </a></li>
                      
                      <li><a
                        href='#bookgetrange'
                        class='regular pre-open'>
                        #getRange
                      </a></li>
                      
                      <li><a
                        href='#bookkey'
                        class='regular pre-open'>
                        #key
                      </a></li>
                      
                      <li><a
                        href='#bookdestroy'
                        class='regular pre-open'>
                        #destroy
                      </a></li>
                      
                    </ul>
                  
                  
                  
                </div>
                
                </li>
              
                
                <li><a
                  href='#url'
                  class=" toggle-sibling">
                  Url
                  <span class='icon'>▸</span>
                </a>
                
                <div class='toggle-target display-none'>
                  
                  
                    <ul class='list-reset py1-ul pl1'>
                      <li class='h5'><span>Instance members</span></li>
                      
                      <li><a
                        href='#urlpath'
                        class='regular pre-open'>
                        #path
                      </a></li>
                      
                      <li><a
                        href='#urlresolve'
                        class='regular pre-open'>
                        #resolve
                      </a></li>
                      
                      <li><a
                        href='#urlrelative'
                        class='regular pre-open'>
                        #relative
                      </a></li>
                      
                      <li><a
                        href='#urltostring'
                        class='regular pre-open'>
                        #toString
                      </a></li>
                      
                    </ul>
                  
                  
                  
                </div>
                
                </li>
              
                
                <li><a
                  href='#path'
                  class=" toggle-sibling">
                  Path
                  <span class='icon'>▸</span>
                </a>
                
                <div class='toggle-target display-none'>
                  
                  
                    <ul class='list-reset py1-ul pl1'>
                      <li class='h5'><span>Instance members</span></li>
                      
                      <li><a
                        href='#pathparse'
                        class='regular pre-open'>
                        #parse
                      </a></li>
                      
                      <li><a
                        href='#pathisabsolute'
                        class='regular pre-open'>
                        #isAbsolute
                      </a></li>
                      
                      <li><a
                        href='#pathisdirectory'
                        class='regular pre-open'>
                        #isDirectory
                      </a></li>
                      
                      <li><a
                        href='#pathresolve'
                        class='regular pre-open'>
                        #resolve
                      </a></li>
                      
                      <li><a
                        href='#pathrelative'
                        class='regular pre-open'>
                        #relative
                      </a></li>
                      
                      <li><a
                        href='#pathtostring'
                        class='regular pre-open'>
                        #toString
                      </a></li>
                      
                    </ul>
                  
                  
                  
                </div>
                
                </li>
              
                
                <li><a
                  href='#spine'
                  class=" toggle-sibling">
                  Spine
                  <span class='icon'>▸</span>
                </a>
                
                <div class='toggle-target display-none'>
                  
                  
                    <ul class='list-reset py1-ul pl1'>
                      <li class='h5'><span>Instance members</span></li>
                      
                      <li><a
                        href='#spineunpack'
                        class='regular pre-open'>
                        #unpack
                      </a></li>
                      
                      <li><a
                        href='#spineget'
                        class='regular pre-open'>
                        #get
                      </a></li>
                      
                      <li><a
                        href='#spineeach'
                        class='regular pre-open'>
                        #each
                      </a></li>
                      
                      <li><a
                        href='#spinefirst'
                        class='regular pre-open'>
                        #first
                      </a></li>
                      
                      <li><a
                        href='#spinelast'
                        class='regular pre-open'>
                        #last
                      </a></li>
                      
                    </ul>
                  
                  
                  
                </div>
                
                </li>
              
                
                <li><a
                  href='#section'
                  class=" toggle-sibling">
                  Section
                  <span class='icon'>▸</span>
                </a>
                
                <div class='toggle-target display-none'>
                  
                  
                    <ul class='list-reset py1-ul pl1'>
                      <li class='h5'><span>Instance members</span></li>
                      
                      <li><a
                        href='#sectionload'
                        class='regular pre-open'>
                        #load
                      </a></li>
                      
                      <li><a
                        href='#sectionrender'
                        class='regular pre-open'>
                        #render
                      </a></li>
                      
                      <li><a
                        href='#sectionfind'
                        class='regular pre-open'>
                        #find
                      </a></li>
                      
                      <li><a
                        href='#sectionreconcilelayoutsettings'
                        class='regular pre-open'>
                        #reconcileLayoutSettings
                      </a></li>
                      
                      <li><a
                        href='#sectioncfifromrange'
                        class='regular pre-open'>
                        #cfiFromRange
                      </a></li>
                      
                      <li><a
                        href='#sectioncfifromelement'
                        class='regular pre-open'>
                        #cfiFromElement
                      </a></li>
                      
                      <li><a
                        href='#sectionunload'
                        class='regular pre-open'>
                        #unload
                      </a></li>
                      
                    </ul>
                  
                  
                  
                </div>
                
                </li>
              
                
                <li><a
                  href='#locations'
                  class=" toggle-sibling">
                  Locations
                  <span class='icon'>▸</span>
                </a>
                
                <div class='toggle-target display-none'>
                  
                  
                    <ul class='list-reset py1-ul pl1'>
                      <li class='h5'><span>Instance members</span></li>
                      
                      <li><a
                        href='#locationsgenerate'
                        class='regular pre-open'>
                        #generate
                      </a></li>
                      
                      <li><a
                        href='#locationslocationfromcfi'
                        class='regular pre-open'>
                        #locationFromCfi
                      </a></li>
                      
                      <li><a
                        href='#locationspercentagefromcfi'
                        class='regular pre-open'>
                        #percentageFromCfi
                      </a></li>
                      
                      <li><a
                        href='#locationspercentagefromlocation'
                        class='regular pre-open'>
                        #percentageFromLocation
                      </a></li>
                      
                      <li><a
                        href='#locationscfifromlocation'
                        class='regular pre-open'>
                        #cfiFromLocation
                      </a></li>
                      
                      <li><a
                        href='#locationscfifrompercentage'
                        class='regular pre-open'>
                        #cfiFromPercentage
                      </a></li>
                      
                      <li><a
                        href='#locationsload'
                        class='regular pre-open'>
                        #load
                      </a></li>
                      
                      <li><a
                        href='#locationssave'
                        class='regular pre-open'>
                        #save
                      </a></li>
                      
                      <li><a
                        href='#locationscurrentlocation'
                        class='regular pre-open'>
                        #currentLocation
                      </a></li>
                      
                      <li><a
                        href='#locationscurrentlocation'
                        class='regular pre-open'>
                        #currentLocation
                      </a></li>
                      
                      <li><a
                        href='#locationslength'
                        class='regular pre-open'>
                        #length
                      </a></li>
                      
                    </ul>
                  
                  
                  
                </div>
                
                </li>
              
                
                <li><a
                  href='#container'
                  class=" toggle-sibling">
                  Container
                  <span class='icon'>▸</span>
                </a>
                
                <div class='toggle-target display-none'>
                  
                  
                    <ul class='list-reset py1-ul pl1'>
                      <li class='h5'><span>Instance members</span></li>
                      
                      <li><a
                        href='#containerparse'
                        class='regular pre-open'>
                        #parse
                      </a></li>
                      
                    </ul>
                  
                  
                  
                </div>
                
                </li>
              
                
                <li><a
                  href='#packaging'
                  class=" toggle-sibling">
                  Packaging
                  <span class='icon'>▸</span>
                </a>
                
                <div class='toggle-target display-none'>
                  
                  
                    <ul class='list-reset py1-ul pl1'>
                      <li class='h5'><span>Instance members</span></li>
                      
                      <li><a
                        href='#packagingparse'
                        class='regular pre-open'>
                        #parse
                      </a></li>
                      
                      <li><a
                        href='#packagingload'
                        class='regular pre-open'>
                        #load
                      </a></li>
                      
                    </ul>
                  
                  
                  
                </div>
                
                </li>
              
                
                <li><a
                  href='#navigation'
                  class=" toggle-sibling">
                  Navigation
                  <span class='icon'>▸</span>
                </a>
                
                <div class='toggle-target display-none'>
                  
                  
                    <ul class='list-reset py1-ul pl1'>
                      <li class='h5'><span>Instance members</span></li>
                      
                      <li><a
                        href='#navigationparse'
                        class='regular pre-open'>
                        #parse
                      </a></li>
                      
                      <li><a
                        href='#navigationget'
                        class='regular pre-open'>
                        #get
                      </a></li>
                      
                      <li><a
                        href='#navigationlandmark'
                        class='regular pre-open'>
                        #landmark
                      </a></li>
                      
                      <li><a
                        href='#navigationload'
                        class='regular pre-open'>
                        #load
                      </a></li>
                      
                      <li><a
                        href='#navigationforeach'
                        class='regular pre-open'>
                        #forEach
                      </a></li>
                      
                    </ul>
                  
                  
                  
                </div>
                
                </li>
              
                
                <li><a
                  href='#resources'
                  class=" toggle-sibling">
                  Resources
                  <span class='icon'>▸</span>
                </a>
                
                <div class='toggle-target display-none'>
                  
                  
                    <ul class='list-reset py1-ul pl1'>
                      <li class='h5'><span>Instance members</span></li>
                      
                      <li><a
                        href='#resourcescreateurl'
                        class='regular pre-open'>
                        #createUrl
                      </a></li>
                      
                      <li><a
                        href='#resourcesreplacements'
                        class='regular pre-open'>
                        #replacements
                      </a></li>
                      
                      <li><a
                        href='#resourcesrelativeto'
                        class='regular pre-open'>
                        #relativeTo
                      </a></li>
                      
                      <li><a
                        href='#resourcesget'
                        class='regular pre-open'>
                        #get
                      </a></li>
                      
                      <li><a
                        href='#resourcessubstitute'
                        class='regular pre-open'>
                        #substitute
                      </a></li>
                      
                    </ul>
                  
                  
                  
                </div>
                
                </li>
              
                
                <li><a
                  href='#pagelist'
                  class=" toggle-sibling">
                  PageList
                  <span class='icon'>▸</span>
                </a>
                
                <div class='toggle-target display-none'>
                  
                  
                    <ul class='list-reset py1-ul pl1'>
                      <li class='h5'><span>Instance members</span></li>
                      
                      <li><a
                        href='#pagelistparse'
                        class='regular pre-open'>
                        #parse
                      </a></li>
                      
                      <li><a
                        href='#pagelistpagefromcfi'
                        class='regular pre-open'>
                        #pageFromCfi
                      </a></li>
                      
                      <li><a
                        href='#pagelistcfifrompage'
                        class='regular pre-open'>
                        #cfiFromPage
                      </a></li>
                      
                      <li><a
                        href='#pagelistpagefrompercentage'
                        class='regular pre-open'>
                        #pageFromPercentage
                      </a></li>
                      
                      <li><a
                        href='#pagelistpercentagefrompage'
                        class='regular pre-open'>
                        #percentageFromPage
                      </a></li>
                      
                      <li><a
                        href='#pagelistpercentagefromcfi'
                        class='regular pre-open'>
                        #percentageFromCfi
                      </a></li>
                      
                      <li><a
                        href='#pagelistdestroy'
                        class='regular pre-open'>
                        #destroy
                      </a></li>
                      
                    </ul>
                  
                  
                  
                </div>
                
                </li>
              
                
                <li><a
                  href='#archive'
                  class=" toggle-sibling">
                  Archive
                  <span class='icon'>▸</span>
                </a>
                
                <div class='toggle-target display-none'>
                  
                  
                    <ul class='list-reset py1-ul pl1'>
                      <li class='h5'><span>Instance members</span></li>
                      
                      <li><a
                        href='#archiveopen'
                        class='regular pre-open'>
                        #open
                      </a></li>
                      
                      <li><a
                        href='#archiveopenurl'
                        class='regular pre-open'>
                        #openUrl
                      </a></li>
                      
                      <li><a
                        href='#archiverequest'
                        class='regular pre-open'>
                        #request
                      </a></li>
                      
                      <li><a
                        href='#archivegetblob'
                        class='regular pre-open'>
                        #getBlob
                      </a></li>
                      
                      <li><a
                        href='#archivegettext'
                        class='regular pre-open'>
                        #getText
                      </a></li>
                      
                      <li><a
                        href='#archivegetbase64'
                        class='regular pre-open'>
                        #getBase64
                      </a></li>
                      
                      <li><a
                        href='#archivecreateurl'
                        class='regular pre-open'>
                        #createUrl
                      </a></li>
                      
                      <li><a
                        href='#archiverevokeurl'
                        class='regular pre-open'>
                        #revokeUrl
                      </a></li>
                      
                    </ul>
                  
                  
                  
                </div>
                
                </li>
              
                
                <li><a
                  href='#rendition'
                  class=" toggle-sibling">
                  Rendition
                  <span class='icon'>▸</span>
                </a>
                
                <div class='toggle-target display-none'>
                  
                  <ul class='list-reset py1-ul pl1'>
                    <li class='h5'><span>Static members</span></li>
                    
                      <li><a
                        href='#renditionhooks'
                        class='regular pre-open'>
                        .hooks
                      </a></li>
                    
                      <li><a
                        href='#renditionthemes'
                        class='regular pre-open'>
                        .themes
                      </a></li>
                    
                      <li><a
                        href='#renditionannotations'
                        class='regular pre-open'>
                        .annotations
                      </a></li>
                    
                      <li><a
                        href='#renditionlocation'
                        class='regular pre-open'>
                        .location
                      </a></li>
                    
                      <li><a
                        href='#renditionstarted'
                        class='regular pre-open'>
                        .started
                      </a></li>
                    
                    </ul>
                  
                  
                    <ul class='list-reset py1-ul pl1'>
                      <li class='h5'><span>Instance members</span></li>
                      
                      <li><a
                        href='#renditionsetmanager'
                        class='regular pre-open'>
                        #setManager
                      </a></li>
                      
                      <li><a
                        href='#renditionrequiremanager'
                        class='regular pre-open'>
                        #requireManager
                      </a></li>
                      
                      <li><a
                        href='#renditionrequireview'
                        class='regular pre-open'>
                        #requireView
                      </a></li>
                      
                      <li><a
                        href='#renditionstart'
                        class='regular pre-open'>
                        #start
                      </a></li>
                      
                      <li><a
                        href='#renditionattachto'
                        class='regular pre-open'>
                        #attachTo
                      </a></li>
                      
                      <li><a
                        href='#renditiondisplay'
                        class='regular pre-open'>
                        #display
                      </a></li>
                      
                      <li><a
                        href='#renditionmoveto'
                        class='regular pre-open'>
                        #moveTo
                      </a></li>
                      
                      <li><a
                        href='#renditionresize'
                        class='regular pre-open'>
                        #resize
                      </a></li>
                      
                      <li><a
                        href='#renditionclear'
                        class='regular pre-open'>
                        #clear
                      </a></li>
                      
                      <li><a
                        href='#renditionnext'
                        class='regular pre-open'>
                        #next
                      </a></li>
                      
                      <li><a
                        href='#renditionprev'
                        class='regular pre-open'>
                        #prev
                      </a></li>
                      
                      <li><a
                        href='#renditionflow'
                        class='regular pre-open'>
                        #flow
                      </a></li>
                      
                      <li><a
                        href='#renditionlayout'
                        class='regular pre-open'>
                        #layout
                      </a></li>
                      
                      <li><a
                        href='#renditionspread'
                        class='regular pre-open'>
                        #spread
                      </a></li>
                      
                      <li><a
                        href='#renditiondirection'
                        class='regular pre-open'>
                        #direction
                      </a></li>
                      
                      <li><a
                        href='#renditionreportlocation'
                        class='regular pre-open'>
                        #reportLocation
                      </a></li>
                      
                      <li><a
                        href='#renditioncurrentlocation'
                        class='regular pre-open'>
                        #currentLocation
                      </a></li>
                      
                      <li><a
                        href='#renditiondestroy'
                        class='regular pre-open'>
                        #destroy
                      </a></li>
                      
                      <li><a
                        href='#renditiongetrange'
                        class='regular pre-open'>
                        #getRange
                      </a></li>
                      
                      <li><a
                        href='#renditiongetcontents'
                        class='regular pre-open'>
                        #getContents
                      </a></li>
                      
                      <li><a
                        href='#renditionviews'
                        class='regular pre-open'>
                        #views
                      </a></li>
                      
                    </ul>
                  
                  
                  
                    <ul class='list-reset py1-ul pl1'>
                      <li class='h5'>Events</li>
                      
                        <li><a
                          href='#renditioneventstarted'
                          class='regular pre-open'>
                          ⓔ started
                        </a></li>
                      
                        <li><a
                          href='#renditioneventattached'
                          class='regular pre-open'>
                          ⓔ attached
                        </a></li>
                      
                        <li><a
                          href='#renditioneventdisplayed'
                          class='regular pre-open'>
                          ⓔ displayed
                        </a></li>
                      
                        <li><a
                          href='#renditioneventdisplayerror'
                          class='regular pre-open'>
                          ⓔ displayError
                        </a></li>
                      
                        <li><a
                          href='#renditioneventrendered'
                          class='regular pre-open'>
                          ⓔ rendered
                        </a></li>
                      
                        <li><a
                          href='#renditioneventremoved'
                          class='regular pre-open'>
                          ⓔ removed
                        </a></li>
                      
                        <li><a
                          href='#renditioneventresized'
                          class='regular pre-open'>
                          ⓔ resized
                        </a></li>
                      
                        <li><a
                          href='#renditioneventorientationchange'
                          class='regular pre-open'>
                          ⓔ orientationchange
                        </a></li>
                      
                        <li><a
                          href='#renditioneventlocationchanged'
                          class='regular pre-open'>
                          ⓔ locationChanged
                        </a></li>
                      
                        <li><a
                          href='#renditioneventrelocated'
                          class='regular pre-open'>
                          ⓔ relocated
                        </a></li>
                      
                        <li><a
                          href='#renditioneventselected'
                          class='regular pre-open'>
                          ⓔ selected
                        </a></li>
                      
                        <li><a
                          href='#renditioneventmarkclicked'
                          class='regular pre-open'>
                          ⓔ markClicked
                        </a></li>
                      
                    </ul>
                  
                </div>
                
                </li>
              
                
                <li><a
                  href='#hook'
                  class=" toggle-sibling">
                  Hook
                  <span class='icon'>▸</span>
                </a>
                
                <div class='toggle-target display-none'>
                  
                  
                    <ul class='list-reset py1-ul pl1'>
                      <li class='h5'><span>Instance members</span></li>
                      
                      <li><a
                        href='#hookregister'
                        class='regular pre-open'>
                        #register
                      </a></li>
                      
                      <li><a
                        href='#hooktrigger'
                        class='regular pre-open'>
                        #trigger
                      </a></li>
                      
                    </ul>
                  
                  
                  
                </div>
                
                </li>
              
                
                <li><a
                  href='#queue'
                  class=" toggle-sibling">
                  Queue
                  <span class='icon'>▸</span>
                </a>
                
                <div class='toggle-target display-none'>
                  
                  
                    <ul class='list-reset py1-ul pl1'>
                      <li class='h5'><span>Instance members</span></li>
                      
                      <li><a
                        href='#queueenqueue'
                        class='regular pre-open'>
                        #enqueue
                      </a></li>
                      
                      <li><a
                        href='#queuedequeue'
                        class='regular pre-open'>
                        #dequeue
                      </a></li>
                      
                      <li><a
                        href='#queuerun'
                        class='regular pre-open'>
                        #run
                      </a></li>
                      
                      <li><a
                        href='#queueflush'
                        class='regular pre-open'>
                        #flush
                      </a></li>
                      
                      <li><a
                        href='#queueclear'
                        class='regular pre-open'>
                        #clear
                      </a></li>
                      
                      <li><a
                        href='#queuelength'
                        class='regular pre-open'>
                        #length
                      </a></li>
                      
                      <li><a
                        href='#queuepause'
                        class='regular pre-open'>
                        #pause
                      </a></li>
                      
                      <li><a
                        href='#queuestop'
                        class='regular pre-open'>
                        #stop
                      </a></li>
                      
                    </ul>
                  
                  
                  
                </div>
                
                </li>
              
                
                <li><a
                  href='#layout'
                  class=" toggle-sibling">
                  Layout
                  <span class='icon'>▸</span>
                </a>
                
                <div class='toggle-target display-none'>
                  
                  
                    <ul class='list-reset py1-ul pl1'>
                      <li class='h5'><span>Instance members</span></li>
                      
                      <li><a
                        href='#layoutflow'
                        class='regular pre-open'>
                        #flow
                      </a></li>
                      
                      <li><a
                        href='#layoutspread'
                        class='regular pre-open'>
                        #spread
                      </a></li>
                      
                      <li><a
                        href='#layoutcalculate'
                        class='regular pre-open'>
                        #calculate
                      </a></li>
                      
                      <li><a
                        href='#layoutformat'
                        class='regular pre-open'>
                        #format
                      </a></li>
                      
                      <li><a
                        href='#layoutcount'
                        class='regular pre-open'>
                        #count
                      </a></li>
                      
                    </ul>
                  
                  
                  
                </div>
                
                </li>
              
                
                <li><a
                  href='#themes'
                  class=" toggle-sibling">
                  Themes
                  <span class='icon'>▸</span>
                </a>
                
                <div class='toggle-target display-none'>
                  
                  
                    <ul class='list-reset py1-ul pl1'>
                      <li class='h5'><span>Instance members</span></li>
                      
                      <li><a
                        href='#themesregister'
                        class='regular pre-open'>
                        #register
                      </a></li>
                      
                      <li><a
                        href='#themesdefault'
                        class='regular pre-open'>
                        #default
                      </a></li>
                      
                      <li><a
                        href='#themesregisterthemes'
                        class='regular pre-open'>
                        #registerThemes
                      </a></li>
                      
                      <li><a
                        href='#themesregisterurl'
                        class='regular pre-open'>
                        #registerUrl
                      </a></li>
                      
                      <li><a
                        href='#themesregisterrules'
                        class='regular pre-open'>
                        #registerRules
                      </a></li>
                      
                      <li><a
                        href='#themesselect'
                        class='regular pre-open'>
                        #select
                      </a></li>
                      
                      <li><a
                        href='#themesupdate'
                        class='regular pre-open'>
                        #update
                      </a></li>
                      
                      <li><a
                        href='#themesinject'
                        class='regular pre-open'>
                        #inject
                      </a></li>
                      
                      <li><a
                        href='#themesadd'
                        class='regular pre-open'>
                        #add
                      </a></li>
                      
                      <li><a
                        href='#themesoverride'
                        class='regular pre-open'>
                        #override
                      </a></li>
                      
                      <li><a
                        href='#themesoverrides'
                        class='regular pre-open'>
                        #overrides
                      </a></li>
                      
                      <li><a
                        href='#themesfontsize'
                        class='regular pre-open'>
                        #fontSize
                      </a></li>
                      
                      <li><a
                        href='#themesfont'
                        class='regular pre-open'>
                        #font
                      </a></li>
                      
                    </ul>
                  
                  
                  
                </div>
                
                </li>
              
                
                <li><a
                  href='#annotations'
                  class=" toggle-sibling">
                  Annotations
                  <span class='icon'>▸</span>
                </a>
                
                <div class='toggle-target display-none'>
                  
                  
                    <ul class='list-reset py1-ul pl1'>
                      <li class='h5'><span>Instance members</span></li>
                      
                      <li><a
                        href='#annotationsadd'
                        class='regular pre-open'>
                        #add
                      </a></li>
                      
                      <li><a
                        href='#annotationsremove'
                        class='regular pre-open'>
                        #remove
                      </a></li>
                      
                      <li><a
                        href='#annotationshighlight'
                        class='regular pre-open'>
                        #highlight
                      </a></li>
                      
                      <li><a
                        href='#annotationsunderline'
                        class='regular pre-open'>
                        #underline
                      </a></li>
                      
                      <li><a
                        href='#annotationsmark'
                        class='regular pre-open'>
                        #mark
                      </a></li>
                      
                      <li><a
                        href='#annotationseach'
                        class='regular pre-open'>
                        #each
                      </a></li>
                      
                      <li><a
                        href='#annotationsshow'
                        class='regular pre-open'>
                        #show
                      </a></li>
                      
                      <li><a
                        href='#annotationshide'
                        class='regular pre-open'>
                        #hide
                      </a></li>
                      
                    </ul>
                  
                  
                  
                </div>
                
                </li>
              
                
                <li><a
                  href='#annotation'
                  class=" toggle-sibling">
                  Annotation
                  <span class='icon'>▸</span>
                </a>
                
                <div class='toggle-target display-none'>
                  
                  
                    <ul class='list-reset py1-ul pl1'>
                      <li class='h5'><span>Instance members</span></li>
                      
                      <li><a
                        href='#annotationupdate'
                        class='regular pre-open'>
                        #update
                      </a></li>
                      
                      <li><a
                        href='#annotationattach'
                        class='regular pre-open'>
                        #attach
                      </a></li>
                      
                      <li><a
                        href='#annotationdetach'
                        class='regular pre-open'>
                        #detach
                      </a></li>
                      
                      <li><a
                        href='#annotationtext'
                        class='regular pre-open'>
                        #text
                      </a></li>
                      
                    </ul>
                  
                  
                  
                </div>
                
                </li>
              
                
                <li><a
                  href='#epubcfi'
                  class=" toggle-sibling">
                  EpubCFI
                  <span class='icon'>▸</span>
                </a>
                
                <div class='toggle-target display-none'>
                  
                  
                    <ul class='list-reset py1-ul pl1'>
                      <li class='h5'><span>Instance members</span></li>
                      
                      <li><a
                        href='#epubcfiparse'
                        class='regular pre-open'>
                        #parse
                      </a></li>
                      
                      <li><a
                        href='#epubcfitostring'
                        class='regular pre-open'>
                        #toString
                      </a></li>
                      
                      <li><a
                        href='#epubcficompare'
                        class='regular pre-open'>
                        #compare
                      </a></li>
                      
                      <li><a
                        href='#epubcfifromrange'
                        class='regular pre-open'>
                        #fromRange
                      </a></li>
                      
                      <li><a
                        href='#epubcfifromnode'
                        class='regular pre-open'>
                        #fromNode
                      </a></li>
                      
                      <li><a
                        href='#epubcfitorange'
                        class='regular pre-open'>
                        #toRange
                      </a></li>
                      
                      <li><a
                        href='#epubcfiiscfistring'
                        class='regular pre-open'>
                        #isCfiString
                      </a></li>
                      
                      <li><a
                        href='#epubcficollapse'
                        class='regular pre-open'>
                        #collapse
                      </a></li>
                      
                    </ul>
                  
                  
                  
                </div>
                
                </li>
              
                
                <li><a
                  href='#contents'
                  class=" toggle-sibling">
                  Contents
                  <span class='icon'>▸</span>
                </a>
                
                <div class='toggle-target display-none'>
                  
                  <ul class='list-reset py1-ul pl1'>
                    <li class='h5'><span>Static members</span></li>
                    
                      <li><a
                        href='#contentslistenedevents'
                        class='regular pre-open'>
                        .listenedEvents
                      </a></li>
                    
                    </ul>
                  
                  
                    <ul class='list-reset py1-ul pl1'>
                      <li class='h5'><span>Instance members</span></li>
                      
                      <li><a
                        href='#contentswidth'
                        class='regular pre-open'>
                        #width
                      </a></li>
                      
                      <li><a
                        href='#contentsheight'
                        class='regular pre-open'>
                        #height
                      </a></li>
                      
                      <li><a
                        href='#contentscontentwidth'
                        class='regular pre-open'>
                        #contentWidth
                      </a></li>
                      
                      <li><a
                        href='#contentscontentheight'
                        class='regular pre-open'>
                        #contentHeight
                      </a></li>
                      
                      <li><a
                        href='#contentstextwidth'
                        class='regular pre-open'>
                        #textWidth
                      </a></li>
                      
                      <li><a
                        href='#contentstextheight'
                        class='regular pre-open'>
                        #textHeight
                      </a></li>
                      
                      <li><a
                        href='#contentsscrollwidth'
                        class='regular pre-open'>
                        #scrollWidth
                      </a></li>
                      
                      <li><a
                        href='#contentsscrollheight'
                        class='regular pre-open'>
                        #scrollHeight
                      </a></li>
                      
                      <li><a
                        href='#contentsoverflow'
                        class='regular pre-open'>
                        #overflow
                      </a></li>
                      
                      <li><a
                        href='#contentsoverflowx'
                        class='regular pre-open'>
                        #overflowX
                      </a></li>
                      
                      <li><a
                        href='#contentsoverflowy'
                        class='regular pre-open'>
                        #overflowY
                      </a></li>
                      
                      <li><a
                        href='#contentscss'
                        class='regular pre-open'>
                        #css
                      </a></li>
                      
                      <li><a
                        href='#contentsviewport'
                        class='regular pre-open'>
                        #viewport
                      </a></li>
                      
                      <li><a
                        href='#contentsroot'
                        class='regular pre-open'>
                        #root
                      </a></li>
                      
                      <li><a
                        href='#contentslocationof'
                        class='regular pre-open'>
                        #locationOf
                      </a></li>
                      
                      <li><a
                        href='#contentsaddstylesheet'
                        class='regular pre-open'>
                        #addStylesheet
                      </a></li>
                      
                      <li><a
                        href='#contentsaddstylesheetrules'
                        class='regular pre-open'>
                        #addStylesheetRules
                      </a></li>
                      
                      <li><a
                        href='#contentsaddscript'
                        class='regular pre-open'>
                        #addScript
                      </a></li>
                      
                      <li><a
                        href='#contentsaddclass'
                        class='regular pre-open'>
                        #addClass
                      </a></li>
                      
                      <li><a
                        href='#contentsremoveclass'
                        class='regular pre-open'>
                        #removeClass
                      </a></li>
                      
                      <li><a
                        href='#contentsrange'
                        class='regular pre-open'>
                        #range
                      </a></li>
                      
                      <li><a
                        href='#contentscfifromrange'
                        class='regular pre-open'>
                        #cfiFromRange
                      </a></li>
                      
                      <li><a
                        href='#contentscfifromnode'
                        class='regular pre-open'>
                        #cfiFromNode
                      </a></li>
                      
                      <li><a
                        href='#contentssize'
                        class='regular pre-open'>
                        #size
                      </a></li>
                      
                      <li><a
                        href='#contentscolumns'
                        class='regular pre-open'>
                        #columns
                      </a></li>
                      
                      <li><a
                        href='#contentsscaler'
                        class='regular pre-open'>
                        #scaler
                      </a></li>
                      
                      <li><a
                        href='#contentsfit'
                        class='regular pre-open'>
                        #fit
                      </a></li>
                      
                      <li><a
                        href='#contentsdirection'
                        class='regular pre-open'>
                        #direction
                      </a></li>
                      
                      <li><a
                        href='#contentswritingmode'
                        class='regular pre-open'>
                        #writingMode
                      </a></li>
                      
                    </ul>
                  
                  
                  
                </div>
                
                </li>
              
                
                <li><a
                  href='#mapping'
                  class=" toggle-sibling">
                  Mapping
                  <span class='icon'>▸</span>
                </a>
                
                <div class='toggle-target display-none'>
                  
                  
                    <ul class='list-reset py1-ul pl1'>
                      <li class='h5'><span>Instance members</span></li>
                      
                      <li><a
                        href='#mappingsection'
                        class='regular pre-open'>
                        #section
                      </a></li>
                      
                      <li><a
                        href='#mappingpage'
                        class='regular pre-open'>
                        #page
                      </a></li>
                      
                      <li><a
                        href='#mappingaxis'
                        class='regular pre-open'>
                        #axis
                      </a></li>
                      
                    </ul>
                  
                  
                  
                </div>
                
                </li>
              
                
                <li><a
                  href='#core'
                  class=" toggle-sibling">
                  Core
                  <span class='icon'>▸</span>
                </a>
                
                <div class='toggle-target display-none'>
                  
                  <ul class='list-reset py1-ul pl1'>
                    <li class='h5'><span>Static members</span></li>
                    
                      <li><a
                        href='#corerequestanimationframe'
                        class='regular pre-open'>
                        .requestAnimationFrame
                      </a></li>
                    
                      <li><a
                        href='#coreuuid'
                        class='regular pre-open'>
                        .uuid
                      </a></li>
                    
                      <li><a
                        href='#coredocumentheight'
                        class='regular pre-open'>
                        .documentHeight
                      </a></li>
                    
                      <li><a
                        href='#coreiselement'
                        class='regular pre-open'>
                        .isElement
                      </a></li>
                    
                      <li><a
                        href='#coreisnumber'
                        class='regular pre-open'>
                        .isNumber
                      </a></li>
                    
                      <li><a
                        href='#coreisfloat'
                        class='regular pre-open'>
                        .isFloat
                      </a></li>
                    
                      <li><a
                        href='#coreprefixed'
                        class='regular pre-open'>
                        .prefixed
                      </a></li>
                    
                      <li><a
                        href='#coredefaults'
                        class='regular pre-open'>
                        .defaults
                      </a></li>
                    
                      <li><a
                        href='#coreextend'
                        class='regular pre-open'>
                        .extend
                      </a></li>
                    
                      <li><a
                        href='#coreinsert'
                        class='regular pre-open'>
                        .insert
                      </a></li>
                    
                      <li><a
                        href='#corelocationof'
                        class='regular pre-open'>
                        .locationOf
                      </a></li>
                    
                      <li><a
                        href='#coreindexofsorted'
                        class='regular pre-open'>
                        .indexOfSorted
                      </a></li>
                    
                      <li><a
                        href='#corebounds'
                        class='regular pre-open'>
                        .bounds
                      </a></li>
                    
                      <li><a
                        href='#coreborders'
                        class='regular pre-open'>
                        .borders
                      </a></li>
                    
                      <li><a
                        href='#corenodebounds'
                        class='regular pre-open'>
                        .nodeBounds
                      </a></li>
                    
                      <li><a
                        href='#corewindowbounds'
                        class='regular pre-open'>
                        .windowBounds
                      </a></li>
                    
                      <li><a
                        href='#coreindexofnode'
                        class='regular pre-open'>
                        .indexOfNode
                      </a></li>
                    
                      <li><a
                        href='#coreindexoftextnode'
                        class='regular pre-open'>
                        .indexOfTextNode
                      </a></li>
                    
                      <li><a
                        href='#coreindexofelementnode'
                        class='regular pre-open'>
                        .indexOfElementNode
                      </a></li>
                    
                      <li><a
                        href='#coreisxml'
                        class='regular pre-open'>
                        .isXml
                      </a></li>
                    
                      <li><a
                        href='#corecreateblob'
                        class='regular pre-open'>
                        .createBlob
                      </a></li>
                    
                      <li><a
                        href='#corecreatebloburl'
                        class='regular pre-open'>
                        .createBlobUrl
                      </a></li>
                    
                      <li><a
                        href='#corerevokebloburl'
                        class='regular pre-open'>
                        .revokeBlobUrl
                      </a></li>
                    
                      <li><a
                        href='#corecreatebase64url'
                        class='regular pre-open'>
                        .createBase64Url
                      </a></li>
                    
                      <li><a
                        href='#coretype'
                        class='regular pre-open'>
                        .type
                      </a></li>
                    
                      <li><a
                        href='#coreparse'
                        class='regular pre-open'>
                        .parse
                      </a></li>
                    
                      <li><a
                        href='#coreqs'
                        class='regular pre-open'>
                        .qs
                      </a></li>
                    
                      <li><a
                        href='#coreqsa'
                        class='regular pre-open'>
                        .qsa
                      </a></li>
                    
                      <li><a
                        href='#coreqsp'
                        class='regular pre-open'>
                        .qsp
                      </a></li>
                    
                      <li><a
                        href='#coresprint'
                        class='regular pre-open'>
                        .sprint
                      </a></li>
                    
                      <li><a
                        href='#coretreewalker'
                        class='regular pre-open'>
                        .treeWalker
                      </a></li>
                    
                      <li><a
                        href='#corewalk'
                        class='regular pre-open'>
                        .walk
                      </a></li>
                    
                      <li><a
                        href='#coreblob2base64'
                        class='regular pre-open'>
                        .blob2base64
                      </a></li>
                    
                      <li><a
                        href='#coredefer'
                        class='regular pre-open'>
                        .defer
                      </a></li>
                    
                      <li><a
                        href='#corequeryselectorbytype'
                        class='regular pre-open'>
                        .querySelectorByType
                      </a></li>
                    
                      <li><a
                        href='#corefindchildren'
                        class='regular pre-open'>
                        .findChildren
                      </a></li>
                    
                      <li><a
                        href='#coreparents'
                        class='regular pre-open'>
                        .parents
                      </a></li>
                    
                      <li><a
                        href='#corefilterchildren'
                        class='regular pre-open'>
                        .filterChildren
                      </a></li>
                    
                      <li><a
                        href='#coregetparentbytagname'
                        class='regular pre-open'>
                        .getParentByTagName
                      </a></li>
                    
                      <li><a
                        href='#corerangeobject'
                        class='regular pre-open'>
                        .RangeObject
                      </a></li>
                    
                    </ul>
                  
                  
                  
                  
                </div>
                
                </li>
              
            </ul>
          </div>
          <div class='mt1 h6 quiet'>
            <a href='http://documentation.js.org/reading-documentation.html'>Need help reading this?</a>
          </div>
        </div>
      </div>
      <div id='split-right' class='relative overflow-auto height-viewport-100'>
        
          
            <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='epub'>
      ePub
    </h3>
    
    
  </div>
  

  <p>Creates a new Book</p>


  <div class='pre p1 fill-light mt0'>ePub(url: (<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/ArrayBuffer">ArrayBuffer</a>), options: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>): <a href="#book">Book</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>url</span> <code class='quiet'>((<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/ArrayBuffer">ArrayBuffer</a>))</code>
	    URL, Path or ArrayBuffer

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>options</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>)</code>
	    to pass to the book

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#book">Book</a></code>:
        a new Book object

      
    
  

  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'>ePub(<span class="hljs-string">"/path/to/book.epub"</span>, {})</pre>
    
  

  

  

  
</section>

          
        
          
            <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='book'>
      Book
    </h3>
    
    
  </div>
  

  <p>An Epub representation with methods for the loading, parsing and manipulation
of its contents.</p>


  <div class='pre p1 fill-light mt0'>new Book(url: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?, options: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>?): <a href="#book">Book</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>url</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>options</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>?)</code>
	    
          </div>
          
          <table class='mt1 mb2 fixed-table h5 col-12'>
            <colgroup>
              <col width='30%' />
              <col width='70%' />
            </colgroup>
            <thead>
              <tr class='bold fill-light'>
                <th>Name</th>
                <th>Description</th>
              </tr>
            </thead>
            <tbody class='mt1'>
              
                <tr>
  <td class='break-word'><span class='code bold'>options.requestMethod</span> <code class='quiet'>method?</code>
  </td>
  <td class='break-word'><span>a request function to use instead of the default
</span></td>
</tr>


              
                <tr>
  <td class='break-word'><span class='code bold'>options.requestCredentials</span> <code class='quiet'><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a></code>
  
    (default <code>undefined</code>)
  </td>
  <td class='break-word'><span>send the xhr request withCredentials
</span></td>
</tr>


              
                <tr>
  <td class='break-word'><span class='code bold'>options.requestHeaders</span> <code class='quiet'><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a></code>
  
    (default <code>undefined</code>)
  </td>
  <td class='break-word'><span>send the xhr request headers
</span></td>
</tr>


              
                <tr>
  <td class='break-word'><span class='code bold'>options.encoding</span> <code class='quiet'><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
  
    (default <code>binary</code>)
  </td>
  <td class='break-word'><span>optional to pass 'binary' or base64' for archived Epubs
</span></td>
</tr>


              
                <tr>
  <td class='break-word'><span class='code bold'>options.replacements</span> <code class='quiet'><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
  
    (default <code>none</code>)
  </td>
  <td class='break-word'><span>use base64, blobUrl, or none for replacing assets in archived Epubs
</span></td>
</tr>


              
                <tr>
  <td class='break-word'><span class='code bold'>options.canonical</span> <code class='quiet'>method?</code>
  </td>
  <td class='break-word'><span>optional function to determine canonical urls for a path
</span></td>
</tr>


              
                <tr>
  <td class='break-word'><span class='code bold'>options.openAs</span> <code class='quiet'><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?</code>
  </td>
  <td class='break-word'><span>optional string to determine the input type
</span></td>
</tr>


              
            </tbody>
          </table>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#book">Book</a></code>:
        

      
    
  

  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-keyword">new</span> Book(<span class="hljs-string">"/path/to/book.epub"</span>, {})</pre>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-keyword">new</span> Book({ <span class="hljs-attr">replacements</span>: <span class="hljs-string">"blobUrl"</span> })</pre>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Static Members</div>
    <div class="clearfix">
  
    <div class='border-bottom' id='bookopened'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>opened</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  

  <div class='pre p1 fill-light mt0'>opened</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bookspine'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>spine</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  

  <div class='pre p1 fill-light mt0'>spine</div>
  
    <p>
      Type:
      <a href="#spine">Spine</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='booklocations'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>locations</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  

  <div class='pre p1 fill-light mt0'>locations</div>
  
    <p>
      Type:
      <a href="#locations">Locations</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='booknavigation'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>navigation</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  

  <div class='pre p1 fill-light mt0'>navigation</div>
  
    <p>
      Type:
      <a href="#navigation">Navigation</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bookpagelist'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>pagelist</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  

  <div class='pre p1 fill-light mt0'>pagelist</div>
  
    <p>
      Type:
      <a href="#pagelist">PageList</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
</div>

  

  
    <div class='py1 quiet mt1 prose-big'>Instance Members</div>
    <div class="clearfix">
  
    <div class='border-bottom' id='bookopen'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>open(input, what)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Open a epub or url</p>


  <div class='pre p1 fill-light mt0'>open(input: (<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/ArrayBuffer">ArrayBuffer</a>), what: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>input</span> <code class='quiet'>((<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/ArrayBuffer">ArrayBuffer</a>))</code>
	    Url, Path or ArrayBuffer

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>what</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
            = <code>&quot;binary&quot;,&quot;base64&quot;,&quot;epub&quot;,&quot;opf&quot;,&quot;json&quot;,&quot;directory&quot;</code>)</code>
	    force opening as a certain type

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a></code>:
        of when the book has been loaded

      
    
  

  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'>book.open(<span class="hljs-string">"/path/to/book.epub"</span>)</pre>
    
  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bookload'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>load(path)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Load a resource from the Book</p>


  <div class='pre p1 fill-light mt0'>load(path: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>path</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    path to the resource to load

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a></code>:
        returns a promise with the requested resource

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bookresolve'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>resolve(path, absolute?)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Resolve a path to it's absolute position in the Book</p>


  <div class='pre p1 fill-light mt0'>resolve(path: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, absolute: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>?): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>path</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>absolute</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>?)</code>
	    force resolving the full URL

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>:
        the resolved path string

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bookcanonical'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>canonical(path)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Get a canonical link to a path</p>


  <div class='pre p1 fill-light mt0'>canonical(path: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>path</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>:
        the canonical path string

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='booksection'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>section(target)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Gets a Section of the Book from the Spine
Alias for <code>book.spine.get</code></p>


  <div class='pre p1 fill-light mt0'>section(target: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="#section">Section</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>target</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#section">Section</a></code>:
        

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bookrenderto'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>renderTo(element, options?)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Sugar to render a book to an element</p>


  <div class='pre p1 fill-light mt0'>renderTo(element: (<a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a> | <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>), options: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>?): <a href="#rendition">Rendition</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>element</span> <code class='quiet'>((<a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a> | <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>))</code>
	    element or string to add a rendition to

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>options</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>?)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#rendition">Rendition</a></code>:
        

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='booksetrequestcredentials'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>setRequestCredentials(credentials)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Set if request should use withCredentials</p>


  <div class='pre p1 fill-light mt0'>setRequestCredentials(credentials: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>credentials</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='booksetrequestheaders'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>setRequestHeaders(headers)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Set headers request should use</p>


  <div class='pre p1 fill-light mt0'>setRequestHeaders(headers: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>headers</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bookcoverurl'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>coverUrl()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Get the cover url</p>


  <div class='pre p1 fill-light mt0'>coverUrl(): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  

  
  
  
  
  
  

  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>:
        coverUrl

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bookgetrange'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>getRange(cfiRange)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Find a DOM Range for a given CFI Range</p>


  <div class='pre p1 fill-light mt0'>getRange(cfiRange: <a href="#epubcfi">EpubCFI</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Input">Range</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>cfiRange</span> <code class='quiet'>(<a href="#epubcfi">EpubCFI</a>)</code>
	    a epub cfi range

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Input">Range</a></code>:
        

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bookkey'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>key(identifier?)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Generates the Book Key using the identifer in the manifest or other string provided</p>


  <div class='pre p1 fill-light mt0'>key(identifier: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>identifier</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?)</code>
	    to use instead of metadata identifier

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>:
        key

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='bookdestroy'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>destroy()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Destroy the Book and all associated objects</p>


  <div class='pre p1 fill-light mt0'>destroy()</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
</div>

  

  
</section>

          
        
          
            <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='url'>
      Url
    </h3>
    
    
  </div>
  

  <p>creates a Url object for parsing and manipulation of a url string</p>


  <div class='pre p1 fill-light mt0'>new Url(urlString: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, baseString: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>urlString</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    a url string (relative or absolute)

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>baseString</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?)</code>
	    optional base for the url,
default to window.location.href

          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  
    <div class='py1 quiet mt1 prose-big'>Instance Members</div>
    <div class="clearfix">
  
    <div class='border-bottom' id='urlpath'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>path()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  

  <div class='pre p1 fill-light mt0'>path(): <a href="#path">Path</a></div>
  
  

  
  
  
  
  
  

  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#path">Path</a></code>:
        

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='urlresolve'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>resolve(what)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Resolves a relative path to a absolute url</p>


  <div class='pre p1 fill-light mt0'>resolve(what: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>what</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>:
        url

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='urlrelative'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>relative(what)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Resolve a path relative to the url</p>


  <div class='pre p1 fill-light mt0'>relative(what: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>what</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>:
        path

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='urltostring'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>toString()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  

  <div class='pre p1 fill-light mt0'>toString(): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  

  
  
  
  
  
  

  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>:
        

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
</div>

  

  
</section>

          
        
          
            <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='path'>
      Path
    </h3>
    
    
  </div>
  

  <p>Creates a Path object for parsing and manipulation of a path strings</p>
<p>Uses a polyfill for Nodejs path: <a href="https://nodejs.org/api/path.html">https://nodejs.org/api/path.html</a></p>


  <div class='pre p1 fill-light mt0'>new Path(pathString: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>pathString</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    a url string (relative or absolute)

          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  
    <div class='py1 quiet mt1 prose-big'>Instance Members</div>
    <div class="clearfix">
  
    <div class='border-bottom' id='pathparse'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>parse(what)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Parse the path: <a href="https://nodejs.org/api/path.html#path_path_parse_path">https://nodejs.org/api/path.html#path_path_parse_path</a></p>


  <div class='pre p1 fill-light mt0'>parse(what: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>what</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a></code>:
        

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='pathisabsolute'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>isAbsolute(what)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  

  <div class='pre p1 fill-light mt0'>isAbsolute(what: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>what</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a></code>:
        

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='pathisdirectory'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>isDirectory(what)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Check if path ends with a directory</p>


  <div class='pre p1 fill-light mt0'>isDirectory(what: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>what</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a></code>:
        

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='pathresolve'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>resolve(what)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Resolve a path against the directory of the Path</p>
<p><a href="https://nodejs.org/api/path.html#path_path_resolve_paths">https://nodejs.org/api/path.html#path_path_resolve_paths</a></p>


  <div class='pre p1 fill-light mt0'>resolve(what: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>what</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>:
        resolved

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='pathrelative'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>relative(what)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Resolve a path relative to the directory of the Path</p>
<p><a href="https://nodejs.org/api/path.html#path_path_relative_from_to">https://nodejs.org/api/path.html#path_path_relative_from_to</a></p>


  <div class='pre p1 fill-light mt0'>relative(what: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>what</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>:
        relative

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='pathtostring'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>toString()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Return the path string</p>


  <div class='pre p1 fill-light mt0'>toString(): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  

  
  
  
  
  
  

  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>:
        path

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
</div>

  

  
</section>

          
        
          
            <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='spine'>
      Spine
    </h3>
    
    
  </div>
  

  <p>A collection of Spine Items</p>


  <div class='pre p1 fill-light mt0'>new Spine()</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  
    <div class='py1 quiet mt1 prose-big'>Instance Members</div>
    <div class="clearfix">
  
    <div class='border-bottom' id='spineunpack'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>unpack(_package, resolver, canonical)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Unpack items from a opf into spine items</p>


  <div class='pre p1 fill-light mt0'>unpack(_package: <a href="#packaging">Packaging</a>, resolver: method, canonical: method)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>_package</span> <code class='quiet'>(<a href="#packaging">Packaging</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>resolver</span> <code class='quiet'>(method)</code>
	    URL resolver

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>canonical</span> <code class='quiet'>(method)</code>
	    Resolve canonical url

          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='spineget'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>get(target?)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Get an item from the spine</p>


  <div class='pre p1 fill-light mt0'>get(target: (<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)?): <a href="#section">Section</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>target</span> <code class='quiet'>((<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)?)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#section">Section</a></code>:
        section

      
    
  

  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'>spine.get();</pre>
    
      
      <pre class='p1 overflow-auto round fill-light'>spine.get(<span class="hljs-number">1</span>);</pre>
    
      
      <pre class='p1 overflow-auto round fill-light'>spine.get(<span class="hljs-string">"chap1.html"</span>);</pre>
    
      
      <pre class='p1 overflow-auto round fill-light'>spine.get(<span class="hljs-string">"#id1234"</span>);</pre>
    
  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='spineeach'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>each()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Loop over the Sections in the Spine</p>


  <div class='pre p1 fill-light mt0'>each(): method</div>
  
  

  
  
  
  
  
  

  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code>method</code>:
        forEach

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='spinefirst'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>first()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Find the first Section in the Spine</p>


  <div class='pre p1 fill-light mt0'>first(): <a href="#section">Section</a></div>
  
  

  
  
  
  
  
  

  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#section">Section</a></code>:
        first section

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='spinelast'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>last()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Find the last Section in the Spine</p>


  <div class='pre p1 fill-light mt0'>last(): <a href="#section">Section</a></div>
  
  

  
  
  
  
  
  

  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#section">Section</a></code>:
        last section

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
</div>

  

  
</section>

          
        
          
            <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='section'>
      Section
    </h3>
    
    
  </div>
  

  <p>Represents a Section of the Book</p>
<p>In most books this is equivelent to a Chapter</p>


  <div class='pre p1 fill-light mt0'>new Section(item: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>, hooks: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>item</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>)</code>
	    The spine item representing the section

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>hooks</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>)</code>
	    hooks for serialize and content

          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  
    <div class='py1 quiet mt1 prose-big'>Instance Members</div>
    <div class="clearfix">
  
    <div class='border-bottom' id='sectionload'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>load(_request?)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Load the section from its url</p>


  <div class='pre p1 fill-light mt0'>load(_request: method?): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript">document</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>_request</span> <code class='quiet'>(method?)</code>
	    a request method to use for loading

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript">document</a></code>:
        a promise with the xml document

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='sectionrender'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>render(_request?)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Render the contents of a section</p>


  <div class='pre p1 fill-light mt0'>render(_request: method?): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>_request</span> <code class='quiet'>(method?)</code>
	    a request method to use for loading

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>:
        output a serialized XML Document

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='sectionfind'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>find(_query)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Find a string in a section</p>


  <div class='pre p1 fill-light mt0'>find(_query: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>_query</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    The query string to find

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>></code>:
        A list of matches, with form {cfi, excerpt}

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='sectionreconcilelayoutsettings'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>reconcileLayoutSettings(globalLayout)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Reconciles the current chapters layout properies with
the global layout properities.</p>


  <div class='pre p1 fill-light mt0'>reconcileLayoutSettings(globalLayout: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>globalLayout</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>)</code>
	    The global layout settings object, chapter properties string

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a></code>:
        layoutProperties Object with layout properties

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='sectioncfifromrange'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>cfiFromRange(_range)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Get a CFI from a Range in the Section</p>


  <div class='pre p1 fill-light mt0'>cfiFromRange(_range: <a href="https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Input">range</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>_range</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Input">range</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>:
        cfi an EpubCFI string

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='sectioncfifromelement'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>cfiFromElement(el)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Get a CFI from an Element in the Section</p>


  <div class='pre p1 fill-light mt0'>cfiFromElement(el: <a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>el</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>:
        cfi an EpubCFI string

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='sectionunload'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>unload()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Unload the section document</p>


  <div class='pre p1 fill-light mt0'>unload()</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
</div>

  

  
</section>

          
        
          
            <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='locations'>
      Locations
    </h3>
    
    
  </div>
  

  <p>Find Locations for a Book</p>


  <div class='pre p1 fill-light mt0'>new Locations(spine: <a href="#spine">Spine</a>, request: <a href="https://developer.mozilla.org/en-US/Add-ons/SDK/High-Level_APIs/request">request</a>, pause: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>spine</span> <code class='quiet'>(<a href="#spine">Spine</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>request</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/Add-ons/SDK/High-Level_APIs/request">request</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>pause</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>
            = <code>100</code>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  
    <div class='py1 quiet mt1 prose-big'>Instance Members</div>
    <div class="clearfix">
  
    <div class='border-bottom' id='locationsgenerate'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>generate(chars)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Load all of sections in the book to generate locations</p>


  <div class='pre p1 fill-light mt0'>generate(chars: int): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>chars</span> <code class='quiet'>(int)</code>
	    how many chars to split on

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a></code>:
        locations

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='locationslocationfromcfi'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>locationFromCfi(cfi)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Get a location from an EpubCFI</p>


  <div class='pre p1 fill-light mt0'>locationFromCfi(cfi: <a href="#epubcfi">EpubCFI</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>cfi</span> <code class='quiet'>(<a href="#epubcfi">EpubCFI</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></code>:
        

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='locationspercentagefromcfi'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>percentageFromCfi(cfi)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Get a percentage position in locations from an EpubCFI</p>


  <div class='pre p1 fill-light mt0'>percentageFromCfi(cfi: <a href="#epubcfi">EpubCFI</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>cfi</span> <code class='quiet'>(<a href="#epubcfi">EpubCFI</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></code>:
        

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='locationspercentagefromlocation'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>percentageFromLocation(loc, location)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Get a percentage position from a location index</p>


  <div class='pre p1 fill-light mt0'>percentageFromLocation(loc: any, location: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>loc</span> <code class='quiet'>(any)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>location</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></code>:
        

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='locationscfifromlocation'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>cfiFromLocation(loc)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Get an EpubCFI from location index</p>


  <div class='pre p1 fill-light mt0'>cfiFromLocation(loc: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>): <a href="#epubcfi">EpubCFI</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>loc</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#epubcfi">EpubCFI</a></code>:
        cfi

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='locationscfifrompercentage'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>cfiFromPercentage(percentage)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Get an EpubCFI from location percentage</p>


  <div class='pre p1 fill-light mt0'>cfiFromPercentage(percentage: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>): <a href="#epubcfi">EpubCFI</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>percentage</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#epubcfi">EpubCFI</a></code>:
        cfi

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='locationsload'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>load(locations)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Load locations from JSON</p>


  <div class='pre p1 fill-light mt0'>load(locations: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON">json</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>locations</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON">json</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='locationssave'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>save()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Save locations to JSON</p>


  <div class='pre p1 fill-light mt0'>save(): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON">json</a></div>
  
  

  
  
  
  
  
  

  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON">json</a></code>:
        

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='locationscurrentlocation'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>currentLocation</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Get the current location</p>


  <div class='pre p1 fill-light mt0'>currentLocation</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='locationscurrentlocation'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>currentLocation</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Set the current location</p>


  <div class='pre p1 fill-light mt0'>currentLocation</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>curr</span> <code class='quiet'>(any)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='locationslength'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>length()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Locations length</p>


  <div class='pre p1 fill-light mt0'>length()</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
</div>

  

  
</section>

          
        
          
            <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='container'>
      Container
    </h3>
    
    
  </div>
  

  <p>Handles Parsing and Accessing an Epub Container</p>


  <div class='pre p1 fill-light mt0'>new Container(containerDocument: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript">document</a>?)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>containerDocument</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript">document</a>?)</code>
	    xml document

          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  
    <div class='py1 quiet mt1 prose-big'>Instance Members</div>
    <div class="clearfix">
  
    <div class='border-bottom' id='containerparse'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>parse(containerDocument)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Parse the Container XML</p>


  <div class='pre p1 fill-light mt0'>parse(containerDocument: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript">document</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>containerDocument</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript">document</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
</div>

  

  
</section>

          
        
          
            <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='packaging'>
      Packaging
    </h3>
    
    
  </div>
  

  <p>Open Packaging Format Parser</p>


  <div class='pre p1 fill-light mt0'>new Packaging(packageDocument: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript">document</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>packageDocument</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript">document</a>)</code>
	    OPF XML

          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  
    <div class='py1 quiet mt1 prose-big'>Instance Members</div>
    <div class="clearfix">
  
    <div class='border-bottom' id='packagingparse'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>parse(packageDocument)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Parse OPF XML</p>


  <div class='pre p1 fill-light mt0'>parse(packageDocument: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript">document</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>packageDocument</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript">document</a>)</code>
	    OPF XML

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a></code>:
        parsed package parts

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='packagingload'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>load(json, packageDocument)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Load JSON Manifest</p>


  <div class='pre p1 fill-light mt0'>load(json: any, packageDocument: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript">document</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>json</span> <code class='quiet'>(any)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>packageDocument</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript">document</a>)</code>
	    OPF XML

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a></code>:
        parsed package parts

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
</div>

  

  
</section>

          
        
          
            <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='navigation'>
      Navigation
    </h3>
    
    
  </div>
  

  <p>Navigation Parser</p>


  <div class='pre p1 fill-light mt0'>new Navigation(xml: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript">document</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>xml</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript">document</a>)</code>
	    navigation html / xhtml / ncx

          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  
    <div class='py1 quiet mt1 prose-big'>Instance Members</div>
    <div class="clearfix">
  
    <div class='border-bottom' id='navigationparse'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>parse(xml)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Parse out the navigation items</p>


  <div class='pre p1 fill-light mt0'>parse(xml: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript">document</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>xml</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript">document</a>)</code>
	    navigation html / xhtml / ncx

          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='navigationget'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>get(target)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Get an item from the navigation</p>


  <div class='pre p1 fill-light mt0'>get(target: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>target</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a></code>:
        navItem

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='navigationlandmark'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>landmark(type)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Get a landmark by type
List of types: <a href="https://idpf.github.io/epub-vocabs/structure/">https://idpf.github.io/epub-vocabs/structure/</a></p>


  <div class='pre p1 fill-light mt0'>landmark(type: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>type</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a></code>:
        landmarkItem

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='navigationload'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>load(json)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Load Spine Items</p>


  <div class='pre p1 fill-light mt0'>load(json: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>json</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>)</code>
	    the items to be loaded

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a></code>:
        navItems

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='navigationforeach'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>forEach(fn)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>forEach pass through</p>


  <div class='pre p1 fill-light mt0'>forEach(fn: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function">Function</a>): method</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>fn</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function">Function</a>)</code>
	    function to run on each item

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code>method</code>:
        forEach loop

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
</div>

  

  
</section>

          
        
          
            <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='resources'>
      Resources
    </h3>
    
    
  </div>
  

  <p>Handle Package Resources</p>


  <div class='pre p1 fill-light mt0'>new Resources(manifest: Manifest, options: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>?)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>manifest</span> <code class='quiet'>(Manifest)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>options</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>?)</code>
	    
          </div>
          
          <table class='mt1 mb2 fixed-table h5 col-12'>
            <colgroup>
              <col width='30%' />
              <col width='70%' />
            </colgroup>
            <thead>
              <tr class='bold fill-light'>
                <th>Name</th>
                <th>Description</th>
              </tr>
            </thead>
            <tbody class='mt1'>
              
                <tr>
  <td class='break-word'><span class='code bold'>options.replacements</span> <code class='quiet'><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
  
    (default <code>&quot;base64&quot;</code>)
  </td>
  <td class='break-word'><span></span></td>
</tr>


              
                <tr>
  <td class='break-word'><span class='code bold'>options.archive</span> <code class='quiet'><a href="#archive">Archive</a>?</code>
  </td>
  <td class='break-word'><span></span></td>
</tr>


              
                <tr>
  <td class='break-word'><span class='code bold'>options.resolver</span> <code class='quiet'>method?</code>
  </td>
  <td class='break-word'><span></span></td>
</tr>


              
            </tbody>
          </table>
          
        </div>
      
    </div>
  

  

  

  

  

  

  
    <div class='py1 quiet mt1 prose-big'>Instance Members</div>
    <div class="clearfix">
  
    <div class='border-bottom' id='resourcescreateurl'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>createUrl(url)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Create a url to a resource</p>


  <div class='pre p1 fill-light mt0'>createUrl(url: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>url</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>></code>:
        Promise resolves with url string

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='resourcesreplacements'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>replacements()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Create blob urls for all the assets</p>


  <div class='pre p1 fill-light mt0'>replacements(): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a></div>
  
  

  
  
  
  
  
  

  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a></code>:
        returns replacement urls

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='resourcesrelativeto'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>relativeTo(absolute, resolver?)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Resolve all resources URLs relative to an absolute URL</p>


  <div class='pre p1 fill-light mt0'>relativeTo(absolute: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, resolver: resolver?): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>absolute</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    to be resolved to

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>resolver</span> <code class='quiet'>(resolver?)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>></code>:
        array with relative Urls

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='resourcesget'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>get(path)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Get a URL for a resource</p>


  <div class='pre p1 fill-light mt0'>get(path: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>path</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>:
        url

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='resourcessubstitute'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>substitute(content, url?)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Substitute urls in content, with replacements,
relative to a url if provided</p>


  <div class='pre p1 fill-light mt0'>substitute(content: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, url: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>content</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>url</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?)</code>
	    url to resolve to

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>:
        content with urls substituted

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
</div>

  

  
</section>

          
        
          
            <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='pagelist'>
      PageList
    </h3>
    
    
  </div>
  

  <p>Page List Parser</p>


  <div class='pre p1 fill-light mt0'>new PageList(xml: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript">document</a>?)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>xml</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript">document</a>?)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  
    <div class='py1 quiet mt1 prose-big'>Instance Members</div>
    <div class="clearfix">
  
    <div class='border-bottom' id='pagelistparse'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>parse(xml)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Parse PageList Xml</p>


  <div class='pre p1 fill-light mt0'>parse(xml: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript">document</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>xml</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript">document</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='pagelistpagefromcfi'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>pageFromCfi(cfi)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Get a PageList result from a EpubCFI</p>


  <div class='pre p1 fill-light mt0'>pageFromCfi(cfi: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>cfi</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    EpubCFI String

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></code>:
        page

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='pagelistcfifrompage'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>cfiFromPage(pg)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Get an EpubCFI from a Page List Item</p>


  <div class='pre p1 fill-light mt0'>cfiFromPage(pg: (<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>pg</span> <code class='quiet'>((<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>))</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>:
        cfi

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='pagelistpagefrompercentage'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>pageFromPercentage(percent)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Get a Page from Book percentage</p>


  <div class='pre p1 fill-light mt0'>pageFromPercentage(percent: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>percent</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></code>:
        page

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='pagelistpercentagefrompage'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>percentageFromPage(pg)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Returns a value between 0 - 1 corresponding to the location of a page</p>


  <div class='pre p1 fill-light mt0'>percentageFromPage(pg: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>pg</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
	    the page

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></code>:
        percentage

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='pagelistpercentagefromcfi'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>percentageFromCfi(cfi)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Returns a value between 0 - 1 corresponding to the location of a cfi</p>


  <div class='pre p1 fill-light mt0'>percentageFromCfi(cfi: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>cfi</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    EpubCFI String

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></code>:
        percentage

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='pagelistdestroy'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>destroy()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Destroy</p>


  <div class='pre p1 fill-light mt0'>destroy()</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
</div>

  

  
</section>

          
        
          
            <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='archive'>
      Archive
    </h3>
    
    
  </div>
  

  <p>Handles Unzipping a requesting files from an Epub Archive</p>


  <div class='pre p1 fill-light mt0'>new Archive()</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  
    <div class='py1 quiet mt1 prose-big'>Instance Members</div>
    <div class="clearfix">
  
    <div class='border-bottom' id='archiveopen'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>open(input, isBase64?)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Open an archive</p>


  <div class='pre p1 fill-light mt0'>open(input: binary, isBase64: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>?): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>input</span> <code class='quiet'>(binary)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>isBase64</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>?)</code>
	    tells JSZip if the input data is base64 encoded

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a></code>:
        zipfile

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='archiveopenurl'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>openUrl(zipUrl, isBase64?)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Load and Open an archive</p>


  <div class='pre p1 fill-light mt0'>openUrl(zipUrl: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, isBase64: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>?): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>zipUrl</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>isBase64</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>?)</code>
	    tells JSZip if the input data is base64 encoded

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a></code>:
        zipfile

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='archiverequest'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>request(url, type?)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Request a url from the archive</p>


  <div class='pre p1 fill-light mt0'>request(url: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, type: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;(<a href="https://developer.mozilla.org/en-US/docs/Web/API/Blob">Blob</a> | <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON">JSON</a> | <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript">Document</a> | XMLDocument)></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>url</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    a url to request from the archive

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>type</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?)</code>
	    specify the type of the returned result

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a>&#x3C;(<a href="https://developer.mozilla.org/en-US/docs/Web/API/Blob">Blob</a> | <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON">JSON</a> | <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript">Document</a> | XMLDocument)></code>:
        

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='archivegetblob'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>getBlob(url, mimeType?)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Get a Blob from Archive by Url</p>


  <div class='pre p1 fill-light mt0'>getBlob(url: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, mimeType: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?): <a href="https://developer.mozilla.org/en-US/docs/Web/API/Blob">Blob</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>url</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>mimeType</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/API/Blob">Blob</a></code>:
        

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='archivegettext'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>getText(url, encoding?)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Get Text from Archive by Url</p>


  <div class='pre p1 fill-light mt0'>getText(url: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, encoding: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>url</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>encoding</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>:
        

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='archivegetbase64'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>getBase64(url, mimeType?)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Get a base64 encoded result from Archive by Url</p>


  <div class='pre p1 fill-light mt0'>getBase64(url: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, mimeType: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>url</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>mimeType</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>:
        base64 encoded

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='archivecreateurl'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>createUrl(url, options)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Create a Url from an unarchived item</p>


  <div class='pre p1 fill-light mt0'>createUrl(url: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, options: any): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>url</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>options</span> <code class='quiet'>(any)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a></code>:
        url promise with Url string

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='archiverevokeurl'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>revokeUrl(url)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Revoke Temp Url for a achive item</p>


  <div class='pre p1 fill-light mt0'>revokeUrl(url: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>url</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    url of the item in the archive

          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
</div>

  

  
</section>

          
        
          
            <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='rendition'>
      Rendition
    </h3>
    
    
  </div>
  

  <p>Displays an Epub as a series of Views for each Section.
Requires Manager and View class to handle specifics of rendering
the section contetn.</p>


  <div class='pre p1 fill-light mt0'>new Rendition(book: <a href="#book">Book</a>, options: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>?)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>book</span> <code class='quiet'>(<a href="#book">Book</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>options</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>?)</code>
	    
          </div>
          
          <table class='mt1 mb2 fixed-table h5 col-12'>
            <colgroup>
              <col width='30%' />
              <col width='70%' />
            </colgroup>
            <thead>
              <tr class='bold fill-light'>
                <th>Name</th>
                <th>Description</th>
              </tr>
            </thead>
            <tbody class='mt1'>
              
                <tr>
  <td class='break-word'><span class='code bold'>options.width</span> <code class='quiet'><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?</code>
  </td>
  <td class='break-word'><span></span></td>
</tr>


              
                <tr>
  <td class='break-word'><span class='code bold'>options.height</span> <code class='quiet'><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?</code>
  </td>
  <td class='break-word'><span></span></td>
</tr>


              
                <tr>
  <td class='break-word'><span class='code bold'>options.ignoreClass</span> <code class='quiet'><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?</code>
  </td>
  <td class='break-word'><span>class for the cfi parser to ignore
</span></td>
</tr>


              
                <tr>
  <td class='break-word'><span class='code bold'>options.manager</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function">function</a> | <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>)</code>
  
    (default <code>&#39;default&#39;</code>)
  </td>
  <td class='break-word'><span></span></td>
</tr>


              
                <tr>
  <td class='break-word'><span class='code bold'>options.view</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function">function</a>)</code>
  
    (default <code>&#39;iframe&#39;</code>)
  </td>
  <td class='break-word'><span></span></td>
</tr>


              
                <tr>
  <td class='break-word'><span class='code bold'>options.layout</span> <code class='quiet'><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?</code>
  </td>
  <td class='break-word'><span>layout to force
</span></td>
</tr>


              
                <tr>
  <td class='break-word'><span class='code bold'>options.spread</span> <code class='quiet'><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?</code>
  </td>
  <td class='break-word'><span>force spread value
</span></td>
</tr>


              
                <tr>
  <td class='break-word'><span class='code bold'>options.minSpreadWidth</span> <code class='quiet'><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?</code>
  </td>
  <td class='break-word'><span>overridden by spread: none (never) / both (always)
</span></td>
</tr>


              
                <tr>
  <td class='break-word'><span class='code bold'>options.stylesheet</span> <code class='quiet'><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?</code>
  </td>
  <td class='break-word'><span>url of stylesheet to be injected
</span></td>
</tr>


              
                <tr>
  <td class='break-word'><span class='code bold'>options.resizeOnOrientationChange</span> <code class='quiet'><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>?</code>
  </td>
  <td class='break-word'><span>false to disable orientation events
</span></td>
</tr>


              
                <tr>
  <td class='break-word'><span class='code bold'>options.script</span> <code class='quiet'><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?</code>
  </td>
  <td class='break-word'><span>url of script to be injected
</span></td>
</tr>


              
            </tbody>
          </table>
          
        </div>
      
    </div>
  

  

  

  

  

  
    <div class='py1 quiet mt1 prose-big'>Static Members</div>
    <div class="clearfix">
  
    <div class='border-bottom' id='renditionhooks'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>hooks</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Adds Hook methods to the Rendition prototype</p>


  <div class='pre p1 fill-light mt0'>hooks</div>
  
    <p>
      Type:
      <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='renditionthemes'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>themes</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  

  <div class='pre p1 fill-light mt0'>themes</div>
  
    <p>
      Type:
      <a href="#themes">Themes</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='renditionannotations'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>annotations</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  

  <div class='pre p1 fill-light mt0'>annotations</div>
  
    <p>
      Type:
      <a href="#annotations">Annotations</a>
    </p>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='renditionlocation'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>location</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>A Rendered Location Range</p>


  <div class='pre p1 fill-light mt0'>location</div>
  
  

  
  
  
  
  
  

  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
        <div class='space-bottom0'>
          <span class='code bold'>start</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>)</code>
          
          
            <ul>
              
                <li><code>start.index</code> <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
                  
                  </li>
              
                <li><code>start.href</code> <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
                  
                  </li>
              
                <li><code>start.displayed</code> <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>
                  
                  </li>
              
                <li><code>start.cfi</code> <a href="#epubcfi">EpubCFI</a>
                  
                  </li>
              
                <li><code>start.location</code> <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>
                  
                  </li>
              
                <li><code>start.percentage</code> <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>
                  
                  </li>
              
            </ul>
          
        </div>
      
        <div class='space-bottom0'>
          <span class='code bold'>end</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>)</code>
          
          
            <ul>
              
                <li><code>end.index</code> <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
                  
                  </li>
              
                <li><code>end.href</code> <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
                  
                  </li>
              
                <li><code>end.displayed</code> <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>
                  
                  </li>
              
                <li><code>end.cfi</code> <a href="#epubcfi">EpubCFI</a>
                  
                  </li>
              
                <li><code>end.location</code> <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>
                  
                  </li>
              
                <li><code>end.percentage</code> <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>
                  
                  </li>
              
            </ul>
          
        </div>
      
        <div class='space-bottom0'>
          <span class='code bold'>atStart</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>)</code>
          
          
        </div>
      
        <div class='space-bottom0'>
          <span class='code bold'>atEnd</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>)</code>
          
          
        </div>
      
    </div>
  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='renditionstarted'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>started</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  

  <div class='pre p1 fill-light mt0'>started</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
</div>

  

  
    <div class='py1 quiet mt1 prose-big'>Instance Members</div>
    <div class="clearfix">
  
    <div class='border-bottom' id='renditionsetmanager'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>setManager(manager)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Set the manager function</p>


  <div class='pre p1 fill-light mt0'>setManager(manager: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function">function</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>manager</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function">function</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='renditionrequiremanager'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>requireManager(manager)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Require the manager from passed string, or as a class function</p>


  <div class='pre p1 fill-light mt0'>requireManager(manager: (<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>)): method</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>manager</span> <code class='quiet'>((<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>))</code>
	    [
description
]

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code>method</code>:
        

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='renditionrequireview'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>requireView(view)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Require the view from passed string, or as a class function</p>


  <div class='pre p1 fill-light mt0'>requireView(view: (<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>)): view</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>view</span> <code class='quiet'>((<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>))</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code>view</code>:
        

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='renditionstart'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>start()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Start the rendering</p>


  <div class='pre p1 fill-light mt0'>start(): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a></div>
  
  

  
  
  
  
  
  

  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a></code>:
        rendering has started

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='renditionattachto'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>attachTo(element)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Call to attach the container to an element in the dom
Container must be attached before rendering can begin</p>


  <div class='pre p1 fill-light mt0'>attachTo(element: <a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>element</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a>)</code>
	    to attach to

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a></code>:
        

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='renditiondisplay'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>display(target)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Display a point in the book
The request will be added to the rendering Queue,
so it will wait until book is opened, rendering started
and all other rendering tasks have finished to be called.</p>


  <div class='pre p1 fill-light mt0'>display(target: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>target</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    Url or EpubCFI

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a></code>:
        

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='renditionmoveto'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>moveTo(offset)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Move the Rendition to a specific offset
Usually you would be better off calling display()</p>


  <div class='pre p1 fill-light mt0'>moveTo(offset: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>offset</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='renditionresize'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>resize(width?, height?)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Trigger a resize of the views</p>


  <div class='pre p1 fill-light mt0'>resize(width: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?, height: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>width</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>height</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='renditionclear'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>clear()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Clear all rendered views</p>


  <div class='pre p1 fill-light mt0'>clear()</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='renditionnext'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>next()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Go to the next "page" in the rendition</p>


  <div class='pre p1 fill-light mt0'>next(): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a></div>
  
  

  
  
  
  
  
  

  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a></code>:
        

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='renditionprev'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>prev()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Go to the previous "page" in the rendition</p>


  <div class='pre p1 fill-light mt0'>prev(): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a></div>
  
  

  
  
  
  
  
  

  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a></code>:
        

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='renditionflow'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>flow(flow)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Adjust the flow of the rendition to paginated or scrolled
(scrolled-continuous vs scrolled-doc are handled by different view managers)</p>


  <div class='pre p1 fill-light mt0'>flow(flow: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>flow</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='renditionlayout'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>layout(settings)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Adjust the layout of the rendition to reflowable or pre-paginated</p>


  <div class='pre p1 fill-light mt0'>layout(settings: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>settings</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='renditionspread'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>spread(spread, min)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Adjust if the rendition uses spreads</p>


  <div class='pre p1 fill-light mt0'>spread(spread: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, min: int)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>spread</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    none | auto (TODO: implement landscape, portrait, both)

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>min</span> <code class='quiet'>(int)</code>
	    min width to use spreads at

          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='renditiondirection'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>direction(dir)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Adjust the direction of the rendition</p>


  <div class='pre p1 fill-light mt0'>direction(dir: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>dir</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='renditionreportlocation'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>reportLocation()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Report the current location</p>


  <div class='pre p1 fill-light mt0'>reportLocation()</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='renditioncurrentlocation'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>currentLocation()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Get the Current Location object</p>


  <div class='pre p1 fill-light mt0'>currentLocation(): (displayedLocation | <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise">promise</a>)</div>
  
  

  
  
  
  
  
  

  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code>(displayedLocation | <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise">promise</a>)</code>:
        location (may be a promise)

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='renditiondestroy'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>destroy()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Remove and Clean Up the Rendition</p>


  <div class='pre p1 fill-light mt0'>destroy()</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='renditiongetrange'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>getRange(cfi, ignoreClass)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Get a Range from a Visible CFI</p>


  <div class='pre p1 fill-light mt0'>getRange(cfi: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, ignoreClass: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Input">range</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>cfi</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    EpubCfi String

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>ignoreClass</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Input">range</a></code>:
        

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='renditiongetcontents'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>getContents()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Get the Contents object of each rendered view</p>


  <div class='pre p1 fill-light mt0'>getContents(): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#contents">Contents</a>></div>
  
  

  
  
  
  
  
  

  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#contents">Contents</a>></code>:
        

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='renditionviews'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>views()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Get the views member from the manager</p>


  <div class='pre p1 fill-light mt0'>views(): Views</div>
  
  

  
  
  
  
  
  

  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code>Views</code>:
        

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
</div>

  

  
    <div class='py1 quiet mt1 prose-big'>Events</div>
    <div class="clearfix">
  
    <div class='border-bottom' id='renditioneventstarted'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>started</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Emit that rendering has started</p>


  <div class='pre p1 fill-light mt0'>started</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='renditioneventattached'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>attached</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Emit that rendering has attached to an element</p>


  <div class='pre p1 fill-light mt0'>attached</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='renditioneventdisplayed'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>displayed</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Emit that a section has been displayed</p>


  <div class='pre p1 fill-light mt0'>displayed</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>section</span> <code class='quiet'>(<a href="#section">Section</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='renditioneventdisplayerror'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>displayError</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Emit that has been an error displaying</p>


  <div class='pre p1 fill-light mt0'>displayError</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>section</span> <code class='quiet'>(<a href="#section">Section</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='renditioneventrendered'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>rendered</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Emit that a section has been rendered</p>


  <div class='pre p1 fill-light mt0'>rendered</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>section</span> <code class='quiet'>(<a href="#section">Section</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>view</span> <code class='quiet'>(View)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='renditioneventremoved'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>removed</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Emit that a section has been removed</p>


  <div class='pre p1 fill-light mt0'>removed</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>section</span> <code class='quiet'>(<a href="#section">Section</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>view</span> <code class='quiet'>(View)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='renditioneventresized'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>resized</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Emit that the rendition has been resized</p>


  <div class='pre p1 fill-light mt0'>resized</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>width</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>height</span> <code class='quiet'>(height)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='renditioneventorientationchange'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>orientationchange</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Emit that the rendition has been rotated</p>


  <div class='pre p1 fill-light mt0'>orientationchange</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>orientation</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='renditioneventlocationchanged'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>locationChanged</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  

  <div class='pre p1 fill-light mt0'>locationChanged</div>
  
  

  <div>Deprecated: This is deprecated.
</div>
  
  
  
  
  

  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
        <div class='space-bottom0'>
          <span class='code bold'>index</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
          
          
        </div>
      
        <div class='space-bottom0'>
          <span class='code bold'>href</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
          
          
        </div>
      
        <div class='space-bottom0'>
          <span class='code bold'>start</span> <code class='quiet'>(<a href="#epubcfi">EpubCFI</a>)</code>
          
          
        </div>
      
        <div class='space-bottom0'>
          <span class='code bold'>end</span> <code class='quiet'>(<a href="#epubcfi">EpubCFI</a>)</code>
          
          
        </div>
      
        <div class='space-bottom0'>
          <span class='code bold'>percentage</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
          
          
        </div>
      
    </div>
  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='renditioneventrelocated'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>relocated</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  

  <div class='pre p1 fill-light mt0'>relocated</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='renditioneventselected'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>selected</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Emit that a text selection has occured</p>


  <div class='pre p1 fill-light mt0'>selected</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>cfirange</span> <code class='quiet'>(<a href="#epubcfi">EpubCFI</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>contents</span> <code class='quiet'>(<a href="#contents">Contents</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='renditioneventmarkclicked'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>markClicked</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Emit that a mark was clicked</p>


  <div class='pre p1 fill-light mt0'>markClicked</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>cfirange</span> <code class='quiet'>(<a href="#epubcfi">EpubCFI</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>data</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>contents</span> <code class='quiet'>(<a href="#contents">Contents</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
</div>

  
</section>

          
        
          
            <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='hook'>
      Hook
    </h3>
    
    
  </div>
  

  <p>Hooks allow for injecting functions that must all complete in order before finishing
They will execute in parallel but all must finish before continuing
Functions may return a promise if they are asycn.</p>


  <div class='pre p1 fill-light mt0'>new Hook(context: any)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>context</span> <code class='quiet'>(any)</code>
	    scope of this

          </div>
          
        </div>
      
    </div>
  

  

  

  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-keyword">this</span>.content = <span class="hljs-keyword">new</span> EPUBJS.Hook(<span class="hljs-keyword">this</span>);</pre>
    
  

  

  
    <div class='py1 quiet mt1 prose-big'>Instance Members</div>
    <div class="clearfix">
  
    <div class='border-bottom' id='hookregister'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>register()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Adds a function to be run before a hook completes</p>


  <div class='pre p1 fill-light mt0'>register()</div>
  
  

  
  
  
  
  
  

  

  

  

  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-keyword">this</span>.content.register(<span class="hljs-function"><span class="hljs-keyword">function</span>(<span class="hljs-params"></span>)</span>{...});</pre>
    
  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='hooktrigger'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>trigger()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Triggers a hook to run all functions</p>


  <div class='pre p1 fill-light mt0'>trigger()</div>
  
  

  
  
  
  
  
  

  

  

  

  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-keyword">this</span>.content.trigger(args).then(<span class="hljs-function"><span class="hljs-keyword">function</span>(<span class="hljs-params"></span>)</span>{...});</pre>
    
  

  

  

  
</section>

      </div>
    </div>
  
</div>

  

  
</section>

          
        
          
            <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='queue'>
      Queue
    </h3>
    
    
  </div>
  

  <p>Queue for handling tasks one at a time</p>


  <div class='pre p1 fill-light mt0'>new Queue(context: scope)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>context</span> <code class='quiet'>(scope)</code>
	    what this will resolve to in the tasks

          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  
    <div class='py1 quiet mt1 prose-big'>Instance Members</div>
    <div class="clearfix">
  
    <div class='border-bottom' id='queueenqueue'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>enqueue()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Add an item to the queue</p>


  <div class='pre p1 fill-light mt0'>enqueue(): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a></div>
  
  

  
  
  
  
  
  

  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a></code>:
        

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='queuedequeue'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>dequeue()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Run one item</p>


  <div class='pre p1 fill-light mt0'>dequeue(): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a></div>
  
  

  
  
  
  
  
  

  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a></code>:
        

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='queuerun'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>run()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Run all tasks sequentially, at convince</p>


  <div class='pre p1 fill-light mt0'>run(): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a></div>
  
  

  
  
  
  
  
  

  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a></code>:
        

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='queueflush'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>flush()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Flush all, as quickly as possible</p>


  <div class='pre p1 fill-light mt0'>flush(): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a></div>
  
  

  
  
  
  
  
  

  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a></code>:
        

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='queueclear'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>clear()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Clear all items in wait</p>


  <div class='pre p1 fill-light mt0'>clear()</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='queuelength'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>length()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Get the number of tasks in the queue</p>


  <div class='pre p1 fill-light mt0'>length(): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></div>
  
  

  
  
  
  
  
  

  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></code>:
        tasks

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='queuepause'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>pause()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Pause a running queue</p>


  <div class='pre p1 fill-light mt0'>pause()</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='queuestop'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>stop()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>End the queue</p>


  <div class='pre p1 fill-light mt0'>stop()</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
</div>

  

  
</section>

          
        
          
            <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='layout'>
      Layout
    </h3>
    
    
  </div>
  

  <p>Figures out the CSS values to apply for a layout</p>


  <div class='pre p1 fill-light mt0'>new Layout(settings: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>settings</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>)</code>
	    
          </div>
          
          <table class='mt1 mb2 fixed-table h5 col-12'>
            <colgroup>
              <col width='30%' />
              <col width='70%' />
            </colgroup>
            <thead>
              <tr class='bold fill-light'>
                <th>Name</th>
                <th>Description</th>
              </tr>
            </thead>
            <tbody class='mt1'>
              
                <tr>
  <td class='break-word'><span class='code bold'>settings.layout</span> <code class='quiet'><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
  
    (default <code>&#39;reflowable&#39;</code>)
  </td>
  <td class='break-word'><span></span></td>
</tr>


              
                <tr>
  <td class='break-word'><span class='code bold'>settings.spread</span> <code class='quiet'><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?</code>
  </td>
  <td class='break-word'><span></span></td>
</tr>


              
                <tr>
  <td class='break-word'><span class='code bold'>settings.minSpreadWidth</span> <code class='quiet'><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></code>
  
    (default <code>800</code>)
  </td>
  <td class='break-word'><span></span></td>
</tr>


              
                <tr>
  <td class='break-word'><span class='code bold'>settings.evenSpreads</span> <code class='quiet'><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a></code>
  
    (default <code>false</code>)
  </td>
  <td class='break-word'><span></span></td>
</tr>


              
            </tbody>
          </table>
          
        </div>
      
    </div>
  

  

  

  

  

  

  
    <div class='py1 quiet mt1 prose-big'>Instance Members</div>
    <div class="clearfix">
  
    <div class='border-bottom' id='layoutflow'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>flow(flow)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Switch the flow between paginated and scrolled</p>


  <div class='pre p1 fill-light mt0'>flow(flow: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>flow</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    paginated | scrolled

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>:
        simplified flow

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='layoutspread'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>spread(spread, min)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Switch between using spreads or not, and set the
width at which they switch to single.</p>


  <div class='pre p1 fill-light mt0'>spread(spread: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, min: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>spread</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    "none" | "always" | "auto"

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>min</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
	    integer in pixels

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a></code>:
        spread true | false

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='layoutcalculate'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>calculate(_width, _height, _gap)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Calculate the dimensions of the pagination</p>


  <div class='pre p1 fill-light mt0'>calculate(_width: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>, _height: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>, _gap: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>_width</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
	    width of the rendering

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>_height</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
	    height of the rendering

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>_gap</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
	    width of the gap between columns

          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='layoutformat'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>format(contents)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Apply Css to a Document</p>


  <div class='pre p1 fill-light mt0'>format(contents: <a href="#contents">Contents</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>contents</span> <code class='quiet'>(<a href="#contents">Contents</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a></code>:
        

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='layoutcount'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>count(totalLength, pageLength)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Count number of pages</p>


  <div class='pre p1 fill-light mt0'>count(totalLength: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>, pageLength: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>): {spreads: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">Number</a>, pages: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">Number</a>}</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>totalLength</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>pageLength</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code>{spreads: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">Number</a>, pages: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">Number</a>}</code>:
        

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
</div>

  

  
</section>

          
        
          
            <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='themes'>
      Themes
    </h3>
    
    
  </div>
  

  <p>Themes to apply to displayed content</p>


  <div class='pre p1 fill-light mt0'>new Themes(rendition: <a href="#rendition">Rendition</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>rendition</span> <code class='quiet'>(<a href="#rendition">Rendition</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  
    <div class='py1 quiet mt1 prose-big'>Instance Members</div>
    <div class="clearfix">
  
    <div class='border-bottom' id='themesregister'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>register()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Add themes to be used by a rendition</p>


  <div class='pre p1 fill-light mt0'>register()</div>
  
  

  
  
  
  
  
  

  

  

  

  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'>themes.register(<span class="hljs-string">"light"</span>, <span class="hljs-string">"http://example.com/light.css"</span>)</pre>
    
      
      <pre class='p1 overflow-auto round fill-light'>themes.register(<span class="hljs-string">"light"</span>, { <span class="hljs-string">"body"</span>: { <span class="hljs-string">"color"</span>: <span class="hljs-string">"purple"</span>}})</pre>
    
      
      <pre class='p1 overflow-auto round fill-light'>themes.register({ <span class="hljs-string">"light"</span> : {...}, <span class="hljs-string">"dark"</span> : {...}})</pre>
    
  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='themesdefault'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>default(theme)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Add a default theme to be used by a rendition</p>


  <div class='pre p1 fill-light mt0'>default(theme: (<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a> | <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>))</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>theme</span> <code class='quiet'>((<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a> | <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>))</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'>themes.register(<span class="hljs-string">"http://example.com/default.css"</span>)</pre>
    
      
      <pre class='p1 overflow-auto round fill-light'>themes.register({ <span class="hljs-string">"body"</span>: { <span class="hljs-string">"color"</span>: <span class="hljs-string">"purple"</span>}})</pre>
    
  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='themesregisterthemes'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>registerThemes(themes)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Register themes object</p>


  <div class='pre p1 fill-light mt0'>registerThemes(themes: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>themes</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='themesregisterurl'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>registerUrl(name, input)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Register a url</p>


  <div class='pre p1 fill-light mt0'>registerUrl(name: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, input: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>name</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>input</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='themesregisterrules'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>registerRules(name, rules)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Register rule</p>


  <div class='pre p1 fill-light mt0'>registerRules(name: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, rules: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>name</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>rules</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='themesselect'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>select(name)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Select a theme</p>


  <div class='pre p1 fill-light mt0'>select(name: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>name</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='themesupdate'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>update(name)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Update a theme</p>


  <div class='pre p1 fill-light mt0'>update(name: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>name</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='themesinject'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>inject(contents)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Inject all themes into contents</p>


  <div class='pre p1 fill-light mt0'>inject(contents: <a href="#contents">Contents</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>contents</span> <code class='quiet'>(<a href="#contents">Contents</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='themesadd'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>add(name, contents)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Add Theme to contents</p>


  <div class='pre p1 fill-light mt0'>add(name: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, contents: <a href="#contents">Contents</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>name</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>contents</span> <code class='quiet'>(<a href="#contents">Contents</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='themesoverride'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>override(name, value, priority)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Add override</p>


  <div class='pre p1 fill-light mt0'>override(name: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, value: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, priority: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>name</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>value</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>priority</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='themesoverrides'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>overrides(contents, content)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Add all overrides</p>


  <div class='pre p1 fill-light mt0'>overrides(contents: any, content: Content)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>contents</span> <code class='quiet'>(any)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>content</span> <code class='quiet'>(Content)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='themesfontsize'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>fontSize(size)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Adjust the font size of a rendition</p>


  <div class='pre p1 fill-light mt0'>fontSize(size: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>size</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='themesfont'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>font(f)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Adjust the font-family of a rendition</p>


  <div class='pre p1 fill-light mt0'>font(f: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>f</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
</div>

  

  
</section>

          
        
          
            <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='annotations'>
      Annotations
    </h3>
    
    
  </div>
  

  <p>Handles managing adding &#x26; removing Annotations</p>


  <div class='pre p1 fill-light mt0'>new Annotations(rendition: <a href="#rendition">Rendition</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>rendition</span> <code class='quiet'>(<a href="#rendition">Rendition</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  
    <div class='py1 quiet mt1 prose-big'>Instance Members</div>
    <div class="clearfix">
  
    <div class='border-bottom' id='annotationsadd'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>add(type, cfiRange, data, cb?, className, styles)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Add an annotation to store</p>


  <div class='pre p1 fill-light mt0'>add(type: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, cfiRange: <a href="#epubcfi">EpubCFI</a>, data: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>, cb: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function">function</a>?, className: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, styles: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>): <a href="#annotation">Annotation</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>type</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    Type of annotation to add: "highlight", "underline", "mark"

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>cfiRange</span> <code class='quiet'>(<a href="#epubcfi">EpubCFI</a>)</code>
	    EpubCFI range to attach annotation to

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>data</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>)</code>
	    Data to assign to annotation

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>cb</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function">function</a>?)</code>
	    Callback after annotation is added

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>className</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    CSS class to assign to annotation

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>styles</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>)</code>
	    CSS styles to assign to annotation

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#annotation">Annotation</a></code>:
        annotation

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='annotationsremove'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>remove(cfiRange, type)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Remove an annotation from store</p>


  <div class='pre p1 fill-light mt0'>remove(cfiRange: <a href="#epubcfi">EpubCFI</a>, type: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>cfiRange</span> <code class='quiet'>(<a href="#epubcfi">EpubCFI</a>)</code>
	    EpubCFI range the annotation is attached to

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>type</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    Type of annotation to add: "highlight", "underline", "mark"

          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='annotationshighlight'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>highlight(cfiRange, data, cb, className, styles)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Add a highlight to the store</p>


  <div class='pre p1 fill-light mt0'>highlight(cfiRange: <a href="#epubcfi">EpubCFI</a>, data: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>, cb: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function">function</a>, className: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, styles: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>cfiRange</span> <code class='quiet'>(<a href="#epubcfi">EpubCFI</a>)</code>
	    EpubCFI range to attach annotation to

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>data</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>)</code>
	    Data to assign to annotation

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>cb</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function">function</a>)</code>
	    Callback after annotation is added

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>className</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    CSS class to assign to annotation

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>styles</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>)</code>
	    CSS styles to assign to annotation

          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='annotationsunderline'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>underline(cfiRange, data, cb, className, styles)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Add a underline to the store</p>


  <div class='pre p1 fill-light mt0'>underline(cfiRange: <a href="#epubcfi">EpubCFI</a>, data: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>, cb: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function">function</a>, className: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, styles: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>cfiRange</span> <code class='quiet'>(<a href="#epubcfi">EpubCFI</a>)</code>
	    EpubCFI range to attach annotation to

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>data</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>)</code>
	    Data to assign to annotation

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>cb</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function">function</a>)</code>
	    Callback after annotation is added

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>className</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    CSS class to assign to annotation

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>styles</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>)</code>
	    CSS styles to assign to annotation

          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='annotationsmark'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>mark(cfiRange, data, cb)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Add a mark to the store</p>


  <div class='pre p1 fill-light mt0'>mark(cfiRange: <a href="#epubcfi">EpubCFI</a>, data: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>, cb: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function">function</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>cfiRange</span> <code class='quiet'>(<a href="#epubcfi">EpubCFI</a>)</code>
	    EpubCFI range to attach annotation to

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>data</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>)</code>
	    Data to assign to annotation

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>cb</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function">function</a>)</code>
	    Callback after annotation is added

          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='annotationseach'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>each()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>iterate over annotations in the store</p>


  <div class='pre p1 fill-light mt0'>each()</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='annotationsshow'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>show()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>[Not Implemented] Show annotations</p>


  <div class='pre p1 fill-light mt0'>show()</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='annotationshide'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>hide()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>[Not Implemented] Hide annotations</p>


  <div class='pre p1 fill-light mt0'>hide()</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
</div>

  

  
</section>

          
        
          
            <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='annotation'>
      Annotation
    </h3>
    
    
  </div>
  

  <p>Annotation object</p>


  <div class='pre p1 fill-light mt0'>new Annotation($0: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">Object</a>, options: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>, className: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, styles: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>): <a href="#annotation">Annotation</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>$0</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">Object</a>)</code>
	    
          </div>
          
          <table class='mt1 mb2 fixed-table h5 col-12'>
            <colgroup>
              <col width='30%' />
              <col width='70%' />
            </colgroup>
            <thead>
              <tr class='bold fill-light'>
                <th>Name</th>
                <th>Description</th>
              </tr>
            </thead>
            <tbody class='mt1'>
              
                <tr>
  <td class='break-word'><span class='code bold'>$0.type</span> <code class='quiet'>any</code>
  </td>
  <td class='break-word'><span></span></td>
</tr>


              
                <tr>
  <td class='break-word'><span class='code bold'>$0.cfiRange</span> <code class='quiet'>any</code>
  </td>
  <td class='break-word'><span></span></td>
</tr>


              
                <tr>
  <td class='break-word'><span class='code bold'>$0.data</span> <code class='quiet'>any</code>
  </td>
  <td class='break-word'><span></span></td>
</tr>


              
                <tr>
  <td class='break-word'><span class='code bold'>$0.sectionIndex</span> <code class='quiet'>any</code>
  </td>
  <td class='break-word'><span></span></td>
</tr>


              
                <tr>
  <td class='break-word'><span class='code bold'>$0.cb</span> <code class='quiet'>any</code>
  </td>
  <td class='break-word'><span></span></td>
</tr>


              
                <tr>
  <td class='break-word'><span class='code bold'>$0.className</span> <code class='quiet'>any</code>
  </td>
  <td class='break-word'><span></span></td>
</tr>


              
                <tr>
  <td class='break-word'><span class='code bold'>$0.styles</span> <code class='quiet'>any</code>
  </td>
  <td class='break-word'><span></span></td>
</tr>


              
            </tbody>
          </table>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>options</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>)</code>
	    
          </div>
          
          <table class='mt1 mb2 fixed-table h5 col-12'>
            <colgroup>
              <col width='30%' />
              <col width='70%' />
            </colgroup>
            <thead>
              <tr class='bold fill-light'>
                <th>Name</th>
                <th>Description</th>
              </tr>
            </thead>
            <tbody class='mt1'>
              
                <tr>
  <td class='break-word'><span class='code bold'>options.type</span> <code class='quiet'><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
  </td>
  <td class='break-word'><span>Type of annotation to add: "highlight", "underline", "mark"
</span></td>
</tr>


              
                <tr>
  <td class='break-word'><span class='code bold'>options.cfiRange</span> <code class='quiet'><a href="#epubcfi">EpubCFI</a></code>
  </td>
  <td class='break-word'><span>EpubCFI range to attach annotation to
</span></td>
</tr>


              
                <tr>
  <td class='break-word'><span class='code bold'>options.data</span> <code class='quiet'><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a></code>
  </td>
  <td class='break-word'><span>Data to assign to annotation
</span></td>
</tr>


              
                <tr>
  <td class='break-word'><span class='code bold'>options.sectionIndex</span> <code class='quiet'>int</code>
  </td>
  <td class='break-word'><span>Index in the Spine of the Section annotation belongs to
</span></td>
</tr>


              
                <tr>
  <td class='break-word'><span class='code bold'>options.cb</span> <code class='quiet'><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function">function</a>?</code>
  </td>
  <td class='break-word'><span>Callback after annotation is added
</span></td>
</tr>


              
            </tbody>
          </table>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>className</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    CSS class to assign to annotation

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>styles</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>)</code>
	    CSS styles to assign to annotation

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#annotation">Annotation</a></code>:
        annotation

      
    
  

  

  

  

  
    <div class='py1 quiet mt1 prose-big'>Instance Members</div>
    <div class="clearfix">
  
    <div class='border-bottom' id='annotationupdate'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>update(data)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Update stored data</p>


  <div class='pre p1 fill-light mt0'>update(data: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>data</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='annotationattach'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>attach(view)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Add to a view</p>


  <div class='pre p1 fill-light mt0'>attach(view: View)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>view</span> <code class='quiet'>(View)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='annotationdetach'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>detach(view)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Remove from a view</p>


  <div class='pre p1 fill-light mt0'>detach(view: View)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>view</span> <code class='quiet'>(View)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='annotationtext'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>text()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>[Not Implemented] Get text of an annotation</p>


  <div class='pre p1 fill-light mt0'>text()</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
</div>

  

  
</section>

          
        
          
            <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='epubcfi'>
      EpubCFI
    </h3>
    
    
  </div>
  

  <p>Parsing and creation of EpubCFIs: <a href="http://www.idpf.org/epub/linking/cfi/epub-cfi.html">http://www.idpf.org/epub/linking/cfi/epub-cfi.html</a></p>
<p>Implements:</p>
<ul>
<li>Character Offset: epubcfi(/6/4[chap01ref]!/4[body01]/10[para05]/2/1:3)</li>
<li>Simple Ranges : epubcfi(/6/4[chap01ref]!/4[body01]/10[para05],/2/1:1,/3:4)</li>
</ul>
<p>Does Not Implement:</p>
<ul>
<li>Temporal Offset (~)</li>
<li>Spatial Offset (@)</li>
<li>Temporal-Spatial Offset (~ + @)</li>
<li>Text Location Assertion ([)</li>
</ul>


  <div class='pre p1 fill-light mt0'>new EpubCFI(cfiFrom: (<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Input">Range</a> | <a href="https://developer.mozilla.org/en-US/docs/Web/API/Node/nextSibling">Node</a>)?, base: (<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>)?, ignoreClass: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>cfiFrom</span> <code class='quiet'>((<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Input">Range</a> | <a href="https://developer.mozilla.org/en-US/docs/Web/API/Node/nextSibling">Node</a>)?)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>base</span> <code class='quiet'>((<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>)?)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>ignoreClass</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?)</code>
	    class to ignore when parsing DOM

          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  
    <div class='py1 quiet mt1 prose-big'>Instance Members</div>
    <div class="clearfix">
  
    <div class='border-bottom' id='epubcfiparse'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>parse(cfiStr)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Parse a cfi string to a CFI object representation</p>


  <div class='pre p1 fill-light mt0'>parse(cfiStr: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>cfiStr</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a></code>:
        cfi

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='epubcfitostring'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>toString()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Convert CFI to a epubcfi(...) string</p>


  <div class='pre p1 fill-light mt0'>toString(): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  

  
  
  
  
  
  

  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>:
        epubcfi

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='epubcficompare'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>compare(cfiOne, cfiTwo)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Compare which of two CFIs is earlier in the text</p>


  <div class='pre p1 fill-light mt0'>compare(cfiOne: any, cfiTwo: any): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>cfiOne</span> <code class='quiet'>(any)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>cfiTwo</span> <code class='quiet'>(any)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></code>:
        First is earlier = -1, Second is earlier = 1, They are equal = 0

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='epubcfifromrange'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>fromRange(range, base, ignoreClass?)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Create a CFI object from a Range</p>


  <div class='pre p1 fill-light mt0'>fromRange(range: <a href="https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Input">Range</a>, base: (<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>), ignoreClass: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>range</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Input">Range</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>base</span> <code class='quiet'>((<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>))</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>ignoreClass</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a></code>:
        cfi

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='epubcfifromnode'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>fromNode(anchor, base, ignoreClass?)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Create a CFI object from a Node</p>


  <div class='pre p1 fill-light mt0'>fromNode(anchor: <a href="https://developer.mozilla.org/en-US/docs/Web/API/Node/nextSibling">Node</a>, base: (<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>), ignoreClass: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>anchor</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/API/Node/nextSibling">Node</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>base</span> <code class='quiet'>((<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>))</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>ignoreClass</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a></code>:
        cfi

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='epubcfitorange'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>toRange(_doc, ignoreClass?)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Creates a DOM range representing a CFI</p>


  <div class='pre p1 fill-light mt0'>toRange(_doc: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript">document</a>, ignoreClass: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?): <a href="https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Input">Range</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>_doc</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript">document</a>)</code>
	    document referenced in the base

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>ignoreClass</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Input">Range</a></code>:
        

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='epubcfiiscfistring'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>isCfiString(str)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Check if a string is wrapped with "epubcfi()"</p>


  <div class='pre p1 fill-light mt0'>isCfiString(str: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>str</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a></code>:
        

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='epubcficollapse'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>collapse(toStart)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Collapse a CFI Range to a single CFI Position</p>


  <div class='pre p1 fill-light mt0'>collapse(toStart: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>toStart</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>
            = <code>false</code>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
</div>

  

  
</section>

          
        
          
            <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='contents'>
      Contents
    </h3>
    
    
  </div>
  

  <p>Handles DOM manipulation, queries and events for View contents</p>


  <div class='pre p1 fill-light mt0'>new Contents(doc: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript">document</a>, content: <a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a>, cfiBase: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, sectionIndex: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>doc</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript">document</a>)</code>
	    Document

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>content</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a>)</code>
	    Parent Element (typically Body)

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>cfiBase</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    Section component of CFIs

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>sectionIndex</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
	    Index in Spine of Conntent's Section

          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  
    <div class='py1 quiet mt1 prose-big'>Static Members</div>
    <div class="clearfix">
  
    <div class='border-bottom' id='contentslistenedevents'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>listenedEvents</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Get DOM events that are listened for and passed along</p>


  <div class='pre p1 fill-light mt0'>listenedEvents</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
</div>

  

  
    <div class='py1 quiet mt1 prose-big'>Instance Members</div>
    <div class="clearfix">
  
    <div class='border-bottom' id='contentswidth'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>width(w?)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Get or Set width</p>


  <div class='pre p1 fill-light mt0'>width(w: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>w</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></code>:
        width

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='contentsheight'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>height(h?)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Get or Set height</p>


  <div class='pre p1 fill-light mt0'>height(h: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>h</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></code>:
        height

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='contentscontentwidth'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>contentWidth(w?)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Get or Set width of the contents</p>


  <div class='pre p1 fill-light mt0'>contentWidth(w: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>w</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></code>:
        width

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='contentscontentheight'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>contentHeight(h?)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Get or Set height of the contents</p>


  <div class='pre p1 fill-light mt0'>contentHeight(h: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>h</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></code>:
        height

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='contentstextwidth'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>textWidth()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Get the width of the text using Range</p>


  <div class='pre p1 fill-light mt0'>textWidth(): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></div>
  
  

  
  
  
  
  
  

  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></code>:
        width

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='contentstextheight'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>textHeight()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Get the height of the text using Range</p>


  <div class='pre p1 fill-light mt0'>textHeight(): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></div>
  
  

  
  
  
  
  
  

  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></code>:
        height

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='contentsscrollwidth'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>scrollWidth()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Get documentElement scrollWidth</p>


  <div class='pre p1 fill-light mt0'>scrollWidth(): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></div>
  
  

  
  
  
  
  
  

  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></code>:
        width

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='contentsscrollheight'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>scrollHeight()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Get documentElement scrollHeight</p>


  <div class='pre p1 fill-light mt0'>scrollHeight(): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></div>
  
  

  
  
  
  
  
  

  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></code>:
        height

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='contentsoverflow'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>overflow(overflow?)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Set overflow css style of the contents</p>


  <div class='pre p1 fill-light mt0'>overflow(overflow: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>overflow</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='contentsoverflowx'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>overflowX(overflow?)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Set overflowX css style of the documentElement</p>


  <div class='pre p1 fill-light mt0'>overflowX(overflow: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>overflow</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='contentsoverflowy'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>overflowY(overflow?)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Set overflowY css style of the documentElement</p>


  <div class='pre p1 fill-light mt0'>overflowY(overflow: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>overflow</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='contentscss'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>css(property, value, priority?)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Set Css styles on the contents element (typically Body)</p>


  <div class='pre p1 fill-light mt0'>css(property: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, value: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, priority: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>?)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>property</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>value</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>priority</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>?)</code>
	    set as "important"

          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='contentsviewport'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>viewport(options?)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Get or Set the viewport element</p>


  <div class='pre p1 fill-light mt0'>viewport(options: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>?)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>options</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>?)</code>
	    
          </div>
          
          <table class='mt1 mb2 fixed-table h5 col-12'>
            <colgroup>
              <col width='30%' />
              <col width='70%' />
            </colgroup>
            <thead>
              <tr class='bold fill-light'>
                <th>Name</th>
                <th>Description</th>
              </tr>
            </thead>
            <tbody class='mt1'>
              
                <tr>
  <td class='break-word'><span class='code bold'>options.width</span> <code class='quiet'><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?</code>
  </td>
  <td class='break-word'><span></span></td>
</tr>


              
                <tr>
  <td class='break-word'><span class='code bold'>options.height</span> <code class='quiet'><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?</code>
  </td>
  <td class='break-word'><span></span></td>
</tr>


              
                <tr>
  <td class='break-word'><span class='code bold'>options.scale</span> <code class='quiet'><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?</code>
  </td>
  <td class='break-word'><span></span></td>
</tr>


              
                <tr>
  <td class='break-word'><span class='code bold'>options.minimum</span> <code class='quiet'><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?</code>
  </td>
  <td class='break-word'><span></span></td>
</tr>


              
                <tr>
  <td class='break-word'><span class='code bold'>options.maximum</span> <code class='quiet'><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?</code>
  </td>
  <td class='break-word'><span></span></td>
</tr>


              
                <tr>
  <td class='break-word'><span class='code bold'>options.scalable</span> <code class='quiet'><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?</code>
  </td>
  <td class='break-word'><span></span></td>
</tr>


              
            </tbody>
          </table>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='contentsroot'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>root()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Get the documentElement</p>


  <div class='pre p1 fill-light mt0'>root(): <a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a></div>
  
  

  
  
  
  
  
  

  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a></code>:
        documentElement

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='contentslocationof'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>locationOf(target, ignoreClass?)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Get the location offset of a EpubCFI or an #id</p>


  <div class='pre p1 fill-light mt0'>locationOf(target: (<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="#epubcfi">EpubCFI</a>), ignoreClass: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>target</span> <code class='quiet'>((<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="#epubcfi">EpubCFI</a>))</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>ignoreClass</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?)</code>
	    for the cfi

          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='contentsaddstylesheet'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>addStylesheet(src)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Append a stylesheet link to the document head</p>


  <div class='pre p1 fill-light mt0'>addStylesheet(src: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>src</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    url

          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='contentsaddstylesheetrules'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>addStylesheetRules(rules)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Append stylesheet rules to a generate stylesheet
Array: <a href="https://developer.mozilla.org/en-US/docs/Web/API/CSSStyleSheet/insertRule">https://developer.mozilla.org/en-US/docs/Web/API/CSSStyleSheet/insertRule</a>
Object: <a href="https://github.com/desirable-objects/json-to-css">https://github.com/desirable-objects/json-to-css</a></p>


  <div class='pre p1 fill-light mt0'>addStylesheetRules(rules: (<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array">array</a> | <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>))</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>rules</span> <code class='quiet'>((<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array">array</a> | <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>))</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='contentsaddscript'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>addScript(src)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Append a script tag to the document head</p>


  <div class='pre p1 fill-light mt0'>addScript(src: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>src</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    url

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise">Promise</a></code>:
        loaded

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='contentsaddclass'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>addClass(className)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Add a class to the contents container</p>


  <div class='pre p1 fill-light mt0'>addClass(className: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>className</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='contentsremoveclass'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>removeClass(className, removeClass)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Remove a class from the contents container</p>


  <div class='pre p1 fill-light mt0'>removeClass(className: any, removeClass: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>className</span> <code class='quiet'>(any)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>removeClass</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='contentsrange'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>range(_cfi, ignoreClass?)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Get a Dom Range from EpubCFI</p>


  <div class='pre p1 fill-light mt0'>range(_cfi: <a href="#epubcfi">EpubCFI</a>, ignoreClass: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?): <a href="https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Input">Range</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>_cfi</span> <code class='quiet'>(<a href="#epubcfi">EpubCFI</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>ignoreClass</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Input">Range</a></code>:
        range

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='contentscfifromrange'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>cfiFromRange(range, ignoreClass?)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Get an EpubCFI from a Dom Range</p>


  <div class='pre p1 fill-light mt0'>cfiFromRange(range: <a href="https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Input">Range</a>, ignoreClass: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?): <a href="#epubcfi">EpubCFI</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>range</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/HTML/Element/Input">Range</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>ignoreClass</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#epubcfi">EpubCFI</a></code>:
        cfi

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='contentscfifromnode'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>cfiFromNode(node, ignoreClass?)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Get an EpubCFI from a Dom node</p>


  <div class='pre p1 fill-light mt0'>cfiFromNode(node: <a href="https://developer.mozilla.org/en-US/docs/Web/API/Node/nextSibling">node</a>, ignoreClass: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?): <a href="#epubcfi">EpubCFI</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>node</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/API/Node/nextSibling">node</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>ignoreClass</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#epubcfi">EpubCFI</a></code>:
        cfi

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='contentssize'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>size(width?, height?)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Size the contents to a given width and height</p>


  <div class='pre p1 fill-light mt0'>size(width: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?, height: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>width</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>height</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='contentscolumns'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>columns(width, height, columnWidth, gap)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Apply columns to the contents for pagination</p>


  <div class='pre p1 fill-light mt0'>columns(width: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>, height: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>, columnWidth: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>, gap: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>width</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>height</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>columnWidth</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>gap</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='contentsscaler'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>scaler(scale, offsetX, offsetY)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Scale contents from center</p>


  <div class='pre p1 fill-light mt0'>scaler(scale: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>, offsetX: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>, offsetY: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>scale</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>offsetX</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>offsetY</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='contentsfit'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>fit(width, height)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Fit contents into a fixed width and height</p>


  <div class='pre p1 fill-light mt0'>fit(width: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>, height: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>width</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>height</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='contentsdirection'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>direction(dir)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Set the direction of the text</p>


  <div class='pre p1 fill-light mt0'>direction(dir: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>dir</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
            = <code>&quot;ltr&quot;</code>)</code>
	    "rtl" | "ltr"

          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='contentswritingmode'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>writingMode(mode)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Set the writingMode of the text</p>


  <div class='pre p1 fill-light mt0'>writingMode(mode: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>mode</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
            = <code>&quot;horizontal-tb&quot;</code>)</code>
	    "horizontal-tb" | "vertical-rl" | "vertical-lr"

          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
</div>

  

  
</section>

          
        
          
            <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='mapping'>
      Mapping
    </h3>
    
    
  </div>
  

  <p>Map text locations to CFI ranges</p>


  <div class='pre p1 fill-light mt0'>new Mapping(layout: <a href="#layout">Layout</a>, direction: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, axis: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, dev: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>?)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>layout</span> <code class='quiet'>(<a href="#layout">Layout</a>)</code>
	    Layout to apply

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>direction</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
            = <code>&quot;ltr&quot;</code>)</code>
	    Text direction

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>axis</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
            = <code>&quot;horizontal&quot;</code>)</code>
	    vertical or horizontal axis

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>dev</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>?)</code>
	    toggle developer highlighting

          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  
    <div class='py1 quiet mt1 prose-big'>Instance Members</div>
    <div class="clearfix">
  
    <div class='border-bottom' id='mappingsection'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>section(view)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Find CFI pairs for entire section at once</p>


  <div class='pre p1 fill-light mt0'>section(view: any)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>view</span> <code class='quiet'>(any)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='mappingpage'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>page(contents, cfiBase, start, end)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Find CFI pairs for a page</p>


  <div class='pre p1 fill-light mt0'>page(contents: <a href="#contents">Contents</a>, cfiBase: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, start: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>, end: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>contents</span> <code class='quiet'>(<a href="#contents">Contents</a>)</code>
	    Contents from view

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>cfiBase</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    string of the base for a cfi

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>start</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
	    position to start at

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>end</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
	    position to end at

          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='mappingaxis'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>axis(axis)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Set the axis for mapping</p>


  <div class='pre p1 fill-light mt0'>axis(axis: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>axis</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    horizontal | vertical

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a></code>:
        is it horizontal?

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
</div>

  

  
</section>

          
        
          
            <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='core'>
      Core
    </h3>
    
    
  </div>
  

  <p>Core Utilities and Helpers</p>


  <div class='pre p1 fill-light mt0'>Core</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  
    <div class='py1 quiet mt1 prose-big'>Static Members</div>
    <div class="clearfix">
  
    <div class='border-bottom' id='corerequestanimationframe'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>requestAnimationFrame</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Vendor prefixed requestAnimationFrame</p>


  <div class='pre p1 fill-light mt0'>requestAnimationFrame</div>
  
  

  
  
  
  
  
  

  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function">function</a></code>:
        requestAnimationFrame

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='coreuuid'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>uuid()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Generates a UUID
based on: <a href="http://stackoverflow.com/questions/105034/how-to-create-a-guid-uuid-in-javascript">http://stackoverflow.com/questions/105034/how-to-create-a-guid-uuid-in-javascript</a></p>


  <div class='pre p1 fill-light mt0'>uuid(): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  

  
  
  
  
  
  

  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>:
        uuid

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='coredocumentheight'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>documentHeight()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Gets the height of a document</p>


  <div class='pre p1 fill-light mt0'>documentHeight(): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></div>
  
  

  
  
  
  
  
  

  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></code>:
        height

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='coreiselement'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>isElement(obj)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Checks if a node is an element</p>


  <div class='pre p1 fill-light mt0'>isElement(obj: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>obj</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a></code>:
        

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='coreisnumber'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>isNumber(n)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  

  <div class='pre p1 fill-light mt0'>isNumber(n: any): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>n</span> <code class='quiet'>(any)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a></code>:
        

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='coreisfloat'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>isFloat(n)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  

  <div class='pre p1 fill-light mt0'>isFloat(n: any): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>n</span> <code class='quiet'>(any)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a></code>:
        

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='coreprefixed'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>prefixed(unprefixed)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Get a prefixed css property</p>


  <div class='pre p1 fill-light mt0'>prefixed(unprefixed: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>unprefixed</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>:
        

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='coredefaults'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>defaults(obj)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Apply defaults to an object</p>


  <div class='pre p1 fill-light mt0'>defaults(obj: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>obj</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a></code>:
        

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='coreextend'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>extend(target)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Extend properties of an object</p>


  <div class='pre p1 fill-light mt0'>extend(target: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>target</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a></code>:
        

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='coreinsert'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>insert(item, array, compareFunction?)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Fast quicksort insert for sorted array -- based on:
<a href="http://stackoverflow.com/questions/1344500/efficient-way-to-insert-a-number-into-a-sorted-array-of-numbers">http://stackoverflow.com/questions/1344500/efficient-way-to-insert-a-number-into-a-sorted-array-of-numbers</a></p>


  <div class='pre p1 fill-light mt0'>insert(item: any, array: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array">array</a>, compareFunction: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function">function</a>?): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>item</span> <code class='quiet'>(any)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>array</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array">array</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>compareFunction</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function">function</a>?)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></code>:
        location (in array)

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='corelocationof'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>locationOf(item, array, compareFunction?, _start?, _end?)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Finds where something would fit into a sorted array</p>


  <div class='pre p1 fill-light mt0'>locationOf(item: any, array: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array">array</a>, compareFunction: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function">function</a>?, _start: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function">function</a>?, _end: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function">function</a>?): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>item</span> <code class='quiet'>(any)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>array</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array">array</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>compareFunction</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function">function</a>?)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>_start</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function">function</a>?)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>_end</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function">function</a>?)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></code>:
        location (in array)

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='coreindexofsorted'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>indexOfSorted(item, array, compareFunction?, _start?, _end?)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Finds index of something in a sorted array
Returns -1 if not found</p>


  <div class='pre p1 fill-light mt0'>indexOfSorted(item: any, array: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array">array</a>, compareFunction: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function">function</a>?, _start: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function">function</a>?, _end: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function">function</a>?): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>item</span> <code class='quiet'>(any)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>array</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array">array</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>compareFunction</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function">function</a>?)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>_start</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function">function</a>?)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>_end</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function">function</a>?)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></code>:
        index (in array) or -1

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='corebounds'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>bounds(el)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Find the bounds of an element
taking padding and margin into account</p>


  <div class='pre p1 fill-light mt0'>bounds(el: <a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a>): {width: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">Number</a>, height: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">Number</a>}</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>el</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code>{width: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">Number</a>, height: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">Number</a>}</code>:
        

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='coreborders'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>borders(el)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Find the bounds of an element
taking padding, margin and borders into account</p>


  <div class='pre p1 fill-light mt0'>borders(el: <a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a>): {width: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">Number</a>, height: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">Number</a>}</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>el</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code>{width: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">Number</a>, height: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">Number</a>}</code>:
        

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='corenodebounds'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>nodeBounds(node)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Find the bounds of any node
allows for getting bounds of text nodes by wrapping them in a range</p>


  <div class='pre p1 fill-light mt0'>nodeBounds(node: <a href="https://developer.mozilla.org/en-US/docs/Web/API/Node/nextSibling">node</a>): BoundingClientRect</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>node</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/API/Node/nextSibling">node</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code>BoundingClientRect</code>:
        

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='corewindowbounds'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>windowBounds()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Find the equivelent of getBoundingClientRect of a browser window</p>


  <div class='pre p1 fill-light mt0'>windowBounds(): {width: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">Number</a>, height: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">Number</a>, top: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">Number</a>, left: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">Number</a>, right: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">Number</a>, bottom: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">Number</a>}</div>
  
  

  
  
  
  
  
  

  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code>{width: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">Number</a>, height: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">Number</a>, top: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">Number</a>, left: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">Number</a>, right: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">Number</a>, bottom: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">Number</a>}</code>:
        

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='coreindexofnode'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>indexOfNode(node, typeId)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Gets the index of a node in its parent</p>


  <div class='pre p1 fill-light mt0'>indexOfNode(node: <a href="https://developer.mozilla.org/en-US/docs/Web/API/Node/nextSibling">Node</a>, typeId: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>node</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/API/Node/nextSibling">Node</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>typeId</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></code>:
        index

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='coreindexoftextnode'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>indexOfTextNode(textNode)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Gets the index of a text node in its parent</p>


  <div class='pre p1 fill-light mt0'>indexOfTextNode(textNode: <a href="https://developer.mozilla.org/en-US/docs/Web/API/Node/nextSibling">node</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>textNode</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/API/Node/nextSibling">node</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></code>:
        index

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='coreindexofelementnode'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>indexOfElementNode(elementNode)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Gets the index of an element node in its parent</p>


  <div class='pre p1 fill-light mt0'>indexOfElementNode(elementNode: <a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>elementNode</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></code>:
        index

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='coreisxml'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>isXml(ext)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Check if extension is xml</p>


  <div class='pre p1 fill-light mt0'>isXml(ext: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>ext</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a></code>:
        

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='corecreateblob'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>createBlob(content, mime)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Create a new blob</p>


  <div class='pre p1 fill-light mt0'>createBlob(content: any, mime: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/API/Blob">Blob</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>content</span> <code class='quiet'>(any)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>mime</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/API/Blob">Blob</a></code>:
        

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='corecreatebloburl'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>createBlobUrl(content, mime)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Create a new blob url</p>


  <div class='pre p1 fill-light mt0'>createBlobUrl(content: any, mime: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>content</span> <code class='quiet'>(any)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>mime</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>:
        url

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='corerevokebloburl'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>revokeBlobUrl(url)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Remove a blob url</p>


  <div class='pre p1 fill-light mt0'>revokeBlobUrl(url: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>url</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='corecreatebase64url'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>createBase64Url(content, mime)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Create a new base64 encoded url</p>


  <div class='pre p1 fill-light mt0'>createBase64Url(content: any, mime: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>content</span> <code class='quiet'>(any)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>mime</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>:
        url

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='coretype'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>type(obj)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Get type of an object</p>


  <div class='pre p1 fill-light mt0'>type(obj: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>obj</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>:
        type

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='coreparse'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>parse(markup, mime, forceXMLDom)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Parse xml (or html) markup</p>


  <div class='pre p1 fill-light mt0'>parse(markup: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, mime: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, forceXMLDom: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript">document</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>markup</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>mime</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>forceXMLDom</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>)</code>
	    force using xmlDom to parse instead of native parser

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript">document</a></code>:
        document

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='coreqs'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>qs(el, sel)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>querySelector polyfill</p>


  <div class='pre p1 fill-light mt0'>qs(el: <a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a>, sel: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>el</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>sel</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    selector string

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a></code>:
        element

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='coreqsa'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>qsa(el, sel)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>querySelectorAll polyfill</p>


  <div class='pre p1 fill-light mt0'>qsa(el: <a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a>, sel: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>el</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>sel</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    selector string

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a>></code>:
        elements

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='coreqsp'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>qsp(el, sel, props)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>querySelector by property</p>


  <div class='pre p1 fill-light mt0'>qsp(el: <a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a>, sel: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, props: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>el</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>sel</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    selector string

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>props</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a>></code>:
        elements

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='coresprint'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>sprint(root, func)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Sprint through all text nodes in a document</p>


  <div class='pre p1 fill-light mt0'>sprint(root: <a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a>, func: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function">function</a>)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>root</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a>)</code>
	    element to start with

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>func</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function">function</a>)</code>
	    function to run on each element

          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='coretreewalker'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>treeWalker(root, func, filter)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Create a treeWalker</p>


  <div class='pre p1 fill-light mt0'>treeWalker(root: <a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a>, func: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function">function</a>, filter: (<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function">function</a> | <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>))</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>root</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a>)</code>
	    element to start with

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>func</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function">function</a>)</code>
	    function to run on each element

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>filter</span> <code class='quiet'>((<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/function">function</a> | <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object">object</a>))</code>
	    funtion or object to filter with

          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='corewalk'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>walk(node, callback, return)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  

  <div class='pre p1 fill-light mt0'>walk(node: <a href="https://developer.mozilla.org/en-US/docs/Web/API/Node/nextSibling">node</a>, callback: any, return: callback)</div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>node</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/API/Node/nextSibling">node</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>callback</span> <code class='quiet'>(any)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>return</span> <code class='quiet'>(callback)</code>
	    false for continue,true for break inside callback

          </div>
          
        </div>
      
    </div>
  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='coreblob2base64'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>blob2base64(blob)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Convert a blob to a base64 encoded string</p>


  <div class='pre p1 fill-light mt0'>blob2base64(blob: Blog): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>blob</span> <code class='quiet'>(Blog)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>:
        

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='coredefer'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>defer()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Creates a new pending promise and provides methods to resolve or reject it.
From: <a href="https://developer.mozilla.org/en-US/docs/Mozilla/JavaScript_code_modules/Promise.jsm/Deferred#backwards_forwards_compatible">https://developer.mozilla.org/en-US/docs/Mozilla/JavaScript_code_modules/Promise.jsm/Deferred#backwards_forwards_compatible</a></p>


  <div class='pre p1 fill-light mt0'>defer()</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='corequeryselectorbytype'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>querySelectorByType(html, element, type)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>querySelector with filter by epub type</p>


  <div class='pre p1 fill-light mt0'>querySelectorByType(html: <a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a>, element: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, type: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>html</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>element</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    element type to find

          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>type</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    epub type to find

          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a>></code>:
        elements

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='corefindchildren'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>findChildren(el)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Find direct decendents of an element</p>


  <div class='pre p1 fill-light mt0'>findChildren(el: <a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>el</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a>></code>:
        children

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='coreparents'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>parents(node)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Find all parents (ancestors) of an element</p>


  <div class='pre p1 fill-light mt0'>parents(node: <a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>node</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a>></code>:
        parents

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='corefilterchildren'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>filterChildren(el, nodeName, single?)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Find all direct decendents of a specific type</p>


  <div class='pre p1 fill-light mt0'>filterChildren(el: <a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a>, nodeName: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, single: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>?): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>el</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>nodeName</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>single</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>?)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a>></code>:
        children

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='coregetparentbytagname'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>getParentByTagName(node, tagname)</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Filter all parents (ancestors) with tag name</p>


  <div class='pre p1 fill-light mt0'>getParentByTagName(node: <a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a>, tagname: <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a>></div>
  
  

  
  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>node</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>tagname</span> <code class='quiet'>(<a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="https://developer.mozilla.org/en-US/docs/Web/API/Element">element</a>></code>:
        parents

      
    
  

  

  

  

  

  
</section>

      </div>
    </div>
  
    <div class='border-bottom' id='corerangeobject'>
      <div class="clearfix small pointer toggle-sibling">
        <div class="py1 contain">
            <a class='icon pin-right py1 dark-link caret-right'>▸</a>
            <span class='code strong strong truncate'>new RangeObject()</span>
        </div>
      </div>
      <div class="clearfix display-none toggle-target">
        <section class='p2 mb2 clearfix bg-white minishadow'>

  

  <p>Lightweight Polyfill for DOM Range</p>


  <div class='pre p1 fill-light mt0'>new RangeObject()</div>
  
  

  
  
  
  
  
  

  

  

  

  

  

  

  

  
</section>

      </div>
    </div>
  
</div>

  

  

  
</section>

          
        
      </div>
    </div>
  <script src='assets/anchor.js'></script>
  <script src='assets/split.js'></script>
  <script src='assets/site.js'></script>
</body>
</html>
