{"name": "epubjs", "version": "0.3.93", "description": "Parse and Render Epubs", "main": "lib/index.js", "module": "src/index.js", "types": "types/index.d.ts", "repository": "https://github.com/futurepress/epub.js", "directories": {"test": "test"}, "scripts": {"test": "karma start --single-run --browsers ChromeHeadlessNoSandbox", "docs": "documentation build src/epub.js -f html -o documentation/html/", "docs:html": "documentation build src/epub.js -f html -o documentation/html/", "docs:md": "documentation build src/epub.js -f md -o documentation/md/API.md", "lint": "eslint -c .eslintrc.js src; exit 0", "start": "webpack-dev-server --inline --d", "build": "NODE_ENV=production webpack --progress", "minify": "NODE_ENV=production MINIMIZE=true webpack --progress", "legacy": "NODE_ENV=production LEGACY=true webpack --progress", "productionLegacy": "NODE_ENV=production MINIMIZE=true LEGACY=true webpack --progress", "compile": "babel -d lib/ src/", "watch": "babel --watch -d lib/ src/", "prepare": "npm run compile && npm run build && npm run minify && npm run legacy && npm run productionLegacy"}, "author": "<EMAIL>", "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"@babel/cli": "^7.15.7", "@babel/core": "^7.15.8", "@babel/plugin-proposal-export-default-from": "^7.14.5", "@babel/plugin-proposal-export-namespace-from": "^7.14.5", "@babel/preset-env": "^7.15.8", "@babel/runtime": "^7.15.4", "babel-loader": "^8.2.3", "documentation": "^13.2.5", "eslint": "^8.0.1", "jsdoc": "^3.6.7", "karma": "^5.0.9", "karma-chrome-launcher": "^3.1.0", "karma-mocha": "^2.0.1", "karma-mocha-reporter": "^2.2.5", "karma-phantomjs-launcher": "^1.0.4", "karma-sourcemap-loader": "^0.3.7", "karma-webpack": "^4.0.2", "mocha": "^7.2.0", "mocha-loader": "^5.0.0", "raw-loader": "^4.0.2", "terser-webpack-plugin": "^3.0.3", "tsd-jsdoc": "^2.5.0", "webpack": "^4.43.0", "webpack-cli": "^3.3.11", "webpack-dev-middleware": "^3.7.2", "webpack-dev-server": "^3.11.0"}, "dependencies": {"@types/localforage": "0.0.34", "@xmldom/xmldom": "^0.7.5", "core-js": "^3.18.3", "event-emitter": "^0.3.5", "jszip": "^3.7.1", "localforage": "^1.10.0", "lodash": "^4.17.21", "marks-pane": "^1.0.9", "path-webpack": "0.0.3"}}