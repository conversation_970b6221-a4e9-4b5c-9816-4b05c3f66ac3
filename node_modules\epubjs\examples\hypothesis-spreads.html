<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>EPUB.js + Hypothes.is Example</title>

  <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.5/jszip.min.js"></script>
  <script src="../dist/epub.js"></script>

  <script type="text/javascript">
    window.hypothesisConfig = function () {
      return {
        openSidebar: false,
        enableMultiFrameSupport: true,
        onLayoutChange: function(state) {
          var nav = document.getElementById("navigation");
          if (state.expanded === true) {
            nav.classList.remove("open");
          }
        }
      };
    };
  </script>
  <script src="https://cdn.hypothes.is/hypothesis"></script>

  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link rel="stylesheet" type="text/css" href="hypothesis.css">

  <script src="hypothesis.js"></script>

</head>
<body>
  <div id="main">
    <a id="opener">
      <i class="material-icons">menu</i>
    </a>
    <div id="viewer" class="spreads"></div>
    <span id="hiddenTitle"></span>
    <a id="prev" href="#prev" class="arrow">
      <i class="material-icons">chevron_left</i>
    </a>
    <a id="next" href="#next" class="arrow">
      <i class="material-icons">chevron_right</i>
    </a>
  </div>
  <div id="navigation">
    <a id="closer">
      <i class="material-icons">close</i>
    </a>
    <h1 id="title">...</h1>
    <image id="cover" width="150px"/>
    <h2 id="author">...</h2>
    <ul id="toc"></ul>
  </div>

</body>
</html>
