/**
 * Markdown Parser Implementation
 * Handles Markdown documents with table of contents extraction
 */

import RNFS from 'react-native-fs';
import {
  DocumentFormat,
  DocumentParser,
  DocumentParseResult,
  DocumentParseOptions,
  DocumentMetadata,
  DocumentContent,
  DocumentChapter,
  DocumentTableOfContents,
  ParsedDocument,
  DocumentError,
  DocumentProcessingError,
} from '../../../types/document';

export class MarkdownParser implements DocumentParser {
  supportedFormats: DocumentFormat[] = [DocumentFormat.MD, DocumentFormat.HTML];

  canParse(filePath: string, mimeType?: string): boolean {
    const extension = filePath.toLowerCase().split('.').pop();
    return (
      extension === 'md' ||
      extension === 'markdown' ||
      extension === 'html' ||
      extension === 'htm' ||
      mimeType === 'text/markdown' ||
      mimeType === 'text/html'
    );
  }

  async validateDocument(filePath: string): Promise<boolean> {
    try {
      // Check if file exists and is readable
      const exists = await RNFS.exists(filePath);
      if (!exists) {
        return false;
      }

      // For markdown/HTML, just check if it's a text file
      const sample = await RNFS.read(filePath, 1024, 0, 'utf8');
      return !sample.includes('\0');
    } catch (error) {
      console.error('Markdown validation error:', error);
      return false;
    }
  }

  async parse(filePath: string, options: DocumentParseOptions = {
    extractText: true,
    extractMetadata: true,
    extractTableOfContents: true,
    generateThumbnail: false,
  }): Promise<DocumentParseResult> {
    try {
      const metadata = await this.extractMetadataFromFile(filePath);
      let content: DocumentContent = { text: '' };
      let tableOfContents: DocumentTableOfContents | undefined;

      // Extract content if requested
      if (options.extractText) {
        content = await this.extractContent(filePath, options);
      }

      // Extract table of contents if requested
      if (options.extractTableOfContents) {
        tableOfContents = await this.extractTableOfContents(filePath);
      }

      const parsedDocument: ParsedDocument = {
        metadata,
        content,
        tableOfContents,
      };

      return {
        success: true,
        document: parsedDocument,
      };
    } catch (error) {
      console.error('Markdown parsing error:', error);
      return {
        success: false,
        error: `Failed to parse Markdown: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }

  async extractMetadata(filePath: string): Promise<DocumentMetadata> {
    try {
      return await this.extractMetadataFromFile(filePath);
    } catch (error) {
      throw new DocumentProcessingError(
        DocumentError.PARSING_ERROR,
        `Failed to extract Markdown metadata: ${error instanceof Error ? error.message : 'Unknown error'}`,
        filePath,
        error instanceof Error ? error : undefined,
      );
    }
  }

  private async extractMetadataFromFile(filePath: string): Promise<DocumentMetadata> {
    const stats = await RNFS.stat(filePath);
    const extension = filePath.toLowerCase().split('.').pop();
    
    // Determine format based on extension
    const format = extension === 'html' || extension === 'htm' ? DocumentFormat.HTML : DocumentFormat.MD;
    const mimeType = format === DocumentFormat.HTML ? 'text/html' : 'text/markdown';
    
    // Generate unique ID based on file path and modification time
    const id = this.generateDocumentId(filePath, stats.mtime, format);

    // Extract frontmatter metadata if available
    const frontmatterData = await this.extractFrontmatter(filePath);
    const textStats = await this.extractTextStatistics(filePath);

    return {
      id,
      title: frontmatterData.title || this.getFileNameWithoutExtension(filePath),
      author: frontmatterData.author,
      publishedDate: frontmatterData.date,
      description: frontmatterData.description,
      fileSize: stats.size,
      format,
      mimeType,
      createdAt: new Date(stats.ctime),
      modifiedAt: new Date(stats.mtime),
      readingProgress: 0,
      totalReadingTime: 0,
      wordCount: textStats.wordCount,
    };
  }

  private async extractFrontmatter(filePath: string): Promise<{
    title?: string;
    author?: string;
    date?: string;
    description?: string;
  }> {
    try {
      const content = await RNFS.read(filePath, 2048, 0, 'utf8');
      
      // Check for YAML frontmatter
      const yamlMatch = content.match(/^---\s*\n([\s\S]*?)\n---/);
      if (yamlMatch) {
        return this.parseYAMLFrontmatter(yamlMatch[1]);
      }

      // Check for HTML title
      const htmlTitleMatch = content.match(/<title[^>]*>([^<]+)<\/title>/i);
      if (htmlTitleMatch) {
        return { title: htmlTitleMatch[1].trim() };
      }

      // Check for first heading as title
      const headingMatch = content.match(/^#\s+(.+)$/m);
      if (headingMatch) {
        return { title: headingMatch[1].trim() };
      }

      return {};
    } catch (error) {
      console.warn('Failed to extract frontmatter:', error);
      return {};
    }
  }

  private parseYAMLFrontmatter(yaml: string): {
    title?: string;
    author?: string;
    date?: string;
    description?: string;
  } {
    const metadata: Record<string, unknown> = {};
    
    // Simple YAML parsing for common fields
    const lines = yaml.split('\n');
    for (const line of lines) {
      const match = line.match(/^(\w+):\s*(.+)$/);
      if (match) {
        const [, key, value] = match;
        metadata[key.toLowerCase()] = value.replace(/^["']|["']$/g, '');
      }
    }

    return {
      title: metadata.title,
      author: metadata.author,
      date: metadata.date,
      description: metadata.description,
    };
  }

  private async extractTextStatistics(filePath: string): Promise<{
    wordCount: number;
  }> {
    try {
      const content = await RNFS.readFile(filePath, 'utf8');
      const plainText = this.markdownToPlainText(content);
      
      return {
        wordCount: this.countWords(plainText),
      };
    } catch (error) {
      console.warn('Failed to extract text statistics:', error);
      return { wordCount: 0 };
    }
  }

  private async extractContent(filePath: string, options: DocumentParseOptions): Promise<DocumentContent> {
    try {
      let content = await RNFS.readFile(filePath, 'utf8');
      
      // Apply text length limit if specified
      if (options.maxTextLength && content.length > options.maxTextLength) {
        content = content.substring(0, options.maxTextLength);
      }

      const plainText = this.markdownToPlainText(content);
      const html = this.markdownToHTML(content);

      return {
        text: plainText,
        html,
      };
    } catch (error) {
      console.error('Failed to extract Markdown content:', error);
      return {
        text: '',
      };
    }
  }

  private async extractTableOfContents(filePath: string): Promise<DocumentTableOfContents> {
    try {
      const content = await RNFS.readFile(filePath, 'utf8');
      const chapters: DocumentChapter[] = [];

      // Extract headings for table of contents
      const headingRegex = /^(#{1,6})\s+(.+)$/gm;
      let match;
      let chapterIndex = 0;

      while ((match = headingRegex.exec(content)) !== null) {
        const level = match[1].length - 1; // Convert # count to 0-based level
        const title = match[2].trim();
        
        chapters.push({
          id: `heading-${chapterIndex}`,
          title,
          href: `#${this.slugify(title)}`,
          level,
        });
        
        chapterIndex++;
      }

      return {
        chapters,
        totalChapters: chapters.length,
      };
    } catch (error) {
      console.warn('Failed to extract table of contents:', error);
      return {
        chapters: [],
        totalChapters: 0,
      };
    }
  }

  private markdownToPlainText(markdown: string): string {
    // Remove frontmatter
    let text = markdown.replace(/^---\s*\n[\s\S]*?\n---\s*\n/, '');
    
    // Remove markdown syntax
    text = text
      .replace(/^#{1,6}\s+/gm, '') // Headers
      .replace(/\*\*(.*?)\*\*/g, '$1') // Bold
      .replace(/\*(.*?)\*/g, '$1') // Italic
      .replace(/`(.*?)`/g, '$1') // Inline code
      .replace(/```[\s\S]*?```/g, '') // Code blocks
      .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // Links
      .replace(/!\[([^\]]*)\]\([^)]+\)/g, '$1') // Images
      .replace(/^[-*+]\s+/gm, '') // List items
      .replace(/^\d+\.\s+/gm, '') // Numbered lists
      .replace(/^>\s+/gm, '') // Blockquotes
      .replace(/\n{3,}/g, '\n\n'); // Multiple newlines

    return text.trim();
  }

  private markdownToHTML(markdown: string): string {
    // Basic markdown to HTML conversion
    let html = markdown;

    // Headers
    html = html.replace(/^### (.*$)/gm, '<h3>$1</h3>');
    html = html.replace(/^## (.*$)/gm, '<h2>$1</h2>');
    html = html.replace(/^# (.*$)/gm, '<h1>$1</h1>');

    // Bold and italic
    html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
    html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');

    // Links
    html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>');

    // Line breaks
    html = html.replace(/\n/g, '<br>');

    return html;
  }

  private slugify(text: string): string {
    return text
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-');
  }

  private countWords(text: string): number {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  private generateDocumentId(filePath: string, modificationTime: string, format: DocumentFormat): string {
    const fileName = this.getFileNameWithoutExtension(filePath);
    const timestamp = new Date(modificationTime).getTime();
    return `${format}-${fileName}-${timestamp}`;
  }

  private getFileNameWithoutExtension(filePath: string): string {
    const fileName = filePath.split('/').pop() || filePath;
    const lastDotIndex = fileName.lastIndexOf('.');
    return lastDotIndex !== -1 ? fileName.substring(0, lastDotIndex) : fileName;
  }
}
