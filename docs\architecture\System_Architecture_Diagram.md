# System Architecture Diagram - InkSight

## Overview

This document provides a comprehensive system architecture diagram and detailed explanation of InkSight's technical architecture, showcasing the offline AI integration, privacy-first design, and modular component structure.

## High-Level System Architecture

### Architecture Diagram

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                           InkSight System Architecture                       │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│  ┌─────────────────────────────────────────────────────────────────────┐   │
│  │                     Presentation Layer                              │   │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐   │   │
│  │  │   React     │ │  Material   │ │ Navigation  │ │ Responsive  │   │   │
│  │  │  Native     │ │  Design 3   │ │   System    │ │   Layout    │   │   │
│  │  │ Components  │ │ Components  │ │             │ │   Manager   │   │   │
│  │  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘   │   │
│  └─────────────────────────────────────────────────────────────────────┘   │
│                                    │                                        │
│  ┌─────────────────────────────────────────────────────────────────────┐   │
│  │                      Business Logic Layer                          │   │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐   │   │
│  │  │  Document   │ │ Annotation  │ │   Search    │ │    Focus    │   │   │
│  │  │   Reading   │ │   System    │ │  & Discovery│ │    Mode     │   │   │
│  │  │   Engine    │ │             │ │             │ │  Controller │   │   │
│  │  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘   │   │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐   │   │
│  │  │ Handwriting │ │     AI      │ │ Read-Later  │ │   Privacy   │   │   │
│  │  │ Recognition │ │Summarization│ │   Manager   │ │ Controller  │   │   │
│  │  │   System    │ │   Engine    │ │             │ │             │   │   │
│  │  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘   │   │
│  └─────────────────────────────────────────────────────────────────────┘   │
│                                    │                                        │
│  ┌─────────────────────────────────────────────────────────────────────┐   │
│  │                        AI/ML Layer                                 │   │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐   │   │
│  │  │   Vision    │ │     mT5     │ │    T5       │ │  Sentence   │   │   │
│  │  │Transformer  │ │ Multilingual│ │Summarization│ │Transformers │   │   │
│  │  │   (ViT)     │ │    Model    │ │    Model    │ │   (Search)  │   │   │
│  │  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘   │   │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐   │   │
│  │  │ TensorFlow  │ │    OCR      │ │   Model     │ │ Inference   │   │   │
│  │  │    Lite     │ │ Integration │ │ Optimization│ │   Engine    │   │   │
│  │  │  Runtime    │ │(Tesseract)  │ │   Manager   │ │             │   │   │
│  │  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘   │   │
│  └─────────────────────────────────────────────────────────────────────┘   │
│                                    │                                        │
│  ┌─────────────────────────────────────────────────────────────────────┐   │
│  │                       Data Access Layer                            │   │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐   │   │
│  │  │  Document   │ │ Annotation  │ │   Search    │ │   User      │   │   │
│  │  │   Parser    │ │   Storage   │ │   Index     │ │Preferences  │   │   │
│  │  │   Manager   │ │   Manager   │ │   Manager   │ │   Manager   │   │   │
│  │  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘   │   │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐   │   │
│  │  │ Encryption  │ │    Cache    │ │    File     │ │   Backup    │   │   │
│  │  │   Service   │ │   Manager   │ │   System    │ │   Manager   │   │   │
│  │  │             │ │             │ │   Access    │ │             │   │   │
│  │  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘   │   │
│  └─────────────────────────────────────────────────────────────────────┘   │
│                                    │                                        │
│  ┌─────────────────────────────────────────────────────────────────────┐   │
│  │                       Storage Layer                                │   │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐   │   │
│  │  │   SQLite    │ │    File     │ │   Secure    │ │    Cache    │   │   │
│  │  │  Database   │ │   System    │ │   Storage   │ │   Storage   │   │   │
│  │  │ (Encrypted) │ │  (Documents)│ │ (Keychain)  │ │ (Temporary) │   │   │
│  │  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘   │   │
│  └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

## Layer-by-Layer Architecture Description

### 1. Presentation Layer

**Purpose**: User interface and user experience components

#### React Native Components

- **Core Framework**: React Native 0.72+ with TypeScript
- **Component Library**: Custom components built on Material Design 3
- **State Management**: Redux Toolkit with RTK Query for data fetching
- **Navigation**: React Navigation 6 with type-safe navigation

#### Material Design 3 Components

- **Design System**: Complete MD3 component library implementation
- **Theming**: Dynamic color theming with Material You integration
- **Accessibility**: WCAG 2.1 AA compliant components
- **Responsive Design**: Adaptive layouts for all screen sizes

#### Navigation System

- **Bottom Navigation**: Primary navigation for phones
- **Navigation Drawer**: Secondary navigation and organization
- **Navigation Rail**: Tablet-optimized navigation
- **Deep Linking**: URL-based navigation for document access

#### Responsive Layout Manager

- **Breakpoint System**: Compact, medium, expanded layouts
- **Adaptive Components**: Screen-size aware component rendering
- **Orientation Handling**: Portrait and landscape optimizations
- **Foldable Support**: Dual-screen and fold-aware layouts

### 2. Business Logic Layer

**Purpose**: Core application logic and feature implementation

#### Document Reading Engine

- **Multi-Format Support**: 9 document formats with unified interface
- **Rendering Engine**: Optimized document rendering and display
- **Navigation Controller**: Chapter, bookmark, and progress management
- **Split-Screen Manager**: Dual-document viewing for tablets

#### Annotation System

- **Highlight Manager**: Color-coded text highlighting
- **Note System**: Rich text notes with attachment support
- **Annotation Storage**: Encrypted local annotation persistence
- **Cross-Format Sync**: Consistent annotations across document types

#### Search & Discovery

- **Full-Text Search**: SQLite FTS5 powered search
- **Semantic Search**: AI-powered contextual search
- **Index Manager**: Incremental search index updates
- **Result Ranking**: Hybrid relevance scoring

#### AI Services Integration

- **Handwriting Recognition**: ViT + mT5 model coordination
- **Text Summarization**: T5-based document summarization
- **Language Detection**: Automatic language identification
- **Model Lifecycle**: Efficient AI model loading and management

### 3. AI/ML Layer

**Purpose**: Offline artificial intelligence and machine learning capabilities

#### Vision Transformer (ViT)

- **Model Size**: 25MB quantized model
- **Input Processing**: 224x224 image patch processing
- **Feature Extraction**: 768-dimensional feature vectors
- **Attention Mechanism**: Multi-head self-attention for spatial relationships

#### mT5 Multilingual Model

- **Model Size**: 60MB quantized model
- **Language Support**: English, Chinese, French with expansion capability
- **Text Generation**: Encoder-decoder architecture for text generation
- **Tokenization**: SentencePiece with 250K vocabulary

#### T5 Summarization Model

- **Model Size**: 60MB quantized model
- **Summarization Types**: Extractive and abstractive summaries
- **Context Awareness**: Document structure preservation
- **Length Control**: Configurable summary lengths

#### TensorFlow Lite Runtime

- **Inference Engine**: Optimized mobile inference
- **Hardware Acceleration**: GPU/NPU acceleration where available
- **Memory Management**: Efficient model memory usage
- **Quantization**: INT8 quantization for size optimization

### 4. Data Access Layer

**Purpose**: Data management, storage, and retrieval services

#### Document Parser Manager

- **Format Parsers**: Specialized parsers for each document format
- **Metadata Extraction**: Document information and structure extraction
- **Content Processing**: Text and image extraction from documents
- **Error Handling**: Robust parsing with fallback mechanisms

#### Storage Managers

- **Annotation Storage**: Encrypted annotation persistence
- **Search Index**: Full-text search index management
- **User Preferences**: Settings and configuration storage
- **Cache Management**: Intelligent caching for performance

#### Security Services

- **Encryption Service**: AES-256 encryption for sensitive data
- **Key Management**: Secure key derivation and storage
- **Access Control**: Permission-based data access
- **Audit Logging**: Security event logging

### 5. Storage Layer

**Purpose**: Physical data storage and persistence

#### SQLite Database (Encrypted)

- **Schema Design**: Optimized database schema for reading app
- **Encryption**: Transparent database encryption
- **Indexing**: Performance-optimized indexes
- **Backup**: Automated local backup mechanisms

#### File System Storage

- **Document Storage**: Organized document file storage
- **Asset Management**: Image and media file management
- **Temporary Files**: Secure temporary file handling
- **Cleanup**: Automatic cleanup of unused files

#### Secure Storage (Keychain/Keystore)

- **Credential Storage**: Secure storage for sensitive credentials
- **Key Storage**: Encryption key secure storage
- **Biometric Integration**: Biometric authentication support
- **Hardware Security**: Hardware-backed security where available

## Data Flow Architecture

### Document Reading Flow

```
User Selection → Document Parser → Content Extraction → Rendering Engine → UI Display
                      ↓
              Metadata Storage → Search Index → Annotation System
```

### Handwriting Recognition Flow

```
Camera Capture → Image Preprocessing → ViT Feature Extraction → mT5 Text Generation → Result Display
                      ↓                        ↓                       ↓
              Temporary Storage → Model Inference → Text Processing → User Editing
```

### Search and Discovery Flow

```
User Query → Query Processing → Parallel Search (FTS + Semantic) → Result Ranking → UI Display
                ↓                      ↓                ↓
        Query Expansion → Index Search → Vector Search → Result Fusion
```

## Security Architecture Integration

### Privacy-First Design

- **Network Isolation**: No network access permissions
- **Local Processing**: All AI operations on-device
- **Data Encryption**: AES-256 encryption for all sensitive data
- **Audit Trail**: Comprehensive security event logging

### Threat Mitigation

- **Data Exfiltration**: Complete offline operation prevents data leakage
- **Unauthorized Access**: Device-level and app-level authentication
- **Memory Attacks**: Secure memory handling and cleanup
- **Side-Channel Attacks**: Timing attack mitigation

## Performance Architecture

### Optimization Strategies

- **Lazy Loading**: On-demand resource loading
- **Memory Management**: Efficient memory usage patterns
- **Background Processing**: Non-blocking operations
- **Cache Hierarchy**: Multi-level caching strategy

### Scalability Considerations

- **Modular Design**: Loosely coupled components
- **Plugin Architecture**: Extensible feature system
- **Resource Pooling**: Reusable object patterns
- **Horizontal Scaling**: Independent component scaling

## Integration Points

### Platform Integration

- **iOS Integration**: Native iOS APIs and frameworks
- **Android Integration**: Native Android APIs and services
- **Cross-Platform**: Shared business logic with platform-specific UI

### Third-Party Integration

- **TensorFlow Lite**: AI model inference
- **SQLite**: Database operations
- **React Native**: Cross-platform framework
- **Material Design**: UI component system

## Deployment Architecture

### Application Bundle

- **Code Splitting**: Modular code organization
- **Asset Optimization**: Compressed assets and resources
- **Model Bundling**: AI models included in app bundle
- **Platform Optimization**: iOS and Android specific optimizations

### Update Strategy

- **App Store Updates**: Traditional app store update mechanism
- **Hot Updates**: React Native CodePush for non-native updates
- **Model Updates**: Separate AI model update mechanism
- **Backward Compatibility**: Version compatibility management

---

**Document Version**: 1.0  
**Last Updated**: June 21, 2025  
**Author**: InkSight Architecture Team  
**Reviewers**: Technical Lead, Security Architect, Performance Engineer
