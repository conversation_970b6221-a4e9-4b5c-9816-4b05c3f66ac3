<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>EPUB.js Continuous Example</title>

  <script src="../dist/epub.js"></script>

  <link rel="stylesheet" type="text/css" href="examples.css">

  <style type="text/css">

    .epub-container {
      /* min-width: 320px; */
      /* margin: 0 auto; */
      /* position: relative; */
    }

    .epub-container .epub-view > iframe {
        background: white;
        box-shadow: 0 0 4px #ccc;
        /*margin: 10px;
        padding: 20px;*/
    }

    #viewer {
      width: 600px;
      height: 100vh;
      /* overflow: auto; */
      margin: 0 auto;
    }

  </style>
</head>
<body>
  <div id="viewer"></div>

  <script>
    var params = URLSearchParams && new URLSearchParams(document.location.search.substring(1));
    var url = params && params.get("url") && decodeURIComponent(params.get("url"));
    var currentSectionIndex = (params && params.get("loc")) ? params.get("loc") : undefined;

    // Load the opf
    var book = ePub(url || "https://s3.amazonaws.com/epubjs/books/moby-dick/OPS/package.opf");
    var rendition = book.renderTo("viewer", {
        manager: "continuous",
        flow: "scrolled",
        width: "100%",
        height: "100%"
      });
    var displayed = rendition.display(currentSectionIndex);


    displayed.then(function(renderer){
      // -- do stuff
    });

    // Navigation loaded
    book.loaded.navigation.then(function(toc){
      // console.log(toc);
    });


  </script>

</body>
</html>
