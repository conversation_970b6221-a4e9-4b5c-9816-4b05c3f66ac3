{"name": "epubjs", "version": "0.3.0", "authors": ["<PERSON> <<EMAIL>>"], "description": "Enhanced eBooks in the browser.", "main": "dist/epub.js", "moduleType": ["amd", "globals", "node"], "keywords": ["epub"], "license": "MIT", "homepage": "http://futurepress.org", "ignore": ["**/.*", "node_modules", "bower_components", "test", "tools", "books", "examples"], "dependencies": {"event-emitter": "^0.3.5", "jszip": "^3.4.0", "localforage": "^1.7.3", "lodash": "^4.17.15", "marks-pane": "^1.0.9", "path-webpack": "0.0.3", "stream-browserify": "^3.0.0", "url-polyfill": "^1.1.9", "xmldom": "^0.3.0"}}