# Responsive Design Patterns - InkSight

## Overview

InkSight's responsive design system adapts seamlessly across phones, tablets, and foldable devices using Material Design 3 principles, ensuring optimal reading and note-taking experiences regardless of screen size or orientation.

## Breakpoint System

### Material Design 3 Breakpoints

```typescript
interface ResponsiveBreakpoints {
  compact: {
    range: "0-599dp";
    description: "Phones in portrait, small tablets in portrait";
    primaryUse: "Single-column layouts, bottom navigation";
  };
  medium: {
    range: "600-839dp";
    description: "Large phones in landscape, small tablets";
    primaryUse: "Two-column layouts, navigation drawer";
  };
  expanded: {
    range: "840dp+";
    description: "Large tablets, desktop, foldables unfolded";
    primaryUse: "Multi-column layouts, navigation rail";
  };
}
```

### InkSight-Specific Breakpoints

```typescript
interface InkSightBreakpoints {
  // Reading-optimized breakpoints
  singleColumn: "0-599dp"; // Single document view
  dualPane: "600-959dp"; // Document + sidebar
  multiPane: "960dp+"; // Multiple documents + navigation

  // Handwriting capture breakpoints
  compactCapture: "0-479dp"; // Basic capture interface
  enhancedCapture: "480dp+"; // Advanced capture with preview
}
```

## Layout Patterns by Screen Size

### Compact Layout (Phones)

#### Navigation Pattern

```typescript
interface CompactNavigation {
  primary: "bottom_navigation";
  secondary: "modal_drawer";
  contextual: "floating_action_button";

  bottomNavigation: {
    destinations: 4; // Library, Reading, Notes, Search
    labelBehavior: "selected_only";
    height: "80dp";
  };

  modalDrawer: {
    width: "280dp";
    overlay: true;
    swipeGesture: true;
  };
}
```

#### Reading Layout

```typescript
interface CompactReadingLayout {
  orientation: {
    portrait: {
      appBar: "center_aligned";
      content: "full_width";
      controls: "overlay_bottom";
      fab: "bottom_right";
    };
    landscape: {
      appBar: "hidden_on_scroll";
      content: "full_screen";
      controls: "gesture_based";
      fab: "hidden";
    };
  };

  margins: {
    horizontal: "16dp";
    vertical: "8dp";
  };
}
```

#### Document Library

```typescript
interface CompactLibraryLayout {
  listType: "single_column";
  cardSize: "compact";
  itemHeight: "72dp";

  gridFallback: {
    columns: 2;
    aspectRatio: "3:4";
    spacing: "8dp";
  };
}
```

### Medium Layout (Small Tablets)

#### Navigation Pattern

```typescript
interface MediumNavigation {
  primary: "bottom_navigation" | "navigation_drawer";
  secondary: "standard_drawer";
  contextual: "extended_fab";

  adaptiveNavigation: {
    portrait: "bottom_navigation";
    landscape: "navigation_drawer";
  };

  standardDrawer: {
    width: "360dp";
    persistent: true;
    collapsible: true;
  };
}
```

#### Split-Screen Reading

```typescript
interface MediumSplitLayout {
  splitRatio: {
    default: "60:40"; // Document:Sidebar
    reading: "70:30"; // More space for content
    notes: "50:50"; // Equal space for notes
  };

  splitOrientation: {
    portrait: "horizontal_split";
    landscape: "vertical_split";
  };

  resizable: true;
  minimumPaneWidth: "280dp";
}
```

#### Enhanced Library

```typescript
interface MediumLibraryLayout {
  listType: "two_column_grid";
  cardSize: "standard";

  gridConfiguration: {
    columns: 3;
    aspectRatio: "3:4";
    spacing: "12dp";
  };

  listFallback: {
    itemHeight: "88dp";
    thumbnail: true;
    metadata: "extended";
  };
}
```

### Expanded Layout (Large Tablets)

#### Navigation Pattern

```typescript
interface ExpandedNavigation {
  primary: "navigation_rail";
  secondary: "permanent_drawer";
  contextual: "multiple_fabs";

  navigationRail: {
    width: "80dp";
    position: "left";
    destinations: 5;
    labelBehavior: "always_show";
  };

  permanentDrawer: {
    width: "360dp";
    position: "left";
    collapsible: false;
    overlay: false;
  };
}
```

#### Multi-Pane Reading

```typescript
interface ExpandedMultiPaneLayout {
  paneConfiguration: {
    threePane: {
      navigation: "280dp";
      primary: "flex_1";
      secondary: "320dp";
    };
    twoPane: {
      primary: "flex_2";
      secondary: "flex_1";
    };
  };

  readingModes: {
    focus: "single_pane_centered";
    research: "dual_document_view";
    annotation: "document_plus_notes";
  };
}
```

#### Advanced Library

```typescript
interface ExpandedLibraryLayout {
  listType: "multi_column_grid";
  cardSize: "detailed";

  gridConfiguration: {
    columns: 4;
    aspectRatio: "3:4";
    spacing: "16dp";
  };

  tableView: {
    columns: ["title", "author", "date", "size", "type"];
    sortable: true;
    filterable: true;
  };
}
```

## Foldable Device Support

### Fold-Aware Layouts

```typescript
interface FoldableSupport {
  foldStates: {
    folded: "compact_layout";
    halfOpen: "dual_screen_layout";
    unfolded: "expanded_layout";
  };

  foldableFeatures: {
    continuity: true; // Seamless transition between states
    dualScreen: true; // Utilize both screens when half-open
    hinge: "content_aware"; // Avoid placing content on hinge
  };
}
```

### Dual-Screen Patterns

```typescript
interface DualScreenPatterns {
  readingMode: {
    primary: "document_content";
    secondary: "notes_and_annotations";
    synchronization: "scroll_linked";
  };

  libraryMode: {
    primary: "document_grid";
    secondary: "preview_and_metadata";
    interaction: "tap_to_preview";
  };

  captureMode: {
    primary: "camera_viewfinder";
    secondary: "recognition_results";
    workflow: "real_time_processing";
  };
}
```

## Adaptive Component Behavior

### Navigation Adaptation

```typescript
interface AdaptiveNavigation {
  // Component selection based on screen size
  selectNavigationComponent(screenSize: ScreenSize): NavigationComponent;

  // Transition between navigation types
  transitionNavigation(
    from: NavigationComponent,
    to: NavigationComponent
  ): void;

  // Preserve navigation state across size changes
  preserveNavigationState(): NavigationState;
}
```

### Content Adaptation

```typescript
interface AdaptiveContent {
  // Text scaling and layout
  textAdaptation: {
    compact: { scale: 0.9; lineHeight: 1.4 };
    medium: { scale: 1.0; lineHeight: 1.5 };
    expanded: { scale: 1.1; lineHeight: 1.6 };
  };

  // Image and media scaling
  mediaAdaptation: {
    compact: { maxWidth: "100%"; quality: "standard" };
    medium: { maxWidth: "80%"; quality: "high" };
    expanded: { maxWidth: "60%"; quality: "high" };
  };
}
```

### UI Density Adaptation

```typescript
interface DensityAdaptation {
  compact: {
    density: "comfortable";
    spacing: "standard";
    touchTargets: "48dp_minimum";
  };

  medium: {
    density: "comfortable";
    spacing: "relaxed";
    touchTargets: "48dp_preferred";
  };

  expanded: {
    density: "compact";
    spacing: "relaxed";
    touchTargets: "40dp_minimum";
  };
}
```

## Orientation Handling

### Portrait Orientation

```typescript
interface PortraitLayout {
  navigation: "bottom_navigation";
  content: "single_column";
  sidebar: "modal_overlay";

  readingOptimization: {
    textWidth: "optimal_line_length";
    margins: "comfortable";
    scrollDirection: "vertical";
  };
}
```

### Landscape Orientation

```typescript
interface LandscapeLayout {
  navigation: "side_navigation";
  content: "multi_column";
  sidebar: "persistent_panel";

  readingOptimization: {
    textWidth: "constrained_for_readability";
    margins: "expanded";
    scrollDirection: "horizontal_optional";
  };
}
```

## Performance Optimization

### Layout Efficiency

```typescript
interface LayoutPerformance {
  // Lazy loading for large screens
  lazyLoading: {
    enabled: true;
    threshold: "2_screens_ahead";
    placeholder: "skeleton_loading";
  };

  // Efficient re-layouts
  layoutOptimization: {
    batchUpdates: true;
    debounceResize: 300; // milliseconds
    useLayoutCache: true;
  };

  // Memory management
  memoryOptimization: {
    recycleViews: true;
    limitConcurrentLayouts: 3;
    cleanupOffscreen: true;
  };
}
```

### Responsive Image Loading

```typescript
interface ResponsiveImages {
  // Different image sizes for different screens
  imageSizes: {
    compact: { width: 360; quality: 0.8 };
    medium: { width: 720; quality: 0.9 };
    expanded: { width: 1080; quality: 1.0 };
  };

  // Adaptive loading strategy
  loadingStrategy: {
    preload: "critical_images_only";
    lazy: "below_fold_images";
    progressive: "large_images";
  };
}
```

## Testing Strategy

### Responsive Testing Framework

```typescript
interface ResponsiveTestSuite {
  // Screen size testing
  testScreenSizes: ScreenSize[];

  // Orientation testing
  testOrientations: ["portrait", "landscape"];

  // Foldable testing
  testFoldStates: ["folded", "half_open", "unfolded"];

  // Performance testing
  testLayoutPerformance(screenSize: ScreenSize): PerformanceMetrics;

  // Visual regression testing
  captureLayoutScreenshots(configuration: TestConfiguration): Screenshot[];
}
```

### Device Testing Matrix

```typescript
interface DeviceTestMatrix {
  phones: [
    { name: "iPhone SE"; size: "compact"; orientation: "both" },
    { name: "iPhone 14"; size: "compact"; orientation: "both" },
    { name: "Pixel 7"; size: "compact"; orientation: "both" }
  ];

  tablets: [
    { name: "iPad Mini"; size: "medium"; orientation: "both" },
    { name: "iPad Air"; size: "expanded"; orientation: "both" },
    { name: "Galaxy Tab S8"; size: "expanded"; orientation: "both" }
  ];

  foldables: [
    { name: "Galaxy Z Fold"; states: "all"; orientation: "both" },
    { name: "Surface Duo"; states: "all"; orientation: "both" }
  ];
}
```

## Implementation Guidelines

### Responsive Component Architecture

```typescript
interface ResponsiveComponent {
  // Breakpoint-aware rendering
  render(breakpoint: Breakpoint): ReactElement;

  // Adaptive props based on screen size
  getAdaptiveProps(screenSize: ScreenSize): ComponentProps;

  // Layout transition handling
  handleLayoutTransition(from: Layout, to: Layout): void;
}
```

### CSS-in-JS Responsive Patterns

```typescript
interface ResponsiveStyles {
  // Breakpoint-based styles
  styles: {
    compact: StyleSheet;
    medium: StyleSheet;
    expanded: StyleSheet;
  };

  // Adaptive values
  adaptiveValues: {
    spacing: (breakpoint: Breakpoint) => number;
    fontSize: (breakpoint: Breakpoint) => number;
    margins: (breakpoint: Breakpoint) => EdgeInsets;
  };
}
```

---

**Document Version**: 1.0  
**Last Updated**: June 21, 2025  
**Author**: InkSight Responsive Design Team  
**Reviewers**: UX Designer, Frontend Developer, QA Engineer
