<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>EPUB.js Basic Example</title>

  <script src="../dist/epub.js"></script>

  <link rel="stylesheet" type="text/css" href="examples.css">

</head>
<body>
  <div id="viewer" class="scrolled"></div>

  <script>
    var $viewer = document.getElementById("viewer");

    var book = ePub("https://s3.amazonaws.com/epubjs/books/moby-dick/OPS/package.opf");
    book.ready.then(function(){
      book.getRange("epubcfi(/6/14[xchapter_001]!/4/2,/2/2/2[c001s0000]/1:0,/8/2[c001p0003]/1:663)").then(function(range) {
        let text = range.toString()
        console.log(text);
        $viewer.textContent = text;
      });
    });
  </script>

</body>
</html>
